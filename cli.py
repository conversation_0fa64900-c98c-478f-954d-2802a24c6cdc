"""
EcoCycle - Command Line Interface Module
Provides a command-line interface for interacting with EcoCycle features.
"""
import json
import logging
import os
import sys
import argparse
import datetime
import subprocess
import importlib.util
from typing import Dict, List
import core.database_manager as database_manager
from core.cli_core import C<PERSON><PERSON><PERSON>, ConfigManager
from core.argument_parser import create_cli_parser
from core.command_handlers import Base<PERSON>ommandHandler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('Logs/ecocycle_cli.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# Constants
CONFIG_FILE = ".ecocycle.config"
VERSION = "3.4.0"
MAX_LOG_SIZE_MB = 10
REQUIRED_DEPENDENCIES = ['colorama']
OPTIONAL_DEPENDENCIES = [
    'requests', 'folium', 'matplotlib', 'numpy', 'plotly',
    'tabulate', 'pillow', 'qrcode', 'fpdf', 'tqdm',
    'python-dotenv', 'google-api-python-client', 'sendgrid', 'twilio'
]
DATA_FILES = [
    'data/user/users.json',
    'config/preferences/notification_settings.json',
    'Logs/notification_logs.json'
]
LOG_FILES = ['ecocycle.log', 'ecocycle_debug.log', 'ecocycle_web.log', 'ecocycle_cli.log']


def display_version():
    """Display the application version."""
    print(f"EcoCycle version {VERSION}")
    print("Cycle into a greener tomorrow")
    print("Copyright 2025")


# Removed duplicate config functions - now using ConfigManager from core.cli_core


def _get_import_name(package: str) -> str:
    """Get the correct import name for a package."""
    if package == 'google-api-python-client':
        return 'googleapiclient'
    return package.replace('-', '_')


def verify_dependencies(required_only: bool = False) -> List[str]:
    """
    Verify that all dependencies are installed.
    Returns a list of missing dependencies.
    """
    # Check which packages to verify
    to_check = REQUIRED_DEPENDENCIES if required_only else REQUIRED_DEPENDENCIES + OPTIONAL_DEPENDENCIES

    # Check each package
    missing = []
    for package in to_check:
        import_name = _get_import_name(package)
        if importlib.util.find_spec(import_name) is None:
            missing.append(package)

    return missing


def _install_packages(packages: List[str]) -> bool:
    """Install packages using pip."""
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + packages)
        return True
    except subprocess.CalledProcessError:
        return False


def run_doctor(fix_issues=False):
    """
    Run comprehensive system diagnostics and fix common issues.
    Enhanced with Rich UI and comprehensive health checks.

    Args:
        fix_issues (bool): Whether to attempt automatic fixes for detected issues
    """
    # Import enhanced doctor functionality
    try:
        from utils.doctor_ui import EnhancedDoctorUI
        doctor_ui = EnhancedDoctorUI()
        doctor_ui.run_comprehensive_diagnostics(fix_issues=fix_issues)
    except ImportError as e:
        # Fallback to basic implementation if enhanced UI is not available
        print(f"Enhanced doctor UI not available ({e}), using basic implementation...")
        _run_basic_doctor(fix_issues=fix_issues)
    except Exception as e:
        # Fallback for any other errors
        print(f"Error with enhanced doctor UI ({e}), using basic implementation...")
        _run_basic_doctor(fix_issues=fix_issues)


def _check_python_version():
    """Check Python version and display warning if needed."""
    print(f"\nPython Version: {sys.version}")
    python_version = tuple(map(int, sys.version.split()[0].split('.')))
    if python_version < (3, 7):
        print("WARNING: Python 3.7 or newer is recommended")


def _check_and_install_dependencies(fix_issues: bool = False):
    """Check dependencies and optionally install missing ones."""
    print("\nChecking dependencies...")
    missing_required = verify_dependencies(required_only=True)
    missing_optional = [pkg for pkg in verify_dependencies() if pkg not in missing_required]

    # Handle required dependencies
    if missing_required:
        print(f"CRITICAL: Missing required dependencies: {', '.join(missing_required)}")
        print("These packages are required for basic functionality.")

        should_install = fix_issues or input("Would you like to install them now? (y/n): ").lower() == 'y'
        if should_install:
            if _install_packages(missing_required):
                print("Required dependencies installed successfully.")
            else:
                print("Error installing required dependencies. Please install manually.")
    else:
        print("All required dependencies are installed.")

    # Handle optional dependencies
    if missing_optional:
        print(f"\nMissing optional dependencies: {', '.join(missing_optional)}")
        print("These packages enable additional features but are not required.")

        should_install = fix_issues or input("Would you like to install them now? (y/n): ").lower() == 'y'
        if should_install:
            if _install_packages(missing_optional):
                print("Optional dependencies installed successfully.")
            else:
                print("Error installing optional dependencies. Please install manually.")
    else:
        print("All optional dependencies are installed.")


def _run_basic_doctor(fix_issues=False):
    """
    Fallback basic doctor implementation for when enhanced UI is not available.

    Args:
        fix_issues (bool): Whether to attempt automatic fixes for detected issues
    """
    print("EcoCycle Doctor - System Diagnostics")
    print("====================================")

    _check_python_version()
    _check_and_install_dependencies(fix_issues)

    _check_configuration()
    _check_data_files()
    _check_log_files(fix_issues)
    _check_for_updates()

    print("\nDiagnostics completed.")


def _check_configuration():
    """Check configuration files."""
    print("\nChecking configuration...")
    config_manager = ConfigManager()
    config = config_manager.load_config()
    if not config:
        print("No configuration found. A new one will be created when needed.")
    else:
        print("Configuration file found.")

        # Check for common configuration issues
        if 'user_preferences' not in config:
            print("WARNING: user_preferences section is missing from configuration")

        if 'last_sync' in config and isinstance(config['last_sync'], (int, float)):
            last_sync = datetime.datetime.fromtimestamp(config['last_sync'])
            print(f"Last data synchronization: {last_sync}")


def _check_data_files():
    """Check data files for validity."""
    print("\nChecking data files...")
    for file in DATA_FILES:
        if os.path.exists(file):
            try:
                with open(file, 'r') as f:
                    json.load(f)
                print(f"✓ {file} exists and is valid JSON")
            except json.JSONDecodeError:
                print(f"✗ {file} exists but contains invalid JSON")
        else:
            print(f"✗ {file} does not exist (will be created when needed)")


def _check_log_files(fix_issues: bool = False):
    """Check log files and optionally rotate large ones."""
    print("\nChecking log files...")
    for file in LOG_FILES:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✓ {file} exists ({size} bytes)")
            if size > MAX_LOG_SIZE_MB * 1024 * 1024:
                print(f"  WARNING: Log file {file} is very large ({size // (1024*1024)} MB)")
                should_rotate = fix_issues or input(f"  Would you like to rotate {file}? (y/n): ").lower() == 'y'
                if should_rotate:
                    backup = f"{file}.{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
                    try:
                        os.rename(file, backup)
                        print(f"  Log file rotated to {backup}")
                    except Exception as e:
                        print(f"  Error rotating log file: {e}")
        else:
            print(f"✗ {file} does not exist (will be created when needed)")


def _check_for_updates():
    """Check for application updates."""
    print("\nChecking for updates...")
    try:
        # Use packaging version comparison if available
        import packaging.version

        # In a real application, this would make an API call to check for updates
        # This is just a placeholder
        current_version = packaging.version.parse(VERSION)
        latest_version = packaging.version.parse(VERSION)  # Would get from API

        if latest_version > current_version:
            print(f"A new version is available: {latest_version} (current: {current_version})")
            print("Run 'python -m ecocycle update' to update.")
        else:
            print(f"You have the latest version ({VERSION}).")
    except ImportError:
        print("Could not check for updates (packaging module not installed).")


def run_update():
    """
    Update the application to the latest version.
    """
    print("EcoCycle Update")
    print("==============")

    print("Checking for updates...")

    # In a real application, this would check for updates and update if needed
    # This is just a placeholder
    print(f"Current version: {VERSION}")
    print(f"Latest version: {VERSION}")
    print("You have the latest version.")
    print("\nNo update required.")


# Removed duplicate handle_log_command - now using shared BaseCommandHandler


# Removed duplicate handle_stats_command - now using shared BaseCommandHandler


# Removed duplicate handle_weather_command - now using shared BaseCommandHandler


def handle_routes_command(args):
    """
    Handle the 'routes' command to manage cycling routes.
    """
    # Import necessary modules
    try:
        import main
        import apps.route_planner.weather_route_planner as weather_route_planner

        # Initialize modules
        user_manager = main.import_local_modules()['user_manager'].UserManager()
        weather_planner = weather_route_planner.WeatherRoutePlanner(user_manager)

        if args.action == "plan":
            print("Planning a cycling route...")
            weather_planner.plan_route()
        elif args.action == "list":
            print("Listing saved routes...")
            weather_planner.view_saved_routes()
        elif args.action == "impact":
            print("Calculating cycling impact...")
            weather_planner.cycling_impact_calculator()
        else:
            print("Invalid action. Use 'plan', 'list', or 'impact'.")

    except ImportError as e:
        print(f"Error: Required modules not available - {e}")
        print("Please run 'python -m ecocycle doctor' to diagnose.")
    except Exception as e:
        print(f"Error managing routes: {e}")


# Removed duplicate handle_config_command - now using shared BaseCommandHandler


# Removed duplicate handle_export_command and export helper functions - now using shared BaseCommandHandler


def handle_social_command(args: argparse.Namespace):
    """
    Handle the 'social' command to manage social features.

    Args:
        args: Command line arguments (currently unused but required for consistency)
    """
    # Mark args as used to avoid linting warnings
    del args
    # Import necessary modules
    try:
        import main
        import apps.gamification.social_gamification as social_gamification

        # Initialize modules
        user_manager = main.import_local_modules()['user_manager'].UserManager()

        # Check if user is authenticated
        if not user_manager.is_authenticated() or user_manager.is_guest():
            print("You need to be logged in (with a registered account) to use social features.")
            print("Please run 'python main.py' and log in first.")
            return

        # Run the social gamification module
        social_gamification.run_social_features(user_manager)

    except ImportError as e:
        print(f"Error: Required modules not available - {e}")
        print("Please run 'python -m ecocycle doctor' to diagnose.")
    except Exception as e:
        print(f"Error with social features: {e}")


def handle_notifications_command(args: argparse.Namespace):
    """
    Handle the 'notifications' command to manage notifications.

    Args:
        args: Command line arguments (currently unused but required for consistency)
    """
    # Mark args as used to avoid linting warnings
    del args
    # Import necessary modules
    try:
        import main
        import services.notifications.notification_system as notification_system

        # Initialize modules
        user_manager = main.import_local_modules()['user_manager'].UserManager()

        # Check if user is authenticated
        if not user_manager.is_authenticated():
            print("You need to be logged in to manage notifications.")
            print("Please run 'python main.py' and log in first.")
            return

        # Run the notification system module
        notification_system.run_notification_manager(user_manager)

    except ImportError as e:
        print(f"Error: Required modules not available - {e}")
        print("Please run 'python -m ecocycle doctor' to diagnose. ")
    except Exception as e:
        print(f"Error with notifications: {e}")


# Removed duplicate _setup_argument_parser - now using shared argument parser from core.argument_parser


def main():
    """Main entry point for the CLI."""
    # Use shared argument parser
    argument_parser = create_cli_parser()
    args = argument_parser.parse_args()

    # Initialize CLI core and command handler
    cli_core = CLICore()
    command_handler = BaseCommandHandler(cli_core)

    # Handle commands
    if args.command == 'run' or args.command is None:
        # Run the main program
        import main
        main.main()
    elif args.command == 'log':
        command_handler.handle_log_command(args)
    elif args.command == 'stats':
        command_handler.handle_stats_command(args)
    elif args.command == 'weather':
        command_handler.handle_weather_command(args)
    elif args.command == 'routes':
        handle_routes_command(args)  # Keep CLI-specific routes handler
    elif args.command == 'config':
        command_handler.handle_config_command(args)
    elif args.command == 'export':
        command_handler.handle_export_command(args)
    elif args.command == 'update':
        run_update()
    elif args.command == 'doctor':
        run_doctor(fix_issues=getattr(args, 'fix', False))
    elif args.command == 'version':
        display_version()
    elif args.command == 'social':
        handle_social_command(args)  # Keep CLI-specific social handler
    elif args.command == 'notifications':
        handle_notifications_command(args)  # Keep CLI-specific notifications handler
    else:
        argument_parser.print_help()


if __name__ == "__main__":
    main()
