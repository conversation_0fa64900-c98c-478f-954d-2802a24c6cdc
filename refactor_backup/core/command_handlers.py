"""
EcoCycle - Shared Command Handlers Module
Common command handling functionality for both main.py and cli.py.
"""
import os
import sys
import json
import datetime
import logging
from typing import Any, Dict, Optional
import argparse

from core.cli_core import CLICore, ConfigManager
import core.database_manager as database_manager
from utils.app_functions import import_local_modules, view_statistics


class BaseCommandHandler:
    """Base class for command handlers with shared functionality."""

    def __init__(self, cli_core: Optional[CLICore] = None):
        """Initialize command handler."""
        self.cli_core = cli_core or CLICore()
        self.config_manager = ConfigManager()
        self.logger = logging.getLogger(__name__)

    def handle_log_command(self, args: argparse.Namespace) -> bool:
        """
        Handle the 'log' command to log a cycling trip.

        Returns:
            True if successful, False otherwise
        """
        try:
            return self.cli_core.handle_trip_logging(
                date=args.date,
                distance=args.distance,
                duration=args.duration,
                auto_save=getattr(args, 'yes', False)
            )
        except Exception as e:
            self.logger.error(f"Error logging trip: {e}", exc_info=True)
            print(f"Error logging trip: {e}")
            return False

    def handle_stats_command(self, args: argparse.Namespace) -> bool:
        """
        Handle the 'stats' command to view cycling statistics.

        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.cli_core.ensure_authentication("view statistics"):
                return False

            # Check for admin-specific functionality (main.py)
            if hasattr(args, 'user') and args.user:
                if self.cli_core.user_manager.is_admin():
                    print(f"Viewing stats for user: {args.user}")
                    # TODO: Implement logic to show stats for specific user
                    print("Viewing stats for specific user not yet fully implemented.")
                else:
                    print("Admin privileges required to view stats for other users.")
                    print("Showing stats for the current user instead.")
                    view_statistics(self.cli_core.user_manager)
            else:
                # Standard stats viewing
                if hasattr(args, 'summary') and args.summary:
                    self._show_summary_stats()
                else:
                    view_statistics(self.cli_core.user_manager)

                # Handle visualization flag (CLI only)
                if hasattr(args, 'visualize') and args.visualize:
                    self._handle_visualization()

            return True

        except Exception as e:
            self.logger.error(f"Error viewing statistics: {e}", exc_info=True)
            print(f"Error viewing statistics: {e}")
            return False

    def handle_weather_command(self, args: argparse.Namespace) -> bool:
        """
        Handle the 'weather' command to check weather for cycling.

        Returns:
            True if successful, False otherwise
        """
        try:
            from apps.route_planner.weather_route_planner import WeatherRoutePlanner, check_weather, run_planner

            if args.location:
                # Pass None for user_manager if location is specified
                check_weather(None, location_override=args.location)
            else:
                # Initialize user manager if not already done
                if not self.cli_core.user_manager:
                    self.cli_core.initialize_managers()

                # Use the weather route planner
                weather_planner = WeatherRoutePlanner(self.cli_core.user_manager)
                weather_planner.check_weather()

            return True

        except ImportError:
            print("Weather and route planning module not available.")
            print("Please install required dependencies with 'python -m ecocycle doctor'.")
            return False
        except Exception as e:
            self.logger.error(f"Error running weather command: {e}", exc_info=True)
            print(f"Error checking weather: {e}")
            return False

    def handle_config_command(self, args: argparse.Namespace) -> bool:
        """
        Handle the 'config' command to configure application settings.

        Returns:
            True if successful, False otherwise
        """
        try:
            # For user preferences, require authentication
            if args.set or args.get or args.list:
                if not self.cli_core.ensure_authentication("manage configuration"):
                    return False

                return self._handle_user_preferences(args)
            else:
                # Handle basic config file operations
                return self._handle_config_file_operations(args)

        except Exception as e:
            self.logger.error(f"Error handling config command: {e}", exc_info=True)
            print(f"Error managing configuration: {e}")
            return False

    def handle_export_command(self, args: argparse.Namespace) -> bool:
        """
        Handle the 'export' command to export cycling data.

        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.cli_core.ensure_authentication("export data"):
                return False

            # Get user data
            user = self.cli_core.user_manager.get_current_user()
            stats = user.get('stats', {})

            if not stats or not stats.get('total_trips', 0):
                print("No cycling data to export.")
                return False

            # Determine output file path
            output_path = args.output
            if not output_path:
                date_str = datetime.datetime.now().strftime("%Y%m%d")
                username = user.get('username', 'unknown')
                output_path = f"ecocycle_export_{username}_{date_str}.{args.format}"

            # Export data based on format
            if args.format == 'csv':
                return self._export_to_csv(user, output_path)
            elif args.format == 'json':
                return self._export_to_json(user, output_path)
            elif args.format == 'pdf':
                return self._export_to_pdf(user, output_path)
            else:
                print(f"Unsupported format: {args.format}")
                return False

        except Exception as e:
            self.logger.error(f"Error exporting data: {e}", exc_info=True)
            print(f"Error exporting data: {e}")
            return False

    def _show_summary_stats(self):
        """Show summary statistics only."""
        user = self.cli_core.user_manager.get_current_user()
        stats = user.get('stats', {})

        if not stats or not stats.get('total_trips', 0):
            print("No cycling data recorded yet.")
            return

        name = user.get('name', user.get('username', 'Unknown'))
        print(f"Statistics for: {name}")

        total_trips = stats.get('total_trips', 0)
        total_distance = stats.get('total_distance', 0.0)
        total_co2_saved = stats.get('total_co2_saved', 0.0)
        total_calories = stats.get('total_calories', 0)

        print(f"Total Trips: {total_trips}")
        print(f"Total Distance: {total_distance:.1f} km")
        print(f"Total CO2 Saved: {total_co2_saved:.2f} kg")
        print(f"Total Calories Burned: {total_calories}")

    def _handle_visualization(self):
        """Handle data visualization request."""
        try:
            from apps.data_viz import DataVisualization
            print("\nStarting data visualization...")
            data_viz = DataVisualization(self.cli_core.user_manager)
            data_viz.run_visualization()
        except ImportError:
            print("\nData visualization module not available.")
            print("Please install required dependencies with 'python -m ecocycle doctor'.")

    def _handle_user_preferences(self, args: argparse.Namespace) -> bool:
        """Handle user preference operations."""
        modules = import_local_modules()
        ascii_art = modules['ascii_art']

        if args.set:
            key, value = args.set
            if self.cli_core.user_manager.update_user_preference(key, value):
                ascii_art.display_success_message(f"Configuration value '{key}' set to '{value}'")
                return True
            else:
                ascii_art.display_error_message(f"Failed to set configuration value '{key}'.")
                return False

        elif args.get:
            value = self.cli_core.user_manager.get_user_preference(args.get)
            if value is not None:
                print(f"{args.get}: {value}")
                return True
            else:
                print(f"Configuration key '{args.get}' not found.")
                return False

        elif args.list:
            user = self.cli_core.user_manager.get_current_user()
            preferences = user.get('preferences', {})

            if preferences:
                print("\nCurrent Configuration values:")
                headers = ["Key", "Value"]
                data = [[key, str(value)] for key, value in preferences.items()]
                ascii_art.display_data_table(headers, data)
            else:
                print("No configuration values set.")
            return True

        return False

    def _handle_config_file_operations(self, args: argparse.Namespace) -> bool:
        """Handle basic config file operations."""
        config = self.config_manager.load_config()

        if args.list:
            print("Current Configuration:")
            print("=====================")
            if config:
                print(json.dumps(config, indent=2))
            else:
                print("Configuration is empty.")
            return True

        if args.get:
            key = args.get
            if key in config:
                print(f"{key}: {json.dumps(config[key], indent=2)}")
            else:
                print(f"Key '{key}' not found in configuration.")
            return True

        if args.set:
            key, value = args.set
            # Try to parse value as JSON for numbers, booleans, etc.
            try:
                value = json.loads(value)
            except json.JSONDecodeError:
                # If not valid JSON, treat as string
                pass

            if self.config_manager.set_config_value(key, value):
                print(f"Configuration updated: {key} = {value}")
                return True
            else:
                print("Error saving configuration.")
                return False

        # If no specific action, show help
        print("Use --list to show all configuration values.")
        print("Use --get KEY to show a specific configuration value.")
        print("Use --set KEY VALUE to set a configuration value.")
        return True

    def _export_to_csv(self, user: Dict, output_path: str) -> bool:
        """Export user data to CSV file."""
        import csv

        stats = user.get('stats', {})
        trips = stats.get('trips', [])

        try:
            with open(output_path, 'w', newline='') as f:
                writer = csv.writer(f)

                # Write header
                writer.writerow(['Date', 'Distance (km)', 'Duration (min)', 'CO2 Saved (kg)', 'Calories'])

                # Write trip data
                for trip in trips:
                    date = trip.get('date', '')
                    distance = trip.get('distance', 0.0)
                    duration = trip.get('duration', 0.0)
                    co2_saved = trip.get('co2_saved', 0.0)
                    calories = trip.get('calories', 0)

                    writer.writerow([date, distance, duration, co2_saved, calories])

            print(f"Data exported to CSV: {output_path}")
            return True
        except Exception as e:
            print(f"Error exporting to CSV: {e}")
            return False

    def _export_to_json(self, user: Dict, output_path: str) -> bool:
        """Export user data to JSON file."""
        try:
            # Create a clean export structure
            export_data = {
                'user': {
                    'username': user.get('username', ''),
                    'name': user.get('name', ''),
                    'email': user.get('email', ''),
                    'export_date': datetime.datetime.now().isoformat()
                },
                'stats': user.get('stats', {})
            }

            with open(output_path, 'w') as f:
                json.dump(export_data, f, indent=2)

            print(f"Data exported to JSON: {output_path}")
            return True
        except Exception as e:
            print(f"Error exporting to JSON: {e}")
            return False

    def _export_to_pdf(self, user: Dict, output_path: str) -> bool:
        """Export user data to PDF file."""
        try:
            from fpdf import FPDF
            import utils.general_utils as utils

            username = user.get('username', 'unknown')
            name = user.get('name', username)
            stats = user.get('stats', {})
            trips = stats.get('trips', [])

            # Create PDF
            pdf = FPDF()
            pdf.add_page()

            # Add header
            pdf.set_font("Arial", "B", 16)
            pdf.cell(0, 10, "EcoCycle Data Export", ln=True, align="C")
            pdf.set_font("Arial", "", 12)
            pdf.cell(0, 10, f"User: {name}", ln=True, align="C")
            pdf.cell(0, 10, f"Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}", ln=True, align="C")

            # Add summary
            pdf.ln(10)
            pdf.set_font("Arial", "B", 14)
            pdf.cell(0, 10, "Cycling Summary", ln=True)

            pdf.set_font("Arial", "", 12)
            total_trips = stats.get('total_trips', 0)
            total_distance = stats.get('total_distance', 0.0)
            total_co2_saved = stats.get('total_co2_saved', 0.0)
            total_calories = stats.get('total_calories', 0)

            pdf.cell(0, 8, f"Total Trips: {total_trips}", ln=True)
            pdf.cell(0, 8, f"Total Distance: {utils.format_distance(total_distance)}", ln=True)
            pdf.cell(0, 8, f"Total CO2 Saved: {utils.format_co2(total_co2_saved)}", ln=True)
            pdf.cell(0, 8, f"Total Calories Burned: {utils.format_calories(total_calories)}", ln=True)

            # Add trip table
            if trips:
                pdf.ln(10)
                pdf.set_font("Arial", "B", 14)
                pdf.cell(0, 10, "Trip History", ln=True)

                # Table headers
                pdf.set_font("Arial", "B", 10)
                pdf.cell(40, 10, "Date", border=1)
                pdf.cell(30, 10, "Distance (km)", border=1)
                pdf.cell(30, 10, "Duration (min)", border=1)
                pdf.cell(40, 10, "CO2 Saved (kg)", border=1)
                pdf.cell(40, 10, "Calories", border=1, ln=True)

                # Table rows
                pdf.set_font("Arial", "", 10)
                for trip in trips:
                    date = trip.get('date', '').split('T')[0]  # Extract date part
                    distance = trip.get('distance', 0.0)
                    duration = trip.get('duration', 0.0)
                    co2_saved = trip.get('co2_saved', 0.0)
                    calories = trip.get('calories', 0)

                    pdf.cell(40, 8, date, border=1)
                    pdf.cell(30, 8, f"{distance:.1f}", border=1)
                    pdf.cell(30, 8, f"{duration:.1f}", border=1)
                    pdf.cell(40, 8, f"{co2_saved:.2f}", border=1)
                    pdf.cell(40, 8, f"{calories}", border=1, ln=True)

            # Output the PDF
            pdf.output(output_path)
            print(f"Data exported to PDF: {output_path}")
            return True

        except ImportError:
            print("Error: fpdf library not available.")
            print("Please install it with 'pip install fpdf'.")
            return False
        except Exception as e:
            print(f"Error exporting to PDF: {e}")
            return False
