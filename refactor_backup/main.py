"""
EcoCycle - Main Application Module
A command-line dashboard for cycling and environmental data tracking
with Google Sheets integration.
"""
import os
import sys
import logging
import argparse
import time
import json
import hmac
import signal
import atexit
from auth.user_management.user_manager import SESSION_SECRET_ENV_VAR
import core.database_manager as database_manager
import core.dependency.dependency_manager as dependency_manager
import core.plugin.plugin_loader as plugin_loader
from apps.menu import show_main_menu
from utils.app_functions import import_local_modules, view_statistics
from core.error_handler import handle_error, DatabaseError
from core.cli_core import CLICore
from core.argument_parser import create_main_parser
from core.command_handlers import BaseCommandHandler
from cli import display_version

if 'TERM' not in os.environ:
    os.environ['TERM'] = 'xterm'

import config.config as config

# --- Define Log Directory and File Path ---
# Use the LOG_DIR from config.py
LOG_DIR = config.LOG_DIR
LOG_FILE = os.path.join(LOG_DIR, 'ecocycle.log')

try:
    # Create the directory if it doesn't exist.
    # os.makedirs creates parent directories as needed.
    # exist_ok=True prevents an error if the directory already exists.
    os.makedirs(LOG_DIR, exist_ok=True)
    # Optional: Print a message to confirm (useful for debugging)
    # print(f"Log directory '{LOG_DIR}' checked/created.", file=sys.stderr)
except OSError as e:
    # Handle potential errors during directory creation (e.g., permissions)
    print(f"Error: Could not create log directory '{LOG_DIR}'. {e}", file=sys.stderr)
    # Decide if you want to exit or continue without file logging
    # sys.exit(1) # Uncomment to exit if log dir creation fails
except Exception as e:
    print(f"Unexpected error creating log directory '{LOG_DIR}': {e}", file=sys.stderr)
    # sys.exit(1)

# Configure logger
logger = logging.getLogger(__name__)

# Configure logging
logging.basicConfig(
    level=logging.INFO, # Keep overall level at INFO for file logging
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE)
        # We will configure the StreamHandler separately
    ]
)

# Configure console handler separately to control its level
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.ERROR) # Set console level to ERROR
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)

# Add the console handler to the root logger
logging.getLogger('').addHandler(console_handler)

# Create a separate debug log file
debug_handler = logging.FileHandler(config.LOG_FILENAME)
debug_handler.setLevel(logging.DEBUG)
debug_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))

logger = logging.getLogger(__name__)
logger.addHandler(debug_handler)

# Constants
REQUIRED_PACKAGES = ['colorama']
OPTIONAL_PACKAGES = ['dotenv', 'requests', 'tqdm', 'tabulate', 'matplotlib', 'folium']
GOOGLE_SHEETS_AVAILABLE = False

# Global flag for graceful shutdown
_shutdown_requested = False


def _show_goodbye_message():
    """Display the goodbye message."""
    print("\n\nThank you for using EcoCycle! Goodbye.")
    print("With ♡ - the EcoCycle team.")


def _graceful_exit():
    """Perform graceful exit with cleanup and goodbye message."""
    global _shutdown_requested
    _shutdown_requested = True
    cleanup()
    _show_goodbye_message()
    sys.exit(0)


def signal_handler(signum, frame):
    """Handle SIGINT (Ctrl+C) gracefully."""
    # Parameters are required by signal.signal() but not used
    del signum, frame  # Explicitly mark as unused
    _graceful_exit()


def handle_keyboard_interrupt(func):
    """Decorator to handle KeyboardInterrupt gracefully in functions."""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except KeyboardInterrupt:
            global _shutdown_requested
            _shutdown_requested = True

            print("\n")  # New line for better formatting
            print("Operation cancelled by user.")

            # Ask user if they want to exit or continue
            try:
                choice = input("\nDo you want to exit EcoCycle? (y/n): ").lower().strip()
                if choice in ['y', 'yes']:
                    _graceful_exit()
                else:
                    print("Returning to main menu...")
                    _shutdown_requested = False
                    return None
            except KeyboardInterrupt:
                # If user presses Ctrl+C again, force exit
                _graceful_exit()
    return wrapper


def safe_input(prompt, default=""):
    """Safe input function that handles KeyboardInterrupt gracefully."""
    try:
        return input(prompt) or default
    except KeyboardInterrupt:
        global _shutdown_requested
        _shutdown_requested = True

        print("\n")  # New line for better formatting
        print("Input cancelled by user.")

        # Ask user if they want to exit or continue
        try:
            choice = input("\nDo you want to exit EcoCycle? (y/n): ").lower().strip()
            if choice in ['y', 'yes']:
                _graceful_exit()
            else:
                print("Returning to previous menu...")
                _shutdown_requested = False
                return default
        except KeyboardInterrupt:
            # If user presses Ctrl+C again, force exit
            _graceful_exit()


def setup_environment():
    """Set up environment variables from .env file."""
    try:
        import dotenv
        dotenv.load_dotenv()
        logger.info("Environment variables loaded from .env file")
    except ImportError:
        logger.warning("python-dotenv not installed, environment variables not loaded from .env file")


# Use dependency_manager functions instead of duplicating functionality
def is_package_installed(package_name):
    """Checks if a package is installed without importing it!"""
    return dependency_manager.is_package_installed(package_name)


def install_packages(packages, essential=False):
    """
    Installs or upgrades a list of packages using pip.
    Handles errors and essential package requirements.
    Returns True if all essential packages are available/installed.
    """
    success, failed_packages = dependency_manager.ensure_packages(packages, silent=False)
    if essential and not success:
        logger.error(f"Failed to install essential packages: {', '.join(failed_packages)}")
        return False
    return True


def check_existing_session(user_manager_instance):
    """
    Checks for a saved session file, validates its contents using HMAC,
    and logs the user in if the session is valid and the user exists.

    Args:
        user_manager_instance: An initialized instance of UserManager.

    Returns:
        The username (str) if the session is valid, otherwise None.
    """
    logger.debug(f"Checking for existing session file: {config.SESSION_FILE}")
    if not os.path.exists(config.SESSION_FILE):
        logger.info("No existing session file found.")
        return None

    try:
        with open(config.SESSION_FILE, 'r') as f:
            session_data = json.load(f)

        stored_username = session_data.get("username")
        stored_verifier = session_data.get("session_verifier")

        if not stored_username or not stored_verifier:
            logger.warning(f"Session file {config.SESSION_FILE} is incomplete or corrupted. Clearing.")
            user_manager_instance._clear_session() # Clear invalid file
            return None

        logger.debug(f"Session file found for user '{stored_username}'. Verifying...")

        # --- Verification using HMAC ---
        secret = user_manager_instance._get_session_secret()
        if not secret:
             # Critical error logged within _get_session_secret
             logger.error("Cannot verify session: Secret key is missing. Clearing potentially invalid session.")
             user_manager_instance._clear_session() # Clear if key missing
             return None

        # Recalculate the expected verifier based on the stored username
        expected_verifier = user_manager_instance._calculate_verifier(stored_username)
        if not expected_verifier:
            logger.error(f"Could not calculate expected verifier for user '{stored_username}'. Clearing session.")
            user_manager_instance._clear_session()
            return None

        # Securely compare the stored and expected verifiers
        # hmac.compare_digest is crucial to prevent timing attacks
        if not hmac.compare_digest(stored_verifier, expected_verifier):
            logger.warning(f"Session verifier mismatch for user '{stored_username}'. Possible tampering or key change. Clearing session.")
            user_manager_instance._clear_session()
            return None
        # --- End Verification ---

        logger.debug(f"Session verifier for '{stored_username}' is valid.")

        # Final check: Does the user still exist in the user database?
        # Need to access the loaded users dictionary in UserManager
        if stored_username in user_manager_instance.users:
            logger.info(f"Found valid and verified session for user '{stored_username}'. Logging in automatically.")
            # Set the user as logged in within the UserManager instance
            user_manager_instance.current_user = stored_username

            # Load trips from database to sync with in-memory data
            trips_loaded = user_manager_instance.load_user_trips_from_database(stored_username)
            if trips_loaded:
                logger.info(f"Successfully loaded trips from database for user '{stored_username}'")
            else:
                logger.warning(f"Failed to load trips from database for user '{stored_username}'")

            return stored_username
        else:
            logger.warning(f"User '{stored_username}' from verified session file no longer exists in user data. Clearing session.")
            user_manager_instance._clear_session()
            return None

    except json.JSONDecodeError:
        logger.error(f"Error decoding JSON from session file {config.SESSION_FILE}. Clearing session.")
        user_manager_instance._clear_session()
        return None
    except FileNotFoundError:
        logger.info("Session file disappeared during check.")
        return None
    except Exception as e:
        # Catch other potential errors (permissions, etc.)
        logger.error(f"Error reading or verifying session file {config.SESSION_FILE}: {e}", exc_info=True)
        # Consider clearing the session file here too for safety, depending on the error
        # user_manager_instance._clear_session()
        return None


def weather_route_planner(user_manager_instance):
    """Check weather and plan cycling routes."""
    modules = import_local_modules()
    ascii_art = modules['ascii_art']

    ascii_art.clear_screen()
    ascii_art.display_header()
    ascii_art.display_section_header("Weather and Route Planner")

    # Show menu options for different route planning modes
    options = [
        "Check weather forecast for cycling",
        "Plan basic cycling routes",
        "AI-powered route recommendations"
    ]

    ascii_art.display_menu("Route Planning Options", options)

    choice = input("\nSelect an option (1-3) or press Enter for basic route planning: ")

    if choice == "1":
        # Weather forecast only
        try:
            from apps.route_planner.weather_route_planner import WeatherRoutePlanner
            # The check_weather method now handles the case when 'requests' is not available
            WeatherRoutePlanner(user_manager_instance).check_weather()
        except (ImportError, AttributeError) as e:
            print(f"Weather forecast module not available: {e}")
            print("This feature requires the 'requests' package.")

            if not is_package_installed('requests'):
                install = input("Would you like to install the required package now? (y/n): ")
                if install.lower() == 'y':
                    success, _ = dependency_manager.ensure_feature('route_planning', silent=False)
                    if success:
                        print("Successfully installed the required package!")
                        print("Please restart the application to use this feature.")
                    else:
                        print("Failed to install the required package.")
                        print("Please install it manually with: pip install requests")

    elif choice == "3":
        # AI Route Planner
        try:
            if 'ai_route_planner' in modules and modules['ai_route_planner']:
                # Run the AI route planner
                modules['ai_route_planner'].run_ai_route_planner(user_manager_instance, modules.get('sheets_manager'))
            else:
                print("AI route planner module not available.")
                print("This feature requires the Google Generative AI package and API key.")

                if not is_package_installed('google-generativeai'):
                    install = input("Would you like to install the required package now? (y/n): ")
                    if install.lower() == 'y':
                        dependency_manager.ensure_package('google-generativeai', silent=False)
                        print("Please restart the application to use this feature.")

                # Check if API key is available
                if os.environ.get('GEMINI_API_KEY') is None:
                    print("\nThe GEMINI_API_KEY environment variable is not set.")
                    print("You'll need to create a Google AI Studio account and obtain an API key.")
                    print("Then add it to your .env file as GEMINI_API_KEY=your_key_here")
        except Exception as e:
            logger.error(f"Error running AI route planner: {e}")
            print(f"Error running AI route planner: {e}")

    else:  # Default or "2": Basic route planner
        # Basic route planner
        try:
            from apps.route_planner.weather_route_planner import run_planner

            # Run the weather and route planner module
            run_planner(user_manager_instance)
        except ImportError as e:
            # Basic implementation if the module is not available
            print(f"Weather and route planning module not available: {e}")
            print("\nThis feature helps you check weather conditions and plan optimal cycling routes.")
            print("It requires additional dependencies like requests and folium.")

            # Use dependency_manager to check which features are missing
            dependency_results = dependency_manager.check_all_dependencies()
            route_planning_status = dependency_results.get('route_planning', {'available': False, 'missing': []})

            if not route_planning_status['available']:
                missing = route_planning_status['missing']
                # Ensure missing is always a list of strings
                if isinstance(missing, list):
                    print(f"\nMissing dependencies: {', '.join(missing)}")
                else:
                    print(f"\nMissing dependencies: {missing}")
                install = input("Would you like to install them now? (y/n): ")
                if install.lower() == 'y':
                    success, failed = dependency_manager.ensure_feature('route_planning', silent=False)
                    if success:
                        print("Successfully installed all required packages!")
                        print("Please restart the application to use this feature.")
                    else:
                        print(f"Failed to install some packages: {', '.join(failed)}")
                        print("Please install them manually with pip.")
            else:
                print("\nAll required packages are installed, but there might be an issue with the module.")
                print("Please check the logs for more information.")

    input("\nPress Enter to return to the main menu...")


def settings_preferences(user_manager_instance):
    """Manage user settings and preferences using the enhanced Settings View."""
    try:
        # Import the settings view module
        from views.settings_view import show_settings

        # Use the new settings view that incorporates Rich UI styling
        show_settings(user_manager_instance)
    except ImportError as e:
        # Fallback to basic implementation if the settings_view module cannot be imported
        logger.error(f"Failed to import settings view: {e}")
        modules = import_local_modules()
        ascii_art = modules['ascii_art']
        ascii_art.display_error_message("Settings view module not available. Using basic settings interface.")

        # Use existing implementation as fallback
        _basic_settings_preferences(user_manager_instance)


def _basic_settings_preferences(user_manager_instance):
    """Basic implementation of settings and preferences as a fallback."""
    modules = import_local_modules()
    ascii_art = modules['ascii_art']

    # Check if user is authenticated
    if not user_manager_instance.is_authenticated():
        ascii_art.clear_screen()
        ascii_art.display_header()
        print("You need to log in to manage settings and preferences.")
        input("Press Enter to continue...")
        return

    while True:
        ascii_art.clear_screen()
        ascii_art.display_header()
        ascii_art.display_section_header("Settings and Preferences")

        # Get current preferences
        weight_kg = user_manager_instance.get_user_preference('weight_kg', 70)
        transport_mode = user_manager_instance.get_user_preference('default_transport_mode', 'bicycle')
        theme = user_manager_instance.get_user_preference('theme', 'default')
        units = user_manager_instance.get_user_preference('units', 'metric')

        # Get security preferences
        require_email_verification = user_manager_instance.get_user_preference('require_email_verification', False)
        enable_two_factor = user_manager_instance.get_user_preference('enable_two_factor', False)
        email_verified = user_manager_instance.get_current_user().get('email_verified', False)

        # Display current settings
        print("Current Settings:")
        print(f"1. Weight: {weight_kg} kg")
        print(f"2. Default Transport Mode: {transport_mode}")
        print(f"3. Theme: {theme}")
        print(f"4. Units: {units}")
        print(f"5. Customize Theme Colors")
        print(f"6. Security Settings")
        print("7. Back to Main Menu")

        # Get user choice
        choice = input("\nSelect a setting to change (1-7): ")

        if choice == '1':
            # Change weight
            try:
                new_weight = float(input("Enter your weight in kg: "))
                user_manager_instance.update_user_preference('weight_kg', new_weight)
                ascii_art.display_success_message("Weight updated successfully!")
            except ValueError:
                ascii_art.display_error_message("Invalid input. Please enter a numeric value.")

        elif choice == '2':
            # Change default transport mode
            print("\nAvailable Transport Modes:")
            modes = ['bicycle', 'e-bike', 'scooter', 'skateboard', 'walking']
            for i, mode in enumerate(modes, 1):
                print(f"{i}. {mode}")

            try:
                mode_choice = int(input(f"\nSelect a transport mode (1-{len(modes)}): "))
                if 1 <= mode_choice <= len(modes):
                    user_manager_instance.update_user_preference('default_transport_mode', modes[mode_choice-1])
                    ascii_art.display_success_message("Default transport mode updated successfully!")
                else:
                    ascii_art.display_error_message("Invalid selection.")
            except ValueError:
                ascii_art.display_error_message("Invalid input. Please enter a number.")

        elif choice == '3':
            # Change theme
            print("\nAvailable Themes:")
            themes = ['default', 'dark', 'eco', 'high-contrast', 'light']
            for i, theme in enumerate(themes, 1):
                print(f"{i}. {theme}")

            try:
                theme_choice = int(input(f"\nSelect a theme (1-{len(themes)}): "))
                if 1 <= theme_choice <= len(themes):
                    user_manager_instance.update_user_preference('theme', themes[theme_choice-1])
                    ascii_art.display_success_message("Theme updated successfully!")
                else:
                    ascii_art.display_error_message("Invalid selection.")
            except ValueError:
                ascii_art.display_error_message("Invalid input. Please enter a number.")

        elif choice == '4':
            # Change units
            print("\nAvailable Units:")
            unit_options = ['metric', 'imperial']
            for i, unit in enumerate(unit_options, 1):
                print(f"{i}. {unit}")

            try:
                unit_choice = int(input("\nSelect units (1-2): "))
                if 1 <= unit_choice <= len(unit_options):
                    user_manager_instance.update_user_preference('units', unit_options[unit_choice-1])
                    ascii_art.display_success_message("Units updated successfully!")
                else:
                    ascii_art.display_error_message("Invalid selection.")
            except ValueError:
                ascii_art.display_error_message("Invalid input. Please enter a number.")

        elif choice == '5':
            # Customize theme colors
            print("\nCustomize Theme Colors:")
            print("This feature allows you to customize the colors of the current theme.")

            # Check if theme_manager is available
            try:
                from utils.theme_manager import get_theme_manager
                theme_manager = get_theme_manager()

                # Get current theme
                current_theme_id = user_manager_instance.get_user_preference('theme', 'default')
                current_theme = theme_manager.get_theme(current_theme_id)

                # Display current theme colors
                print(f"\nCurrent Theme: {current_theme.get('name', 'Default')}")
                print(f"Description: {current_theme.get('description', '')}")

                # Display color options
                print("\nTheme Colors:")
                colors = current_theme.get('colors', {})
                color_options = list(colors.keys())

                for i, color_name in enumerate(color_options, 1):
                    color_value = colors.get(color_name, '#FFFFFF')
                    print(f"{i}. {color_name.replace('_', ' ').title()}: {color_value}")

                # Allow user to select a color to change
                color_choice = input("\nSelect a color to change (or Enter to cancel): ")

                if color_choice:
                    try:
                        index = int(color_choice) - 1
                        if 0 <= index < len(color_options):
                            color_name = color_options[index]
                            current_color = colors.get(color_name, '#FFFFFF')

                            # Get new color
                            new_color = input(f"\nEnter new color for {color_name} (hex format, e.g. #FF5500) [{current_color}]: ") or current_color

                            # Validate color
                            if not new_color.startswith('#') or not all(c in '0123456789ABCDEFabcdef' for c in new_color[1:]):
                                ascii_art.display_error_message("Invalid color format. Please use hex format (e.g. #FF5500).")
                            else:
                                # Create custom theme based on current theme
                                custom_theme = current_theme.copy()
                                custom_theme['name'] = 'Custom'
                                custom_theme['description'] = 'Your customized theme'

                                # Update color
                                custom_colors = custom_theme.get('colors', {}).copy()
                                custom_colors[color_name] = new_color
                                custom_theme['colors'] = custom_colors

                                # Save custom theme
                                theme_manager.create_theme('custom', custom_theme)

                                # Set as current theme
                                user_manager_instance.update_user_preference('theme', 'custom')
                                theme_manager.set_current_theme('custom')

                                ascii_art.display_success_message(f"Theme updated with new {color_name} color: {new_color}")
                        else:
                            ascii_art.display_error_message("Invalid selection.")
                    except ValueError:
                        ascii_art.display_error_message("Invalid input. Please enter a number.")
            except ImportError:
                ascii_art.display_error_message("Theme customization requires the theme_manager module.")
            except Exception as e:
                ascii_art.display_error_message(f"Error customizing theme: {str(e)}")

        elif choice == '6':
            # Security Settings
            ascii_art.clear_screen()
            ascii_art.display_header()
            ascii_art.display_section_header("Security Settings")

            # Get current security settings
            require_email_verification = user_manager_instance.get_user_preference('require_email_verification', False)
            enable_two_factor = user_manager_instance.get_user_preference('enable_two_factor', False)
            email_verified = user_manager_instance.get_current_user().get('email_verified', False)

            # Display current security settings
            print("\nCurrent Security Settings:")
            print(f"1. Require Email Verification: {'Enabled' if require_email_verification else 'Disabled'}")
            print(f"2. Two-Factor Authentication: {'Enabled' if enable_two_factor else 'Disabled'}")
            print(f"3. Email Verification Status: {'Verified' if email_verified else 'Not Verified'}")
            print("4. Resend Verification Email")
            print("5. Back to Settings Menu")

            security_choice = input("\nSelect an option (1-5): ")

            if security_choice == '1':
                # Toggle require email verification
                new_setting = not require_email_verification

                # If enabling, check if email is verified
                if new_setting and not email_verified:
                    print("\nWarning: Your email is not verified. Enabling this setting will prevent you from logging in until you verify your email.")
                    confirm = input("Are you sure you want to enable this setting? (y/n): ")
                    if confirm.lower() != 'y':
                        ascii_art.display_message("Setting not changed.")
                        continue

                user_manager_instance.update_user_preference('require_email_verification', new_setting)
                status = "enabled" if new_setting else "disabled"
                ascii_art.display_success_message(f"Email verification requirement {status} successfully!")

            elif security_choice == '2':
                # Toggle two-factor authentication
                new_setting = not enable_two_factor

                # If enabling, check if email is verified
                if new_setting and not email_verified:
                    print("\nWarning: Your email is not verified. Two-factor authentication requires a verified email.")
                    print("Please verify your email first.")
                    continue

                user_manager_instance.update_user_preference('enable_two_factor', new_setting)
                status = "enabled" if new_setting else "disabled"
                ascii_art.display_success_message(f"Two-factor authentication {status} successfully!")

            elif security_choice == '3':
                # Show email verification status
                if email_verified:
                    ascii_art.display_success_message("Your email is verified.")
                else:
                    ascii_art.display_warning_message("Your email is not verified.")
                    print("\nYou can resend the verification email by selecting option 4.")

            elif security_choice == '4':
                # Resend verification email
                user = user_manager_instance.get_current_user()
                email = user.get('email')

                if not email:
                    ascii_art.display_error_message("You don't have an email address associated with your account.")
                    print("Please add an email address in your profile settings.")
                else:
                    if user_manager_instance.request_email_verification(user.get('username')):
                        ascii_art.display_success_message(f"Verification email sent to {email}.")
                        print("Please check your email and follow the instructions to verify your email address.")
                    else:
                        ascii_art.display_error_message("Failed to send verification email. Please try again later.")

            elif security_choice == '5':
                # Back to settings menu
                continue

            else:
                ascii_art.display_error_message("Invalid choice. Please try again.")

        elif choice == '7':
            # Back to main menu
            break

        else:
            ascii_art.display_error_message("Invalid choice. Please try again.")

        input("\nPress Enter to continue...")


def social_sharing(user_manager_instance):
    """Manage social sharing and achievements with enhanced animations."""
    modules = import_local_modules()
    ascii_art = modules['ascii_art']

    ascii_art.clear_screen()
    ascii_art.display_header()
    ascii_art.display_section_header("Social Sharing and Achievements")

    # Check if social_gamification module is available
    try:
        import apps.gamification.social_gamification as social_gamification

        # Run the social sharing and achievements module
        social_gamification.run_social_features(user_manager_instance)
    except ImportError:
        # Enhanced implementation if we have enhanced UI and user is authenticated
        if user_manager_instance.is_authenticated() and not user_manager_instance.is_guest():
            try:
                _display_enhanced_achievements(user_manager_instance, ascii_art)
                return
            except Exception as e:
                logger.warning(f"Error running enhanced achievements: {e}")
                # Fall back to basic implementation

        # Basic implementation if the module is not available or enhanced UI failed
        print("Social sharing and achievements module not available.")
        print("\nThis feature allows you to share your cycling achievements and connect with other cyclists.")
        print("It includes achievements, badges, challenges, and social sharing options.")

        if user_manager_instance.is_authenticated() and not user_manager_instance.is_guest():
            user = user_manager_instance.get_current_user()
            stats = user.get('stats', {})

            if stats and stats.get('total_trips', 0) > 0:
                # Show basic achievements based on stats
                total_trips = stats.get('total_trips', 0)
                total_distance = stats.get('total_distance', 0.0)

                print("\nYour Achievements:")

                if total_trips >= 1:
                    print("✓ First Ride - Completed your first cycling trip")

                if total_trips >= 5:
                    print("✓ Regular Rider - Logged 5 or more cycling trips")

                if total_trips >= 10:
                    print("✓ Dedicated Cyclist - Logged 10 or more cycling trips")

                if total_distance >= 50:
                    print("✓ Half Century - Cycled a total of 50 km or more")

                if total_distance >= 100:
                    print("✓ Century Rider - Cycled a total of 100 km or more")
        else:
            print("\nYou need to log in (with a registered account) to track achievements.")

    input("\nPress Enter to return to the main menu...")


def _display_enhanced_achievements(user_manager_instance, ascii_art):
    """Display enhanced achievements with animations."""
    user = user_manager_instance.get_current_user()
    stats = user.get('stats', {})

    # If we have no stats, show a message with the mascot
    if not stats or not stats.get('total_trips', 0):
        if hasattr(ascii_art, 'display_mascot_animation'):
            ascii_art.display_mascot_animation("Start cycling to earn achievements!")
        else:
            print("No achievements yet. Start cycling to earn achievements!")
        input("\nPress Enter to continue...")
        return

    # Calculate achievement levels based on stats
    total_trips = stats.get('total_trips', 0)
    total_distance = stats.get('total_distance', 0.0)
    total_co2_saved = stats.get('total_co2_saved', 0.0)
    total_calories = stats.get('total_calories', 0)

    # Display a fun loading animation
    if hasattr(ascii_art, 'display_loading_animation'):
        ascii_art.display_loading_animation("Loading your achievements", 1.5)

    # Determine achievements and show them with animation
    achievements = []

    # Trip count achievements
    if total_trips >= 10:
        achievements.append(("streak", 3, "Dedicated Cyclist"))
    elif total_trips >= 5:
        achievements.append(("streak", 2, "Regular Rider"))
    elif total_trips >= 1:
        achievements.append(("streak", 1, "First Ride"))

    # Distance achievements
    if total_distance >= 100:
        achievements.append(("distance", 3, "Century Rider"))
    elif total_distance >= 50:
        achievements.append(("distance", 2, "Half Century"))
    elif total_distance >= 10:
        achievements.append(("distance", 1, "Getting Started"))

    # CO2 savings achievements
    if total_co2_saved >= 20:
        achievements.append(("carbon_saver", 3, "Climate Hero"))
    elif total_co2_saved >= 10:
        achievements.append(("carbon_saver", 2, "Climate Guardian"))
    elif total_co2_saved >= 2:
        achievements.append(("carbon_saver", 1, "Climate Conscious"))

    # Display all achievements with animation
    if achievements:
        for achievement_type, level, name in achievements:
            if hasattr(ascii_art, 'display_achievement_badge'):
                ascii_art.display_achievement_badge(achievement_type, level, name)
                time.sleep(0.5)  # Pause between achievements
            else:
                print(f"✓ {name}")
    else:
        print("No achievements yet. Start cycling to earn achievements!")

    # Offer to generate a sharing graphic or view route animation
    print("\nShare options:")

    if hasattr(ascii_art, 'create_social_share_graphic'):
        print("1. Create shareable achievement graphic")
    else:
        print("1. Share achievements (not available in basic mode)")

    if hasattr(ascii_art, 'animate_route_on_map'):
        print("2. View animated cycling routes")
    else:
        print("2. View routes (not available in basic mode)")

    print("3. Return to main menu")

    choice = input("\nSelect an option: ")

    if choice == "1" and hasattr(ascii_art, 'create_social_share_graphic'):
        # Generate a shareable graphic
        username = user.get('username', 'EcoCyclist')
        # Find the highest achievement
        if achievements:
            top_achievement = max(achievements, key=lambda x: x[1])
            achievement_name = top_achievement[2]
        else:
            achievement_name = "EcoCycle User"

        # Display social share graphic
        ascii_art.create_social_share_graphic(
            username,
            achievement_name,
            {
                "Total Trips": total_trips,
                "Total Distance": f"{total_distance:.1f}",
                "CO2 Saved": f"{total_co2_saved:.1f}",
                "Calories Burned": total_calories
            }
        )
    elif choice == "2" and hasattr(ascii_art, 'animate_route_on_map'):
        # Show animated route visualization
        ascii_art.animate_route_on_map()
    elif choice in ["1", "2"]:
        print("\nThis feature is not available in basic mode.")


def notifications(user_manager_instance):
    """Manage user notifications."""
    modules = import_local_modules()
    ascii_art = modules['ascii_art']

    ascii_art.clear_screen()
    ascii_art.display_header()
    ascii_art.display_section_header("Notifications")

    # Check if notification_system module is available
    try:
        import services.notifications.notification_system as notification_system

        # Run the notification system module
        notification_system.run_notification_manager(user_manager_instance)
    except ImportError:
        # Basic implementation if the module is not available
        print("Notification system module not available.")
        print("\nThis feature allows you to set up and manage notifications for:")
        print("- Cycling reminders")
        print("- Weather alerts for optimal cycling conditions")
        print("- Achievement and goal notifications")
        print("- Eco-tip of the day")

        # Check if user has enabled notifications
        if user_manager_instance.is_authenticated():
            notifications_enabled = user_manager_instance.get_user_preference('notifications_enabled', False)

            print(f"\nNotifications are currently {'enabled' if notifications_enabled else 'disabled'}.")

            print("\nOptions:")
            print("1. Toggle notification settings")
            print("2. Return to main menu")

            option = input("\nSelect an option (1-2): ")

            if option == "1":
                new_setting = not notifications_enabled
                user_manager_instance.update_user_preference('notifications_enabled', new_setting)
                status = "enabled" if new_setting else "disabled"
                ascii_art.display_success_message(f"Notifications {status} successfully!")
            elif option == "2" or option.lower() in ["b", "back", "m", "menu"]:
                print("Returning to main menu...")
                # No need to do anything else, control flow will return to menu
            else:
                print("Invalid option. Returning to main menu.")
        else:
            print("\nYou need to be logged in to manage notifications.")
            input("\nPress Enter to return to main menu...")

        # Mention external integrations
        print("\nExternal notification methods (email, SMS) require additional setup.")
        print("Install the notification_system module for full functionality.")

    input("\nPress Enter to return to the main menu...")


def admin_panel(user_manager_instance):
    """Admin panel for system management."""
    modules = import_local_modules()
    ascii_art = modules['ascii_art']

    ascii_art.clear_screen()
    ascii_art.display_header()
    ascii_art.display_section_header("Admin Panel")

    # Check if user is an admin
    if not user_manager_instance.is_admin():
        print("Access denied. Admin privileges required.")
        input("Press Enter to continue...")
        return

    # Check if admin_panel module is available
    try:
        import apps.admin.admin_panel as admin_panel_module

        # Run the admin panel module - check for available functions
        if hasattr(admin_panel_module, 'show_admin_panel'):
            # Need to get sheets_manager for the admin panel
            modules = import_local_modules()
            sheets_manager = modules.get('sheets_manager')
            if sheets_manager:
                admin_panel_module.show_admin_panel(sheets_manager, user_manager_instance)
            else:
                print("Sheets manager not available. Admin panel requires Google Sheets integration.")
        else:
            print("Admin panel module found but no entry point available.")
    except ImportError:
        # Basic implementation if the module is not available
        print("Admin panel module not available.")
        print("\nThis panel provides system management capabilities for administrators.")
        print("Features include:")
        print("- User management")
        print("- System statistics")
        print("- Data management")
        print("- System configuration")

        # Provide basic user management
        print("\nRegistered Users:")
        users = user_manager_instance.users

        if users:
            # Prepare data for table
            headers = ["Username", "Name", "Admin", "Trips"]
            data = []

            for username, user_data in users.items():
                if username != 'guest':  # Skip guest user
                    name = user_data.get('name', 'Unknown')
                    is_admin = "Yes" if user_data.get('is_admin', False) else "No"
                    trips = user_data.get('stats', {}).get('total_trips', 0)

                    data.append([username, name, is_admin, trips])

            # Display table
            ascii_art.display_data_table(headers, data)
        else:
            print("No registered users found.")

    input("\nPress Enter to return to the main menu...")


# Removed duplicate _setup_argument_parser function - now using shared argument parser from core.argument_parser


# Removed duplicate _initialize_managers function - now using CLICore.initialize_managers()


def handle_cli_arguments():
    """Parse command-line arguments and handle commands."""
    # Use shared argument parser
    argument_parser = create_main_parser()
    args = argument_parser.parse_args()

    # Initialize CLI core and command handler
    cli_core = CLICore()
    command_handler = BaseCommandHandler(cli_core)

    # Initialize managers and check session
    user_manager_instance, _ = cli_core.initialize_managers()
    modules = import_local_modules()

    # Check for Session Secret Key
    if not user_manager_instance._get_session_secret():
         # Error logged in _get_session_secret. Provide user feedback.
         print("\n" + "="*50)
         print(" CRITICAL ERROR: Session Secret Key Not Found!")
         print(f" Please ensure the '{SESSION_SECRET_ENV_VAR}' variable is set")
         print(" in your .env file or environment variables.")
         print(" Session persistence is disabled or insecure.")
         print(" The application may not function correctly.")
         print("="*50 + "\n")

    # Check for Existing Valid Session
    logged_in_username = check_existing_session(user_manager_instance)


    # --- Handle Specific CLI Commands ---
    if args.command == 'stats':
        command_handler.handle_stats_command(args)

    elif args.command == 'log':
        command_handler.handle_log_command(args)

    elif args.command == 'weather':
        command_handler.handle_weather_command(args)

    elif args.command == 'config':
        command_handler.handle_config_command(args)


    elif args.command == 'export':
        command_handler.handle_export_command(args)

    elif args.command == 'update':
        # Handle update command (implement actual update logic)
        if args.check:
            print("Checking for updates...")
            print("Update check feature not yet implemented.")
        else:
            print("Updating application...")
            print("Update feature not yet implemented.")

    elif args.command == 'help':
        # Handle help command
        if args.topic:
            print(f"Help for topic: {args.topic}")
            # TODO: Implement topic-specific help
            print("Topic-specific help not yet implemented.")
        else:
            parser.print_help()

    elif args.command == 'verify-email':
        # Handle email verification
        code = args.code
        if not code:
            print("Error: Verification code is required.")
            sys.exit(1)

        # Verify the code
        if user_manager_instance.verify_email_code(code):
            print("Email verification successful! Your email has been verified.")
            print("You can now log in to your account.")
        else:
            print("Email verification failed. The code may be invalid or expired.")
            print("Please request a new verification code from the settings menu.")
        sys.exit(0)

    elif args.command == 'reset-password':
        # Handle password reset
        username = args.username
        code = args.code
        new_password = args.new_password

        if not all([username, code, new_password]):
            print("Error: All parameters are required.")
            sys.exit(1)

        # Reset password
        if user_manager_instance.reset_password(username, code, new_password):
            print("Password reset successful! You can now log in with your new password.")
        else:
            print("Password reset failed. The code may be invalid or expired.")
            print("Please request a new password reset code.")
        sys.exit(0)

    elif args.command == 'recover-account':
        # Handle account recovery
        email = args.email
        code = args.code

        if not all([email, code]):
            print("Error: All parameters are required.")
            sys.exit(1)

        # Recover account
        if user_manager_instance.recover_account(email, code):
            print("Account recovery successful! You can now log in to your account.")
            print("For security reasons, please change your password immediately after logging in.")
        else:
            print("Account recovery failed. The code may be invalid or expired.")
            print("Please request a new account recovery code.")
        sys.exit(0)

    elif args.command == 'request-verification':
        # Handle verification code request
        verification_type = args.type
        username = args.username

        if not all([verification_type, username]):
            print("Error: All parameters are required.")
            sys.exit(1)

        # Request verification code
        if verification_type == 'email':
            if user_manager_instance.request_email_verification(username):
                print(f"Verification code sent to the email address associated with {username}.")
                print("Please check your email and use the 'verify-email' command to verify your email address.")
            else:
                print("Failed to send verification code. Please check the username and try again.")
        elif verification_type == 'password':
            if user_manager_instance.request_password_reset(username):
                print(f"Password reset code sent to the email address associated with {username}.")
                print("Please check your email and use the 'reset-password' command to reset your password.")
            else:
                print("Failed to send password reset code. Please check the username and try again.")
        elif verification_type == 'recovery':
            if user_manager_instance.request_account_recovery(username):
                print(f"Account recovery code sent to the email address associated with {username}.")
                print("Please check your email and use the 'recover-account' command to recover your account.")
            else:
                print("Failed to send account recovery code. Please check the username and try again.")
        sys.exit(0)

    elif args.command == 'doctor':
        # Handle doctor command - use enhanced doctor UI
        try:
            from utils.doctor_ui import EnhancedDoctorUI
            doctor_ui = EnhancedDoctorUI()
            doctor_ui.run_comprehensive_diagnostics(fix_issues=getattr(args, 'fix', False))
        except ImportError as e:
            # Fallback to basic implementation if enhanced UI is not available
            print(f"Enhanced doctor UI not available ({e}), using basic implementation...")
            perform_system_check(getattr(args, 'fix', False))
        except Exception as e:
            # Fallback for any other errors
            print(f"Error with enhanced doctor UI ({e}), using basic implementation...")
            perform_system_check(getattr(args, 'fix', False))

    elif args.command == 'version':
        # Handle version command
        display_version()
        sys.exit(0)

    # --- Default Action: Run Main Application UI ---
    else: # No specific command given, or 'run' command
        if logged_in_username:
            logger.info(f"User '{logged_in_username}' automatically logged in via session.")
            # Get user's display name safely
            user_data = user_manager_instance.users.get(logged_in_username, {})
            display_name = user_data.get('name', logged_in_username)
            print(f"\nWelcome back, {display_name}!")
            # Directly show the main menu
            show_main_menu(user_manager_instance)
        else:
            logger.info("No valid session found or CLI command specified. Proceeding to authentication.")
            # Attempt authentication first
            if user_manager_instance.authenticate():
                # If authentication is successful, show the main menu
                show_main_menu(user_manager_instance)
            else:
                # If authentication fails or is cancelled
                print("\nAuthentication failed or cancelled. Exiting application.")
                sys.exit(0) # Exit cleanly if auth fails/cancelled at start

# --- End of handle_cli_arguments function ---


def perform_system_check(fix_issues=False):
    """Perform basic system checks to ensure everything is working properly."""
    modules = import_local_modules()
    ascii_art = modules['ascii_art']

    ascii_art.clear_screen()
    ascii_art.display_header()
    ascii_art.display_section_header("System Check")

    issues = []

    # Check Python version
    import platform
    python_version = platform.python_version()
    required_version = (3, 6, 0)
    current_version = tuple(map(int, python_version.split('.')))

    if current_version >= required_version:
        ascii_art.display_success_message(f"Python version: {python_version} (compatible)")
    else:
        msg = f"Python version: {python_version} (incompatible, requires 3.6.0+)"
        issues.append(("python_version", msg))
        ascii_art.display_error_message(msg)

    # Check dependencies using dependency manager
    dependency_results = dependency_manager.check_all_dependencies()

    # Check core dependencies (required)
    core_status = dependency_results.get('core', {'available': False, 'missing': []})
    if core_status['available']:
        ascii_art.display_success_message("All core dependencies are installed")
    else:
        missing_packages = core_status.get('missing', [])
        # Ensure missing_packages is always a list
        if isinstance(missing_packages, list):
            for package in missing_packages:
                msg = f"Core dependency {package} is not installed"
                issues.append(("missing_package", package))
                ascii_art.display_error_message(msg)
        else:
            msg = f"Core dependencies issue: {missing_packages}"
            ascii_art.display_error_message(msg)

    # Check other feature dependencies (optional)
    for feature, status in dependency_results.items():
        if feature != 'core':  # Skip core as we already checked it
            if status['available']:
                ascii_art.display_success_message(f"All dependencies for {feature} feature are installed")
            else:
                missing_packages = status.get('missing', [])
                # Ensure missing_packages is always a list
                if isinstance(missing_packages, list):
                    for package in missing_packages:
                        msg = f"Optional dependency {package} for {feature} feature is not installed"
                        ascii_art.display_warning_message(msg)
                else:
                    msg = f"Dependencies issue for {feature}: {missing_packages}"
                    ascii_art.display_warning_message(msg)

    # Check for .env file
    if os.path.exists('.env'):
        ascii_art.display_success_message(".env file exists")
    else:
        msg = ".env file not found (optional)"
        ascii_art.display_warning_message(msg)

        # Create .env file if fixing issues
        if fix_issues:
            try:
                with open('.env', 'w') as f:
                    f.write("# EcoCycle environment variables\n")
                    f.write("# Add your configuration below\n\n")
                ascii_art.display_success_message(".env file created")
            except Exception as e:
                ascii_art.display_error_message(f"Error creating .env file: {e}")

    # Check for essential files
    essential_files = ['main.py', 'utils.py', 'user_manager.py', 'eco_tips.py', 'ascii_art.py']
    for file in essential_files:
        if os.path.exists(file):
            ascii_art.display_success_message(f"Essential file {file} exists")
        else:
            msg = f"Essential file {file} not found"
            issues.append(("missing_file", file))
            ascii_art.display_error_message(msg)

    # Fix issues if requested
    if fix_issues and issues:
        ascii_art.display_section_header("Fixing Issues")

        for issue_type, issue_data in issues:
            if issue_type == "missing_package":
                ascii_art.display_info_message(f"Installing package: {issue_data}")
                dependency_manager.ensure_package(issue_data, silent=False)
            elif issue_type == "missing_file":
                ascii_art.display_error_message(f"Cannot automatically create missing file: {issue_data}")

        # Re-run the check to see if issues were fixed
        ascii_art.display_info_message("Re-checking system after fixes...")
        if issues:
            # Wait a moment to show the message
            import time
            time.sleep(1)

            # Recursive call without fix_issues to prevent infinite loop
            perform_system_check(False)

    # Final summary
    if not issues:
        ascii_art.display_success_message("\nAll system checks passed! EcoCycle is ready to use.")
    else:
        if fix_issues:
            remaining_issues = sum(1 for issue_type, _ in issues
                                if issue_type in ["missing_package", "missing_file"])
            if remaining_issues:
                ascii_art.display_warning_message(f"\n{remaining_issues} issues remain. Some manual intervention may be required.")
            else:
                ascii_art.display_success_message("\nAll issues fixed! EcoCycle is ready to use.")
        else:
            ascii_art.display_warning_message(f"\n{len(issues)} issues found. Run with --fix to attempt automatic fixes.")

    input("Press Enter to continue...")


def cleanup():
    """Perform cleanup operations before exiting."""
    logger.info("Performing cleanup operations...")

    # Shutdown plugins
    try:
        plugin_loader.shutdown_plugins()
        logger.info("Plugins shut down successfully")
    except Exception as e:
        logger.error(f"Error shutting down plugins: {e}")

    # Add any other cleanup operations here

    logger.info("Cleanup complete.")


def main():
    """Main application entry point for the EcoCycle CLI application."""
    # Register cleanup function to be called on exit
    atexit.register(cleanup)

    # Set up signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)

    try:
        # Install core dependencies using dependency manager - silently
        success, failed_packages = dependency_manager.ensure_feature('core', silent=True)
        if not success:
            print(f"Failed to install core dependencies: {', '.join(failed_packages)}. Please install them manually.")
            sys.exit(1)

        # Set up environment.
        setup_environment()

        # Initialize the database
        try:
            database_manager.initialize_database()
        except Exception as e:
            handle_error(DatabaseError(f"Failed to initialize database: {e}"))
            sys.exit(1)

        # Initialize plugins
        try:
            plugins = plugin_loader.initialize_plugins()
            logger.info(f"Initialized {len(plugins)} plugins")
        except Exception as e:
            handle_error(e)
            logger.error(f"Error initializing plugins: {e}")

        # Start the application
        try:
            handle_cli_arguments()
        except Exception as e:
            handle_error(e)
            logger.error(f"Unhandled exception in main application: {e}")
            sys.exit(1)

    except KeyboardInterrupt:
        # Handle KeyboardInterrupt at the top level
        _graceful_exit()


if __name__ == "__main__":
    main()
