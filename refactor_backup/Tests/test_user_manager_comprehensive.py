#!/usr/bin/env python3
"""
Comprehensive User Manager Tests
Tests all aspects of user management including authentication, registration, 
email verification, session management, and data persistence.
"""

import os
import sys
import unittest
import tempfile
import json
import sqlite3
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

from Tests import EcoCycleTestCase, get_test_config
from auth.user_management.user_manager import UserManager
from auth.user_management.user_data_manager import UserDataManager
from auth.user_management.auth_handler import AuthHandler
from auth.user_management.user_registration import UserRegistration
from auth.email_verification import EmailVerification
from core.database_manager import DatabaseManager

class TestUserManagerComprehensive(EcoCycleTestCase):
    """Comprehensive tests for UserManager functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        
        # Create temporary files for testing
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        self.temp_users_file = tempfile.NamedTemporaryFile(delete=False, suffix='.json')
        self.temp_users_file.close()
        
        # Mock sheets manager
        self.mock_sheets_manager = Mock()
        
        # Create UserManager instance with test configuration
        self.user_manager = UserManager(self.mock_sheets_manager)
        
        # Override file paths for testing
        self.user_manager.user_data_manager.users_file = self.temp_users_file.name
        
        # Create test database
        self.db_manager = DatabaseManager(self.temp_db.name)
        self.db_manager.initialize_database()
        
    def tearDown(self):
        """Clean up test fixtures."""
        super().tearDown()
        
        # Remove temporary files
        for temp_file in [self.temp_db.name, self.temp_users_file.name]:
            if os.path.exists(temp_file):
                try:
                    os.unlink(temp_file)
                except OSError:
                    pass
    
    def test_user_manager_initialization(self):
        """Test UserManager initialization."""
        self.assertIsNotNone(self.user_manager)
        self.assertIsNotNone(self.user_manager.user_data_manager)
        self.assertIsNotNone(self.user_manager.auth_handler)
        self.assertIsNotNone(self.user_manager.user_registration)
        self.assertIsInstance(self.user_manager.users, dict)
        
    def test_user_registration_flow(self):
        """Test complete user registration flow."""
        # Test data
        test_username = get_test_config('test_username')
        test_email = get_test_config('test_email')
        test_password = get_test_config('test_password')
        
        # Mock user input for registration
        with patch('builtins.input', side_effect=[test_username, test_email, test_password, test_password]):
            with patch('getpass.getpass', return_value=test_password):
                # Mock email verification
                with patch.object(self.user_manager.user_registration, 'send_verification_email', return_value=True):
                    result = self.user_manager.register_new_user()
                    
        self.assertTrue(result)
        self.assertIn(test_username, self.user_manager.users)
        
        # Verify user data structure
        user_data = self.user_manager.users[test_username]
        self.assertEqual(user_data['username'], test_username)
        self.assertEqual(user_data['email'], test_email)
        self.assertIn('password_hash', user_data)
        self.assertIn('salt', user_data)
        self.assertIn('registration_date', user_data)
        
    def test_user_authentication(self):
        """Test user authentication with various scenarios."""
        # Create a test user first
        test_username = 'auth_test_user'
        test_password = 'test_password_123'
        
        # Create user data
        user_data = self.user_manager.user_data_manager.create_user_data(
            username=test_username,
            email='<EMAIL>',
            password=test_password
        )
        self.user_manager.users[test_username] = user_data
        
        # Test successful authentication
        with patch('getpass.getpass', return_value=test_password):
            result = self.user_manager.authenticate_user(test_username)
            
        self.assertTrue(result)
        self.assertEqual(self.user_manager.current_user, test_username)
        
        # Test failed authentication with wrong password
        with patch('getpass.getpass', return_value='wrong_password'):
            result = self.user_manager.authenticate_user(test_username)
            
        self.assertFalse(result)
        
        # Test authentication with non-existent user
        result = self.user_manager.authenticate_user('non_existent_user')
        self.assertFalse(result)
        
    def test_email_verification_flow(self):
        """Test email verification functionality."""
        test_username = 'email_test_user'
        test_email = '<EMAIL>'
        
        # Create user data
        user_data = self.user_manager.user_data_manager.create_user_data(
            username=test_username,
            email=test_email,
            password='test_password'
        )
        self.user_manager.users[test_username] = user_data
        
        # Test email verification code generation
        with patch.object(self.user_manager.user_registration, 'generate_verification_code') as mock_gen:
            mock_gen.return_value = '123456'
            
            # Test sending verification email
            with patch.object(self.user_manager.user_registration, 'send_verification_email') as mock_send:
                mock_send.return_value = True
                
                result = self.user_manager.user_registration.send_verification_email(test_email, '123456')
                self.assertTrue(result)
                mock_send.assert_called_once()
        
        # Test email verification
        with patch.object(self.user_manager.user_registration, 'verify_email_code') as mock_verify:
            mock_verify.return_value = True
            
            result = self.user_manager.user_registration.verify_email_code(test_email, '123456')
            self.assertTrue(result)
            
    def test_session_management(self):
        """Test session creation, validation, and cleanup."""
        test_username = 'session_test_user'
        
        # Create user data
        user_data = self.user_manager.user_data_manager.create_user_data(
            username=test_username,
            email='<EMAIL>',
            password='test_password'
        )
        self.user_manager.users[test_username] = user_data
        
        # Test session creation
        self.user_manager.current_user = test_username
        session_created = self.user_manager.auth_handler.create_session(test_username)
        self.assertTrue(session_created)
        
        # Test session validation
        is_valid = self.user_manager.auth_handler.validate_session(test_username)
        self.assertTrue(is_valid)
        
        # Test session cleanup
        self.user_manager.logout()
        self.assertIsNone(self.user_manager.current_user)
        
    def test_user_data_persistence(self):
        """Test user data saving and loading."""
        test_username = 'persistence_test_user'
        
        # Create user data
        user_data = self.user_manager.user_data_manager.create_user_data(
            username=test_username,
            email='<EMAIL>',
            password='test_password'
        )
        self.user_manager.users[test_username] = user_data
        
        # Save users
        save_result = self.user_manager.save_users()
        self.assertTrue(save_result)
        
        # Create new UserManager instance and load users
        new_user_manager = UserManager(self.mock_sheets_manager)
        new_user_manager.user_data_manager.users_file = self.temp_users_file.name
        new_user_manager.load_users()
        
        # Verify user data was loaded correctly
        self.assertIn(test_username, new_user_manager.users)
        loaded_user = new_user_manager.users[test_username]
        self.assertEqual(loaded_user['username'], test_username)
        self.assertEqual(loaded_user['email'], '<EMAIL>')
        
    def test_user_preferences_management(self):
        """Test user preferences functionality."""
        test_username = 'prefs_test_user'
        
        # Create and set current user
        user_data = self.user_manager.user_data_manager.create_user_data(
            username=test_username,
            email='<EMAIL>',
            password='test_password'
        )
        self.user_manager.users[test_username] = user_data
        self.user_manager.current_user = test_username
        
        # Test setting preferences
        self.user_manager.update_user_preference('weight_kg', 75.5)
        self.user_manager.update_user_preference('default_transport_mode', 'e-bike')
        self.user_manager.update_user_preference('theme', 'dark')
        
        # Test getting preferences
        weight = self.user_manager.get_user_preference('weight_kg')
        transport = self.user_manager.get_user_preference('default_transport_mode')
        theme = self.user_manager.get_user_preference('theme')
        
        self.assertEqual(weight, 75.5)
        self.assertEqual(transport, 'e-bike')
        self.assertEqual(theme, 'dark')
        
        # Test default values
        non_existent = self.user_manager.get_user_preference('non_existent_pref', 'default_value')
        self.assertEqual(non_existent, 'default_value')
        
    def test_user_statistics_update(self):
        """Test user statistics tracking and updates."""
        test_username = 'stats_test_user'
        
        # Create and set current user
        user_data = self.user_manager.user_data_manager.create_user_data(
            username=test_username,
            email='<EMAIL>',
            password='test_password'
        )
        self.user_manager.users[test_username] = user_data
        self.user_manager.current_user = test_username
        
        # Test updating statistics
        result = self.user_manager.update_user_stats(
            distance=10.5,
            co2_saved=2.016,
            calories=525,
            duration=30.0
        )
        self.assertTrue(result)
        
        # Verify statistics were updated
        user_data = self.user_manager.users[test_username]
        self.assertGreater(user_data.get('total_distance', 0), 0)
        self.assertGreater(user_data.get('total_co2_saved', 0), 0)
        self.assertGreater(user_data.get('total_calories', 0), 0)
        
    def test_guest_user_functionality(self):
        """Test guest user creation and management."""
        # Test guest login
        guest_result = self.user_manager.login_as_guest()
        self.assertTrue(guest_result)
        
        # Verify guest user exists
        self.assertIn('guest', self.user_manager.users)
        guest_user = self.user_manager.users['guest']
        self.assertTrue(guest_user.get('is_guest', False))
        
        # Test guest user limitations
        self.assertEqual(self.user_manager.current_user, 'guest')
        
    def test_password_security(self):
        """Test password hashing and validation."""
        test_password = 'secure_test_password_123!'
        
        # Test password hashing
        password_hash, salt = self.user_manager.auth_handler.hash_password(test_password)
        self.assertIsNotNone(password_hash)
        self.assertIsNotNone(salt)
        self.assertNotEqual(password_hash, test_password)  # Should be hashed
        
        # Test password verification
        is_valid = self.user_manager.auth_handler.verify_password(test_password, password_hash, salt)
        self.assertTrue(is_valid)
        
        # Test wrong password
        is_invalid = self.user_manager.auth_handler.verify_password('wrong_password', password_hash, salt)
        self.assertFalse(is_invalid)
        
    def test_error_handling(self):
        """Test error handling in various scenarios."""
        # Test authentication without current user
        self.assertFalse(self.user_manager.is_authenticated())
        
        # Test updating stats without authentication
        result = self.user_manager.update_user_stats(10.0, 2.0, 500, 30.0)
        self.assertFalse(result)
        
        # Test getting preferences without authentication
        pref = self.user_manager.get_user_preference('weight_kg', 70)
        self.assertEqual(pref, 70)  # Should return default
        
        # Test invalid file operations
        with patch('builtins.open', side_effect=IOError("File not accessible")):
            result = self.user_manager.save_users()
            self.assertFalse(result)

if __name__ == '__main__':
    unittest.main()
