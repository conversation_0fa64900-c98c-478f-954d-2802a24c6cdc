"""
EcoCycle Test Suite
Comprehensive testing framework for the EcoCycle application backend.
"""

import os
import sys
import unittest
import logging
from typing import Dict, Any, Optional

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

# Configure test logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(PROJECT_ROOT, 'Tests', 'test_logs', 'test_suite.log')),
        logging.StreamHandler()
    ]
)

# Test configuration
TEST_CONFIG = {
    'database_file': os.path.join(PROJECT_ROOT, 'Tests', 'test_data', 'test_ecocycle.db'),
    'user_data_file': os.path.join(PROJECT_ROOT, 'Tests', 'test_data', 'test_users.json'),
    'cache_dir': os.path.join(PROJECT_ROOT, 'Tests', 'test_data', 'cache'),
    'log_dir': os.path.join(PROJECT_ROOT, 'Tests', 'test_logs'),
    'backup_dir': os.path.join(PROJECT_ROOT, 'Tests', 'test_data', 'backups'),
    'test_email': '<EMAIL>',
    'test_username': 'test_user',
    'test_password': 'test_password_123',
    'mock_api_responses': True,
    'skip_external_apis': True
}

# Create test directories
for dir_path in [TEST_CONFIG['cache_dir'], TEST_CONFIG['log_dir'], TEST_CONFIG['backup_dir']]:
    os.makedirs(dir_path, exist_ok=True)

def get_test_config(key: str, default: Any = None) -> Any:
    """Get test configuration value."""
    return TEST_CONFIG.get(key, default)

def setup_test_environment():
    """Set up the test environment."""
    # Set environment variables for testing
    os.environ['TESTING'] = 'true'
    os.environ['DATABASE_FILE'] = TEST_CONFIG['database_file']
    os.environ['USER_DATA_FILE'] = TEST_CONFIG['user_data_file']
    os.environ['CACHE_DIR'] = TEST_CONFIG['cache_dir']
    os.environ['LOG_DIR'] = TEST_CONFIG['log_dir']
    os.environ['SKIP_EXTERNAL_APIS'] = str(TEST_CONFIG['skip_external_apis'])

def cleanup_test_environment():
    """Clean up the test environment."""
    # Remove test files
    test_files = [
        TEST_CONFIG['database_file'],
        TEST_CONFIG['user_data_file']
    ]

    for file_path in test_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
            except OSError:
                pass

class EcoCycleTestCase(unittest.TestCase):
    """Base test case for EcoCycle tests."""

    @classmethod
    def setUpClass(cls):
        """Set up class-level test fixtures."""
        setup_test_environment()

    @classmethod
    def tearDownClass(cls):
        """Clean up class-level test fixtures."""
        cleanup_test_environment()

    def setUp(self):
        """Set up test fixtures."""
        self.test_config = TEST_CONFIG.copy()

    def tearDown(self):
        """Clean up test fixtures."""
        pass

# Test discovery and runner functions
def discover_tests(start_dir: str = None) -> unittest.TestSuite:
    """Discover all tests in the test directory."""
    if start_dir is None:
        start_dir = os.path.dirname(__file__)

    loader = unittest.TestLoader()
    suite = loader.discover(start_dir, pattern='test_*.py')
    return suite

def run_test_suite(verbosity: int = 2) -> unittest.TestResult:
    """Run the complete test suite."""
    suite = discover_tests()
    runner = unittest.TextTestRunner(verbosity=verbosity)
    return runner.run(suite)

if __name__ == '__main__':
    # Run all tests when this module is executed directly
    result = run_test_suite()
    sys.exit(0 if result.wasSuccessful() else 1)