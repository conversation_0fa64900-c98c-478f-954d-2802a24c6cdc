#!/usr/bin/env python3
"""
EcoCycle - Enhanced UI Demo
Run this script to demonstrate the enhanced UI features.
"""

import os
import sys
import time
from typing import Dict, Any

def setup_environment():
    """Set up environment variables from .env file."""
    try:
        import dotenv
        dotenv.load_dotenv()
        print("Environment variables loaded from .env file")
    except ImportError:
        print("python-dotenv not installed, environment variables not loaded from .env file")

def main():
    """Main demo function that showcases the enhanced UI features."""
    # First try to use enhanced ASCII art if available
    try:
        import utils.ascii_art as ascii_art
        print("Using enhanced UI with animations\n")
    except ImportError:
        import utils.ascii_art
        print("Using standard UI (enhanced UI not available)\n")
    
    # Clear screen and display header
    ascii_art.clear_screen()
    ascii_art.display_header()
    
    # Introduction
    ascii_art.display_section_header("Enhanced UI Demo")
    print("\nThis demo shows the enhanced UI features of EcoCycle.")
    print("Including animations, progress bars, and interactive elements.")
    
    # Demo message types
    ascii_art.display_section_header("Message Types")
    ascii_art.display_info_message("This is an information message")
    time.sleep(0.5)
    ascii_art.display_success_message("This is a success message")
    time.sleep(0.5)
    ascii_art.display_warning_message("This is a warning message")
    time.sleep(0.5)
    ascii_art.display_error_message("This is an error message")
    time.sleep(1)
    
    # Demo loading animation
    ascii_art.display_section_header("Loading Animation")
    if hasattr(ascii_art, 'display_loading_animation'):
        ascii_art.display_loading_animation("Loading user data", 2.0)
    else:
        print("Loading animation not available in basic mode")
        time.sleep(1)
    
    # Demo animated menu
    ascii_art.display_section_header("Animated Menu")
    menu_options = ["View Statistics", "Log Cycling Trip", "Calculate Carbon Footprint"]
    if hasattr(ascii_art, 'display_animated_menu'):
        ascii_art.display_animated_menu("Main Menu", menu_options, 1)
    else:
        ascii_art.display_menu("Main Menu", menu_options, 1)
    time.sleep(1)
    
    # Demo progress bar
    ascii_art.display_section_header("Progress Bar")
    if hasattr(ascii_art, 'display_animated_progress_bar'):
        ascii_art.display_animated_progress_bar(75, 100, 40, "Carbon Savings Progress", 2.0)
    else:
        ascii_art.display_progress_bar(75, 100, 40, "Carbon Savings Progress")
    time.sleep(1)
    
    # Demo achievement badge
    ascii_art.display_section_header("Achievement Badge")
    if hasattr(ascii_art, 'display_achievement_badge'):
        ascii_art.display_achievement_badge("distance", 3, "100 km Milestone")
    else:
        print("★★★ Achievement Unlocked: 100 km Milestone ★★★")
    time.sleep(1)
    
    # Demo mascot animation
    ascii_art.display_section_header("Mascot Animation")
    if hasattr(ascii_art, 'display_mascot_animation'):
        ascii_art.display_mascot_animation("Remember to cycle safely and wear a helmet!")
    else:
        print("Eco-Mascot says: Remember to cycle safely and wear a helmet!")
    time.sleep(1)
    
    # Demo social share graphic
    ascii_art.display_section_header("Social Share Graphic")
    stats_data = {
        "Distance": "42.5 km",
        "CO2 Saved": "8.2 kg",
        "Calories Burned": "1250",
        "Achievements": "3 new badges"
    }
    if hasattr(ascii_art, 'create_social_share_graphic'):
        ascii_art.create_social_share_graphic(
            "EcoCyclist",
            "Weekly Challenge Completed",
            stats_data
        )
    else:
        print("\nWeekly Challenge Completed - EcoCyclist")
        print("-----------------------------------------")
        for key, value in stats_data.items():
            print(f"{key}: {value}")
        print("-----------------------------------------")
    time.sleep(1)
    
    # Demo route animation
    ascii_art.display_section_header("Route Animation")
    if hasattr(ascii_art, 'animate_route_on_map'):
        ascii_art.animate_route_on_map()
    else:
        print("Route animation not available in basic mode")
        print("A simple map would be displayed here")
    
    # Conclusion
    ascii_art.display_section_header("Demo Complete")
    ascii_art.display_success_message("Enhanced UI features demo completed successfully!")
    print("\nThese UI enhancements improve the user experience with:")
    print("- Visual feedback through animations and colors")
    print("- Interactive elements for better engagement")
    print("- Gamification elements for motivation")
    print("- Social sharing features for community building")
    
    # Final message
    print("\nDemo completed! Exit the program to return to the command line.")

if __name__ == "__main__":
    setup_environment()
    main()