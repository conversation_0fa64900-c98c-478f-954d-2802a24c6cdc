name: EcoCycle CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  lint:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 pylint black isort mypy
        # Install only essential dependencies for linting
        pip install rich colorama python-dotenv
    - name: Lint with flake8 (syntax errors only)
      run: |
        # Only check for critical syntax errors to speed up CI
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics \
          --exclude=venv,eco_venv,.venv,build,dist,*.egg-info,__pycache__,Tests,backups,temp
    - name: Check formatting with black (main files only)
      run: |
        black --check --diff main.py cli.py setup.py
    - name: Check imports with isort (main files only)
      run: |
        isort --check --diff main.py cli.py setup.py
    - name: Static type checking with mypy (main files only)
      run: |
        mypy --ignore-missing-imports main.py cli.py || true
    - name: Lint with pylint (main files only)
      run: |
        pylint --disable=C0111,C0103,C0303,C0330,C0326,C0301,C0302,C0305,R0902,R0903,R0904,R0912,R0913,R0914,R0915,W0212,W0511,W0613,W0703,W1201,W1202 \
          main.py cli.py setup.py || true

  test:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    strategy:
      matrix:
        python-version: ['3.9', '3.10', '3.11']  # Removed 3.8 due to compatibility issues
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-test-${{ matrix.python-version }}-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-test-${{ matrix.python-version }}-
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-cov pytest-timeout
        # Install core dependencies only for testing
        pip install rich colorama python-dotenv requests bcrypt
    - name: Run quick CI tests
      run: |
        python scripts/quick_ci_test.py
    - name: Test with pytest (basic tests only)
      run: |
        # Run only basic functionality tests to speed up CI
        if [ -f "Tests/test_basic_functionality.py" ]; then
          pytest Tests/test_basic_functionality.py -v --timeout=300 || true
        fi
        if [ -f "Tests/test_cli.py" ]; then
          pytest Tests/test_cli.py -v --timeout=300 || true
        fi
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        fail_ci_if_error: false
      if: matrix.python-version == '3.10'

  build:
    runs-on: ubuntu-latest
    needs: [lint, test]
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    - name: Build package
      run: |
        python -m build
    - name: Check package
      run: |
        twine check dist/*
    - name: Archive production artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist
        path: |
          dist/

  security:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-security-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-security-
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety
        # Install only essential dependencies for security scanning
        pip install rich colorama python-dotenv
    - name: Security check with bandit (main files only)
      run: |
        bandit -r . -x ./Tests,./venv,./eco_venv,./build,./dist,./backups,./temp,./cache,./data \
          --skip B101,B601,B602,B603,B604,B605,B606,B607 \
          --format json -o bandit-report.json || true
        bandit -r main.py cli.py setup.py --format txt || true
    - name: Check dependencies with safety
      run: |
        safety check --json || true
