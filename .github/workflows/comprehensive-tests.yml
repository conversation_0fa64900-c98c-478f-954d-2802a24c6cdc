name: Comprehensive Tests

on:
  schedule:
    # Run comprehensive tests daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    # Allow manual triggering
  push:
    branches: [ main ]
    paths:
      - 'Tests/**'
      - 'requirements.txt'
      - '.github/workflows/comprehensive-tests.yml'

jobs:
  comprehensive-test:
    runs-on: ubuntu-latest
    timeout-minutes: 60
    strategy:
      matrix:
        python-version: ['3.9', '3.10', '3.11']
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-comprehensive-${{ matrix.python-version }}-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-comprehensive-${{ matrix.python-version }}-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-dev.txt
        pip install -r requirements.txt
    
    - name: Run comprehensive tests
      run: |
        pytest Tests/ -v --timeout=600 --maxfail=10 \
          --ignore=Tests/test_performance_comprehensive.py \
          --ignore=Tests/test_security_comprehensive.py \
          || true
    
    - name: Run performance tests
      run: |
        if [ -f "Tests/test_performance_comprehensive.py" ]; then
          pytest Tests/test_performance_comprehensive.py -v --timeout=900 || true
        fi
    
    - name: Run security tests
      run: |
        if [ -f "Tests/test_security_comprehensive.py" ]; then
          pytest Tests/test_security_comprehensive.py -v --timeout=600 || true
        fi
    
    - name: Generate test report
      run: |
        echo "Comprehensive test run completed for Python ${{ matrix.python-version }}"
        echo "Check logs above for detailed results"
    
    - name: Upload test artifacts
      uses: actions/upload-artifact@v3
      with:
        name: test-results-${{ matrix.python-version }}
        path: |
          pytest-report.xml
          coverage.xml
        if-no-files-found: ignore
