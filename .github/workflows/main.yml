name: Upload Python Package

on:
  release:
    types: [published]

jobs:
  build-and-upload:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
 
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.x"

      - name: Install build
        run: pip install build twine

      - name: Build distribution files
        run: python -m build

      - name: Upload to GitHub Releases
        uses: actions/upload-release-asset@v1
        env:
            GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
            upload_url: ${{ github.event.release.upload_url }}
            asset_path: dist/ecocycle*
            asset_name: ecocycle-distributions.zip
            asset_content_type: application/zip
