version: '3.8'

services:
  app:
    build: .
    ports:
      - "5050:5050"
    volumes:
      - ./:/app
      - ecocycle-data:/app/data
      - ecocycle-logs:/app/Logs
      - ecocycle-backups:/app/database_backups
    environment:
      - DEBUG=true
      - BASE_URL=http://localhost:5050
      - SESSION_SECRET_KEY=${SESSION_SECRET_KEY:-default-secret-key-for-development-only}
      - EMAIL_USERNAME=${EMAIL_USERNAME}
      - EMAIL_PASSWORD=${EMAIL_PASSWORD}
      - FROM_EMAIL=${FROM_EMAIL:-<EMAIL>}
      - GOOGLE_MAPS_API_KEY=${GOOGLE_MAPS_API_KEY}
      - OPENWEATHERMAP_API_KEY=${OPENWEATHERMAP_API_KEY}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5050/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

volumes:
  ecocycle-data:
  ecocycle-logs:
  ecocycle-backups:
