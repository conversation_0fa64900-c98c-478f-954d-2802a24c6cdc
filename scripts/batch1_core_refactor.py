#!/usr/bin/env python3
"""
EcoCycle Batch 1: Core Module Reorganization
Moves and reorganizes core modules with validation.
"""

import os
import sys
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import subprocess

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('Logs/refactor.log', mode='a'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def move_core_modules(project_root: Path) -> bool:
    """Move core modules to new structure."""
    try:
        logger.info("Starting Batch 1: Core Module Reorganization")
        
        # Define file moves for core modules
        core_moves = [
            # Database module
            ('core/database_manager.py', 'ecocycle/core/database/manager.py'),
            ('core/database_optimizer.py', 'ecocycle/core/database/optimizer.py'),
            
            # Dependency module
            ('core/dependency/dependency_manager.py', 'ecocycle/core/dependency/manager.py'),
            ('core/dependency/__init__.py', 'ecocycle/core/dependency/__init__.py'),
            
            # Error handling
            ('core/error_handler.py', 'ecocycle/core/error/handler.py'),
            
            # Logging
            ('core/logging_manager.py', 'ecocycle/core/logging/manager.py'),
            
            # Plugin system
            ('core/plugin/plugin_manager.py', 'ecocycle/core/plugin/manager.py'),
            ('core/plugin/plugin_loader.py', 'ecocycle/core/plugin/loader.py'),
            ('core/plugin/__init__.py', 'ecocycle/core/plugin/__init__.py'),
            
            # Other core files
            ('core/cli_core.py', 'ecocycle/core/cli_core.py'),
            ('core/argument_parser.py', 'ecocycle/core/argument_parser.py'),
            ('core/command_handlers.py', 'ecocycle/core/command_handlers.py'),
            ('core/__init__.py', 'ecocycle/core/__init__.py'),
        ]
        
        # Move files
        for src_path, dst_path in core_moves:
            src = project_root / src_path
            dst = project_root / dst_path
            
            if src.exists():
                # Ensure destination directory exists
                dst.parent.mkdir(parents=True, exist_ok=True)
                
                # Copy the file (don't move yet, in case we need to rollback)
                shutil.copy2(str(src), str(dst))
                logger.info(f"Copied: {src_path} -> {dst_path}")
            else:
                logger.warning(f"Source file not found: {src_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"Core module move failed: {e}")
        return False

def update_core_imports(project_root: Path) -> bool:
    """Update import statements for moved core modules."""
    try:
        logger.info("Updating core module imports...")
        
        # Import mappings for core modules
        import_mappings = {
            'core.database_manager': 'ecocycle.core.database.manager',
            'core.database_optimizer': 'ecocycle.core.database.optimizer',
            'core.dependency.dependency_manager': 'ecocycle.core.dependency.manager',
            'core.error_handler': 'ecocycle.core.error.handler',
            'core.logging_manager': 'ecocycle.core.logging.manager',
            'core.plugin.plugin_manager': 'ecocycle.core.plugin.manager',
            'core.plugin.plugin_loader': 'ecocycle.core.plugin.loader',
            'core.cli_core': 'ecocycle.core.cli_core',
            'core.argument_parser': 'ecocycle.core.argument_parser',
            'core.command_handlers': 'ecocycle.core.command_handlers',
        }
        
        # Update imports in new ecocycle directory
        ecocycle_dir = project_root / 'ecocycle'
        if ecocycle_dir.exists():
            for py_file in ecocycle_dir.rglob('*.py'):
                update_imports_in_file(py_file, import_mappings)
        
        # Update imports in remaining files at root level
        root_files = ['main.py', 'cli.py', 'mini.py']
        for file_name in root_files:
            file_path = project_root / file_name
            if file_path.exists():
                update_imports_in_file(file_path, import_mappings)
        
        return True
        
    except Exception as e:
        logger.error(f"Import update failed: {e}")
        return False

def update_imports_in_file(file_path: Path, import_mappings: Dict[str, str]) -> bool:
    """Update import statements in a single file."""
    try:
        if not file_path.exists() or file_path.suffix != '.py':
            return True
        
        content = file_path.read_text()
        original_content = content
        
        # Update imports based on mappings
        for old_import, new_import in import_mappings.items():
            # Handle different import patterns
            import re
            patterns = [
                (rf'^import {re.escape(old_import)}$', f'import {new_import}'),
                (rf'^from {re.escape(old_import)} import', f'from {new_import} import'),
                (rf'^import {re.escape(old_import)} as', f'import {new_import} as'),
                (rf'import {re.escape(old_import)}\.', f'import {new_import}.'),
                (rf'from {re.escape(old_import)}\.', f'from {new_import}.'),
            ]
            
            for pattern, replacement in patterns:
                content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        # Only write if content changed
        if content != original_content:
            file_path.write_text(content)
            logger.info(f"Updated imports in: {file_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to update imports in {file_path}: {e}")
        return False

def create_core_init_files(project_root: Path) -> bool:
    """Create proper __init__.py files for core modules."""
    try:
        logger.info("Creating core module __init__.py files...")
        
        # Core database __init__.py
        db_init = project_root / 'ecocycle/core/database/__init__.py'
        db_init.write_text('''"""EcoCycle core database module."""
from .manager import *
from .optimizer import *
''')
        
        # Core dependency __init__.py
        dep_init = project_root / 'ecocycle/core/dependency/__init__.py'
        dep_init.write_text('''"""EcoCycle core dependency module."""
from .manager import *
''')
        
        # Core error __init__.py
        error_init = project_root / 'ecocycle/core/error/__init__.py'
        error_init.write_text('''"""EcoCycle core error handling module."""
from .handler import *
''')
        
        # Core logging __init__.py
        log_init = project_root / 'ecocycle/core/logging/__init__.py'
        log_init.write_text('''"""EcoCycle core logging module."""
from .manager import *
''')
        
        # Core plugin __init__.py
        plugin_init = project_root / 'ecocycle/core/plugin/__init__.py'
        plugin_init.write_text('''"""EcoCycle core plugin module."""
from .manager import *
from .loader import *
''')
        
        logger.info("Core __init__.py files created")
        return True
        
    except Exception as e:
        logger.error(f"Failed to create __init__.py files: {e}")
        return False

def validate_core_reorganization(project_root: Path) -> bool:
    """Validate that core reorganization was successful."""
    try:
        logger.info("Validating core module reorganization...")
        
        # Check that key files exist in new locations
        key_files = [
            'ecocycle/core/database/manager.py',
            'ecocycle/core/dependency/manager.py',
            'ecocycle/core/error/handler.py',
            'ecocycle/core/logging/manager.py',
            'ecocycle/core/plugin/manager.py',
            'ecocycle/core/plugin/loader.py',
        ]
        
        for file_path in key_files:
            full_path = project_root / file_path
            if not full_path.exists():
                logger.error(f"Missing file: {file_path}")
                return False
            logger.info(f"✓ File exists: {file_path}")
        
        # Try to run a basic test
        logger.info("Running basic functionality test...")
        result = subprocess.run([
            sys.executable, '-c', 
            'import sys; sys.path.insert(0, "."); import ecocycle.core.database.manager; print("Core imports working")'
        ], cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✓ Core module imports working")
            return True
        else:
            logger.error(f"✗ Core module import test failed: {result.stderr}")
            return False
        
    except Exception as e:
        logger.error(f"Validation failed: {e}")
        return False

def main():
    """Main function for batch 1 refactoring."""
    project_root = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    try:
        # Step 1: Move core modules
        if not move_core_modules(project_root):
            logger.error("Failed to move core modules")
            return False
        
        # Step 2: Create __init__.py files
        if not create_core_init_files(project_root):
            logger.error("Failed to create __init__.py files")
            return False
        
        # Step 3: Update imports
        if not update_core_imports(project_root):
            logger.error("Failed to update imports")
            return False
        
        # Step 4: Validate
        if not validate_core_reorganization(project_root):
            logger.error("Validation failed")
            return False
        
        logger.info("✓ Batch 1: Core Module Reorganization completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Batch 1 refactoring failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
