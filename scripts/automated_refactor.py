#!/usr/bin/env python3
"""
EcoCycle Automated Refactoring Script
Safely reorganizes the codebase structure with validation at each step.
"""

import os
import sys
import shutil
import json
import subprocess
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import re

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('Logs/refactor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EcoCycleRefactor:
    """Automated refactoring tool for EcoCycle codebase."""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root / "refactor_backup_new"
        self.new_structure = self.project_root / "ecocycle"
        self.validation_results = []
        
    def create_backup(self) -> bool:
        """Create a backup of the current codebase."""
        try:
            if self.backup_dir.exists():
                shutil.rmtree(self.backup_dir)
            
            logger.info(f"Creating backup at {self.backup_dir}")
            
            # Copy important files and directories
            important_items = [
                'apps', 'auth', 'core', 'utils', 'services', 'models', 
                'controllers', 'views', 'config', 'tests', 'web',
                'main.py', 'cli.py', 'mini.py', 'setup.py', 
                'pyproject.toml', 'requirements.txt'
            ]
            
            self.backup_dir.mkdir(exist_ok=True)
            
            for item in important_items:
                src = self.project_root / item
                if src.exists():
                    dst = self.backup_dir / item
                    if src.is_dir():
                        shutil.copytree(src, dst)
                    else:
                        shutil.copy2(src, dst)
                    logger.info(f"Backed up: {item}")
            
            return True
            
        except Exception as e:
            logger.error(f"Backup failed: {e}")
            return False
    
    def create_new_structure(self) -> bool:
        """Create the new directory structure."""
        try:
            logger.info("Creating new directory structure")
            
            # Define new structure
            new_dirs = [
                'ecocycle',
                'ecocycle/cli',
                'ecocycle/cli/commands',
                'ecocycle/core',
                'ecocycle/core/database',
                'ecocycle/core/dependency',
                'ecocycle/core/error',
                'ecocycle/core/logging',
                'ecocycle/core/plugin',
                'ecocycle/apps',
                'ecocycle/apps/menu',
                'ecocycle/apps/carbon_footprint',
                'ecocycle/apps/route_planner',
                'ecocycle/apps/data_viz',
                'ecocycle/apps/challenges',
                'ecocycle/apps/gamification',
                'ecocycle/auth',
                'ecocycle/auth/user_management',
                'ecocycle/auth/oauth',
                'ecocycle/services',
                'ecocycle/services/weather',
                'ecocycle/services/sheets',
                'ecocycle/services/notifications',
                'ecocycle/services/sync',
                'ecocycle/models',
                'ecocycle/utils',
                'ecocycle/config',
                'ecocycle/web',
                'ecocycle/web/api',
                'tests/unit',
                'tests/integration',
                'tests/fixtures'
            ]
            
            # Create directories
            for dir_path in new_dirs:
                full_path = self.project_root / dir_path
                full_path.mkdir(parents=True, exist_ok=True)
                
                # Create __init__.py files
                init_file = full_path / '__init__.py'
                if not init_file.exists():
                    init_file.write_text('"""EcoCycle module."""\n')
            
            logger.info("New directory structure created")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create new structure: {e}")
            return False
    
    def move_files_batch(self, file_moves: List[Tuple[str, str]]) -> bool:
        """Move files in a batch with validation."""
        try:
            logger.info(f"Moving {len(file_moves)} files")
            
            for src_path, dst_path in file_moves:
                src = self.project_root / src_path
                dst = self.project_root / dst_path
                
                if src.exists():
                    # Ensure destination directory exists
                    dst.parent.mkdir(parents=True, exist_ok=True)
                    
                    # Move the file
                    shutil.move(str(src), str(dst))
                    logger.info(f"Moved: {src_path} -> {dst_path}")
                else:
                    logger.warning(f"Source file not found: {src_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"File move batch failed: {e}")
            return False
    
    def update_imports_in_file(self, file_path: Path, import_mappings: Dict[str, str]) -> bool:
        """Update import statements in a single file."""
        try:
            if not file_path.exists() or not file_path.suffix == '.py':
                return True
            
            content = file_path.read_text()
            original_content = content
            
            # Update imports based on mappings
            for old_import, new_import in import_mappings.items():
                # Handle different import patterns
                patterns = [
                    (rf'^import {re.escape(old_import)}$', f'import {new_import}'),
                    (rf'^from {re.escape(old_import)} import', f'from {new_import} import'),
                    (rf'import {re.escape(old_import)}\.', f'import {new_import}.'),
                    (rf'from {re.escape(old_import)}\.', f'from {new_import}.'),
                ]
                
                for pattern, replacement in patterns:
                    content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
            
            # Only write if content changed
            if content != original_content:
                file_path.write_text(content)
                logger.info(f"Updated imports in: {file_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update imports in {file_path}: {e}")
            return False
    
    def validate_imports(self) -> bool:
        """Validate that imports work after refactoring."""
        try:
            logger.info("Validating imports...")
            
            # Try importing key modules
            test_imports = [
                'ecocycle.core.database_manager',
                'ecocycle.apps.menu',
                'ecocycle.auth.user_management.user_manager',
                'ecocycle.utils.app_functions'
            ]
            
            for module_name in test_imports:
                try:
                    __import__(module_name)
                    logger.info(f"✓ Import successful: {module_name}")
                except ImportError as e:
                    logger.error(f"✗ Import failed: {module_name} - {e}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Import validation failed: {e}")
            return False
    
    def run_tests(self) -> bool:
        """Run the test suite to validate functionality."""
        try:
            logger.info("Running test suite...")
            
            # Run basic functionality tests
            result = subprocess.run([
                sys.executable, '-m', 'unittest', 
                'tests.test_working_functionality', '-v'
            ], cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✓ Tests passed")
                return True
            else:
                logger.error(f"✗ Tests failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Test execution failed: {e}")
            return False
    
    def rollback(self) -> bool:
        """Rollback changes if validation fails."""
        try:
            logger.warning("Rolling back changes...")
            
            # Remove new structure if it exists
            if self.new_structure.exists():
                shutil.rmtree(self.new_structure)
            
            # Restore from backup
            if self.backup_dir.exists():
                for item in self.backup_dir.iterdir():
                    dst = self.project_root / item.name
                    if dst.exists():
                        if dst.is_dir():
                            shutil.rmtree(dst)
                        else:
                            dst.unlink()
                    
                    if item.is_dir():
                        shutil.copytree(item, dst)
                    else:
                        shutil.copy2(item, dst)
            
            logger.info("Rollback completed")
            return True
            
        except Exception as e:
            logger.error(f"Rollback failed: {e}")
            return False

def main():
    """Main refactoring function."""
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    refactor = EcoCycleRefactor(project_root)
    
    try:
        # Step 1: Create backup
        if not refactor.create_backup():
            logger.error("Backup failed, aborting refactoring")
            return False
        
        # Step 2: Create new structure
        if not refactor.create_new_structure():
            logger.error("Failed to create new structure")
            return False
        
        logger.info("Refactoring preparation completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Refactoring failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
