#!/usr/bin/env python3
"""
EcoCycle Batch 3: Authentication & Services Reorganization
Moves and reorganizes auth and services modules with validation.
"""

import os
import sys
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import subprocess

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('Logs/refactor.log', mode='a'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def move_auth_services_modules(project_root: Path) -> bool:
    """Move auth and services modules to new structure."""
    try:
        logger.info("Starting Batch 3: Authentication & Services Reorganization")
        
        # Define file moves for auth and services modules
        moves = [
            # Authentication modules
            ('auth/', 'ecocycle/auth/'),  # Directory
            
            # Services modules
            ('services/', 'ecocycle/services/'),  # Directory
        ]
        
        # Move directories
        for src_path, dst_path in moves:
            src = project_root / src_path
            dst = project_root / dst_path
            
            if src.exists():
                # Ensure destination directory exists
                dst.parent.mkdir(parents=True, exist_ok=True)
                
                if src.is_dir():
                    # Copy directory
                    if dst.exists():
                        shutil.rmtree(dst)
                    shutil.copytree(str(src), str(dst))
                    logger.info(f"Copied directory: {src_path} -> {dst_path}")
                else:
                    # Copy file
                    shutil.copy2(str(src), str(dst))
                    logger.info(f"Copied file: {src_path} -> {dst_path}")
            else:
                logger.warning(f"Source not found: {src_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"Auth/Services module move failed: {e}")
        return False

def create_auth_services_init_files(project_root: Path) -> bool:
    """Create proper __init__.py files for auth and services modules."""
    try:
        logger.info("Creating auth and services module __init__.py files...")
        
        # Main auth __init__.py
        auth_init = project_root / 'ecocycle/auth/__init__.py'
        if not auth_init.exists():
            auth_init.write_text('''"""EcoCycle authentication modules."""
from . import user_management
from . import oauth
''')
        
        # Main services __init__.py
        services_init = project_root / 'ecocycle/services/__init__.py'
        if not services_init.exists():
            services_init.write_text('''"""EcoCycle services modules."""
from . import weather
from . import sheets
from . import notifications
from . import sync
''')
        
        logger.info("Auth and services __init__.py files created")
        return True
        
    except Exception as e:
        logger.error(f"Failed to create auth/services __init__.py files: {e}")
        return False

def update_auth_services_imports(project_root: Path) -> bool:
    """Update import statements for moved auth and services modules."""
    try:
        logger.info("Updating auth and services module imports...")
        
        # Import mappings for auth and services modules
        import_mappings = {
            'auth.user_management': 'ecocycle.auth.user_management',
            'auth.oauth': 'ecocycle.auth.oauth',
            'auth.developer_auth': 'ecocycle.auth.developer_auth',
            'auth.email_verification': 'ecocycle.auth.email_verification',
            'services.weather': 'ecocycle.services.weather',
            'services.sheets': 'ecocycle.services.sheets',
            'services.notifications': 'ecocycle.services.notifications',
            'services.sync': 'ecocycle.services.sync',
        }
        
        # Update imports in new ecocycle directory
        ecocycle_dir = project_root / 'ecocycle'
        if ecocycle_dir.exists():
            for py_file in ecocycle_dir.rglob('*.py'):
                update_imports_in_file(py_file, import_mappings)
        
        # Update imports in remaining files at root level
        root_files = ['main.py', 'cli.py', 'mini.py']
        for file_name in root_files:
            file_path = project_root / file_name
            if file_path.exists():
                update_imports_in_file(file_path, import_mappings)
        
        return True
        
    except Exception as e:
        logger.error(f"Auth/Services import update failed: {e}")
        return False

def update_imports_in_file(file_path: Path, import_mappings: Dict[str, str]) -> bool:
    """Update import statements in a single file."""
    try:
        if not file_path.exists() or file_path.suffix != '.py':
            return True
        
        content = file_path.read_text()
        original_content = content
        
        # Update imports based on mappings
        for old_import, new_import in import_mappings.items():
            # Handle different import patterns
            import re
            patterns = [
                (rf'^import {re.escape(old_import)}$', f'import {new_import}'),
                (rf'^from {re.escape(old_import)} import', f'from {new_import} import'),
                (rf'^import {re.escape(old_import)} as', f'import {new_import} as'),
                (rf'import {re.escape(old_import)}\.', f'import {new_import}.'),
                (rf'from {re.escape(old_import)}\.', f'from {new_import}.'),
            ]
            
            for pattern, replacement in patterns:
                content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        # Only write if content changed
        if content != original_content:
            file_path.write_text(content)
            logger.info(f"Updated imports in: {file_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to update imports in {file_path}: {e}")
        return False

def validate_auth_services_reorganization(project_root: Path) -> bool:
    """Validate that auth and services reorganization was successful."""
    try:
        logger.info("Validating auth and services module reorganization...")
        
        # Check that key directories exist in new locations
        key_dirs = [
            'ecocycle/auth',
            'ecocycle/auth/user_management',
            'ecocycle/services',
            'ecocycle/services/weather',
            'ecocycle/services/sheets',
            'ecocycle/services/notifications',
            'ecocycle/services/sync',
        ]
        
        for dir_path in key_dirs:
            full_path = project_root / dir_path
            if not full_path.exists():
                logger.error(f"Missing directory: {dir_path}")
                return False
            logger.info(f"✓ Directory exists: {dir_path}")
        
        # Try to import auth and services modules
        logger.info("Testing auth and services module imports...")
        result = subprocess.run([
            sys.executable, '-c', 
            'import sys; sys.path.insert(0, "."); import ecocycle.auth; import ecocycle.services; print("Auth and services imports working")'
        ], cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✓ Auth and services module imports working")
            return True
        else:
            logger.error(f"✗ Auth and services module import test failed: {result.stderr}")
            return False
        
    except Exception as e:
        logger.error(f"Auth/Services validation failed: {e}")
        return False

def main():
    """Main function for batch 3 refactoring."""
    project_root = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    try:
        # Step 1: Move auth and services modules
        if not move_auth_services_modules(project_root):
            logger.error("Failed to move auth and services modules")
            return False
        
        # Step 2: Create __init__.py files
        if not create_auth_services_init_files(project_root):
            logger.error("Failed to create auth/services __init__.py files")
            return False
        
        # Step 3: Update imports
        if not update_auth_services_imports(project_root):
            logger.error("Failed to update auth/services imports")
            return False
        
        # Step 4: Validate
        if not validate_auth_services_reorganization(project_root):
            logger.error("Auth/Services validation failed")
            return False
        
        logger.info("✓ Batch 3: Authentication & Services Reorganization completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Batch 3 refactoring failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
