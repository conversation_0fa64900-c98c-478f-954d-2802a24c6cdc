#!/usr/bin/env python3
"""
EcoCycle Batch 5: Entry Points & CLI Reorganization
Creates proper entry points and CLI structure.
"""

import os
import sys
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import subprocess

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('Logs/refactor.log', mode='a'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_cli_structure(project_root: Path) -> bool:
    """Create proper CLI structure."""
    try:
        logger.info("Starting Batch 5: Entry Points & CLI Reorganization")
        
        # Create CLI directory structure
        cli_dir = project_root / 'ecocycle/cli'
        cli_dir.mkdir(parents=True, exist_ok=True)
        
        commands_dir = cli_dir / 'commands'
        commands_dir.mkdir(parents=True, exist_ok=True)
        
        # Copy CLI files to new structure
        cli_moves = [
            ('cli.py', 'ecocycle/cli/main.py'),
        ]
        
        for src_path, dst_path in cli_moves:
            src = project_root / src_path
            dst = project_root / dst_path
            
            if src.exists():
                shutil.copy2(str(src), str(dst))
                logger.info(f"Copied: {src_path} -> {dst_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"CLI structure creation failed: {e}")
        return False

def create_main_entry_point(project_root: Path) -> bool:
    """Create main entry point for the package."""
    try:
        logger.info("Creating main entry point...")
        
        # Create ecocycle/__main__.py for python -m ecocycle
        main_entry = project_root / 'ecocycle/__main__.py'
        main_entry.write_text('''#!/usr/bin/env python3
"""
EcoCycle - Main Entry Point
Entry point for running EcoCycle as a module: python -m ecocycle
"""

import sys
import os

# Add the parent directory to the path so we can import from ecocycle
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    """Main entry point for the EcoCycle package."""
    try:
        # Import the main application
        from ecocycle.cli.main import main as cli_main
        
        # Run the CLI
        cli_main()
        
    except ImportError as e:
        print(f"Error importing EcoCycle modules: {e}")
        print("Please ensure EcoCycle is properly installed.")
        sys.exit(1)
    except Exception as e:
        print(f"Error running EcoCycle: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
''')
        
        # Create ecocycle/__init__.py with proper package structure
        package_init = project_root / 'ecocycle/__init__.py'
        package_init.write_text('''"""
EcoCycle - Cycle Into A Greener Tomorrow
A comprehensive cycling tracking and environmental impact application.
"""

__version__ = "3.4.0"
__author__ = "EcoCycle Team"
__email__ = "<EMAIL>"

# Import main components for easy access
from . import core
from . import apps
from . import auth
from . import services
from . import utils
from . import config
from . import models
from . import web

# Main entry point function
def main():
    """Main entry point for EcoCycle."""
    from .cli.main import main as cli_main
    return cli_main()

__all__ = [
    'core', 'apps', 'auth', 'services', 'utils', 
    'config', 'models', 'web', 'main'
]
''')
        
        # Create CLI __init__.py
        cli_init = project_root / 'ecocycle/cli/__init__.py'
        cli_init.write_text('''"""EcoCycle CLI module."""
from .main import main

__all__ = ['main']
''')
        
        logger.info("Entry points created successfully")
        return True
        
    except Exception as e:
        logger.error(f"Entry point creation failed: {e}")
        return False

def update_root_entry_points(project_root: Path) -> bool:
    """Update root-level entry points to use new structure."""
    try:
        logger.info("Updating root-level entry points...")
        
        # Update main.py to use new structure
        main_py = project_root / 'main.py'
        if main_py.exists():
            main_py.write_text('''#!/usr/bin/env python3
"""
EcoCycle - Legacy Entry Point
This file is maintained for backward compatibility.
For new installations, use: python -m ecocycle
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """Legacy main entry point."""
    try:
        # Import from new structure
        from ecocycle import main as ecocycle_main
        return ecocycle_main()
    except ImportError as e:
        print(f"Error importing EcoCycle: {e}")
        print("Please ensure EcoCycle is properly installed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
''')
        
        # Update mini.py to use new structure
        mini_py = project_root / 'mini.py'
        if mini_py.exists():
            mini_py.write_text('''#!/usr/bin/env python3
"""
EcoCycle - Minimal Entry Point
Minimal entry point that uses the new package structure.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """Minimal entry point."""
    try:
        # Use new package structure
        from ecocycle import main as ecocycle_main
        return ecocycle_main()
    except ImportError as e:
        print(f"Error importing EcoCycle: {e}")
        return 1
    except Exception as e:
        print(f"Error running EcoCycle: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
''')
        
        logger.info("Root entry points updated")
        return True
        
    except Exception as e:
        logger.error(f"Root entry point update failed: {e}")
        return False

def validate_entry_points(project_root: Path) -> bool:
    """Validate that entry points work correctly."""
    try:
        logger.info("Validating entry points...")
        
        # Check that key files exist
        key_files = [
            'ecocycle/__init__.py',
            'ecocycle/__main__.py',
            'ecocycle/cli/__init__.py',
            'ecocycle/cli/main.py',
        ]
        
        for file_path in key_files:
            full_path = project_root / file_path
            if not full_path.exists():
                logger.error(f"Missing file: {file_path}")
                return False
            logger.info(f"✓ File exists: {file_path}")
        
        # Test package import
        logger.info("Testing package import...")
        result = subprocess.run([
            sys.executable, '-c', 
            'import sys; sys.path.insert(0, "."); import ecocycle; print("Package import successful")'
        ], cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✓ Package import working")
        else:
            logger.error(f"✗ Package import failed: {result.stderr}")
            return False
        
        # Test module execution (just import, don't run)
        logger.info("Testing module execution...")
        result = subprocess.run([
            sys.executable, '-c', 
            'import sys; sys.path.insert(0, "."); from ecocycle.__main__ import main; print("Module execution setup working")'
        ], cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✓ Module execution setup working")
            return True
        else:
            logger.error(f"✗ Module execution test failed: {result.stderr}")
            return False
        
    except Exception as e:
        logger.error(f"Entry point validation failed: {e}")
        return False

def main():
    """Main function for batch 5 refactoring."""
    project_root = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    try:
        # Step 1: Create CLI structure
        if not create_cli_structure(project_root):
            logger.error("Failed to create CLI structure")
            return False
        
        # Step 2: Create main entry point
        if not create_main_entry_point(project_root):
            logger.error("Failed to create main entry point")
            return False
        
        # Step 3: Update root entry points
        if not update_root_entry_points(project_root):
            logger.error("Failed to update root entry points")
            return False
        
        # Step 4: Validate
        if not validate_entry_points(project_root):
            logger.error("Entry point validation failed")
            return False
        
        logger.info("✓ Batch 5: Entry Points & CLI Reorganization completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Batch 5 refactoring failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
