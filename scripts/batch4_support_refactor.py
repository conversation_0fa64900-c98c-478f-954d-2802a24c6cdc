#!/usr/bin/env python3
"""
EcoCycle Batch 4: Support Modules Reorganization
Moves and reorganizes utils, config, models, controllers, views modules with validation.
"""

import os
import sys
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import subprocess

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('Logs/refactor.log', mode='a'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def move_support_modules(project_root: Path) -> bool:
    """Move support modules to new structure."""
    try:
        logger.info("Starting Batch 4: Support Modules Reorganization")
        
        # Define file moves for support modules
        moves = [
            # Utils modules
            ('utils/', 'ecocycle/utils/'),  # Directory
            
            # Config modules
            ('config/', 'ecocycle/config/'),  # Directory
            
            # Models modules
            ('models/', 'ecocycle/models/'),  # Directory
            
            # Web modules
            ('web/', 'ecocycle/web/'),  # Directory
        ]
        
        # Move directories
        for src_path, dst_path in moves:
            src = project_root / src_path
            dst = project_root / dst_path
            
            if src.exists():
                # Ensure destination directory exists
                dst.parent.mkdir(parents=True, exist_ok=True)
                
                if src.is_dir():
                    # Copy directory
                    if dst.exists():
                        shutil.rmtree(dst)
                    shutil.copytree(str(src), str(dst))
                    logger.info(f"Copied directory: {src_path} -> {dst_path}")
                else:
                    # Copy file
                    shutil.copy2(str(src), str(dst))
                    logger.info(f"Copied file: {src_path} -> {dst_path}")
            else:
                logger.warning(f"Source not found: {src_path}")
        
        # Handle controllers and views separately (they'll be integrated into other modules)
        controllers_src = project_root / 'controllers'
        views_src = project_root / 'views'
        
        if controllers_src.exists():
            controllers_dst = project_root / 'ecocycle/controllers'
            controllers_dst.mkdir(parents=True, exist_ok=True)
            shutil.copytree(str(controllers_src), str(controllers_dst), dirs_exist_ok=True)
            logger.info("Copied controllers directory")
        
        if views_src.exists():
            views_dst = project_root / 'ecocycle/views'
            views_dst.mkdir(parents=True, exist_ok=True)
            shutil.copytree(str(views_src), str(views_dst), dirs_exist_ok=True)
            logger.info("Copied views directory")
        
        return True
        
    except Exception as e:
        logger.error(f"Support modules move failed: {e}")
        return False

def create_support_init_files(project_root: Path) -> bool:
    """Create proper __init__.py files for support modules."""
    try:
        logger.info("Creating support modules __init__.py files...")
        
        # Utils __init__.py
        utils_init = project_root / 'ecocycle/utils/__init__.py'
        if not utils_init.exists():
            utils_init.write_text('''"""EcoCycle utility modules."""
from . import app_functions
from . import ascii_art
from . import general_utils
''')
        
        # Config __init__.py
        config_init = project_root / 'ecocycle/config/__init__.py'
        if not config_init.exists():
            config_init.write_text('''"""EcoCycle configuration modules."""
from .config import *
from .config_manager import *
''')
        
        # Models __init__.py
        models_init = project_root / 'ecocycle/models/__init__.py'
        if not models_init.exists():
            models_init.write_text('''"""EcoCycle data models."""
from . import user
from . import trip
from . import route
from . import statistics
''')
        
        # Web __init__.py
        web_init = project_root / 'ecocycle/web/__init__.py'
        if not web_init.exists():
            web_init.write_text('''"""EcoCycle web interface."""
from . import web_app
from . import api_endpoints
''')
        
        # Controllers __init__.py
        controllers_init = project_root / 'ecocycle/controllers/__init__.py'
        controllers_init.write_text('''"""EcoCycle controllers."""
# Controllers will be gradually integrated into their respective app modules
''')
        
        # Views __init__.py
        views_init = project_root / 'ecocycle/views/__init__.py'
        views_init.write_text('''"""EcoCycle views."""
# Views will be gradually integrated into their respective app modules
''')
        
        logger.info("Support modules __init__.py files created")
        return True
        
    except Exception as e:
        logger.error(f"Failed to create support __init__.py files: {e}")
        return False

def update_support_imports(project_root: Path) -> bool:
    """Update import statements for moved support modules."""
    try:
        logger.info("Updating support modules imports...")
        
        # Import mappings for support modules
        import_mappings = {
            'utils.app_functions': 'ecocycle.utils.app_functions',
            'utils.ascii_art': 'ecocycle.utils.ascii_art',
            'utils.general_utils': 'ecocycle.utils.general_utils',
            'utils.safe_input': 'ecocycle.utils.safe_input',
            'utils.theme_manager': 'ecocycle.utils.theme_manager',
            'utils.localization': 'ecocycle.utils.localization',
            'config.config': 'ecocycle.config.config',
            'config.config_manager': 'ecocycle.config.config_manager',
            'models.user': 'ecocycle.models.user',
            'models.trip': 'ecocycle.models.trip',
            'models.route': 'ecocycle.models.route',
            'models.statistics': 'ecocycle.models.statistics',
            'models.eco_tip': 'ecocycle.models.eco_tip',
            'models.feedback': 'ecocycle.models.feedback',
            'models.metrics': 'ecocycle.models.metrics',
            'models.routes': 'ecocycle.models.routes',
            'models.trips': 'ecocycle.models.trips',
            'models.weather_data': 'ecocycle.models.weather_data',
            'web.web_app': 'ecocycle.web.web_app',
            'web.api_endpoints': 'ecocycle.web.api_endpoints',
            'web.forum_api': 'ecocycle.web.forum_api',
            'controllers': 'ecocycle.controllers',
            'views': 'ecocycle.views',
        }
        
        # Update imports in new ecocycle directory
        ecocycle_dir = project_root / 'ecocycle'
        if ecocycle_dir.exists():
            for py_file in ecocycle_dir.rglob('*.py'):
                update_imports_in_file(py_file, import_mappings)
        
        # Update imports in remaining files at root level
        root_files = ['main.py', 'cli.py', 'mini.py']
        for file_name in root_files:
            file_path = project_root / file_name
            if file_path.exists():
                update_imports_in_file(file_path, import_mappings)
        
        return True
        
    except Exception as e:
        logger.error(f"Support import update failed: {e}")
        return False

def update_imports_in_file(file_path: Path, import_mappings: Dict[str, str]) -> bool:
    """Update import statements in a single file."""
    try:
        if not file_path.exists() or file_path.suffix != '.py':
            return True
        
        content = file_path.read_text()
        original_content = content
        
        # Update imports based on mappings
        for old_import, new_import in import_mappings.items():
            # Handle different import patterns
            import re
            patterns = [
                (rf'^import {re.escape(old_import)}$', f'import {new_import}'),
                (rf'^from {re.escape(old_import)} import', f'from {new_import} import'),
                (rf'^import {re.escape(old_import)} as', f'import {new_import} as'),
                (rf'import {re.escape(old_import)}\.', f'import {new_import}.'),
                (rf'from {re.escape(old_import)}\.', f'from {new_import}.'),
            ]
            
            for pattern, replacement in patterns:
                content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        # Only write if content changed
        if content != original_content:
            file_path.write_text(content)
            logger.info(f"Updated imports in: {file_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to update imports in {file_path}: {e}")
        return False

def validate_support_reorganization(project_root: Path) -> bool:
    """Validate that support reorganization was successful."""
    try:
        logger.info("Validating support modules reorganization...")
        
        # Check that key directories exist in new locations
        key_dirs = [
            'ecocycle/utils',
            'ecocycle/config',
            'ecocycle/models',
            'ecocycle/web',
        ]
        
        for dir_path in key_dirs:
            full_path = project_root / dir_path
            if not full_path.exists():
                logger.error(f"Missing directory: {dir_path}")
                return False
            logger.info(f"✓ Directory exists: {dir_path}")
        
        # Try to import support modules
        logger.info("Testing support module imports...")
        result = subprocess.run([
            sys.executable, '-c', 
            'import sys; sys.path.insert(0, "."); import ecocycle.utils; import ecocycle.config; import ecocycle.models; print("Support imports working")'
        ], cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✓ Support module imports working")
            return True
        else:
            logger.error(f"✗ Support module import test failed: {result.stderr}")
            return False
        
    except Exception as e:
        logger.error(f"Support validation failed: {e}")
        return False

def main():
    """Main function for batch 4 refactoring."""
    project_root = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    try:
        # Step 1: Move support modules
        if not move_support_modules(project_root):
            logger.error("Failed to move support modules")
            return False
        
        # Step 2: Create __init__.py files
        if not create_support_init_files(project_root):
            logger.error("Failed to create support __init__.py files")
            return False
        
        # Step 3: Update imports
        if not update_support_imports(project_root):
            logger.error("Failed to update support imports")
            return False
        
        # Step 4: Validate
        if not validate_support_reorganization(project_root):
            logger.error("Support validation failed")
            return False
        
        logger.info("✓ Batch 4: Support Modules Reorganization completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Batch 4 refactoring failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
