#!/usr/bin/env python3
"""
EcoCycle - Developer Mode Setup Script
Helps users set up developer credentials for debug mode access.
"""
import os
import sys
import getpass
import hashlib

# Add the parent directory to the path so we can import from auth
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from auth.developer_auth import DeveloperAuth


def main():
    """Main setup function."""
    print("🔧 EcoCycle Developer Mode Setup")
    print("=" * 50)
    print("This script will help you set up developer credentials for EcoCycle.")
    print("Developer mode provides advanced debugging and system management tools.")
    print()
    
    # Check if developer mode is already configured
    if os.environ.get('DEVELOPER_MODE_ENABLED') == 'true':
        print("⚠️  Developer mode is already enabled.")
        response = input("Do you want to reconfigure it? (y/N): ").strip().lower()
        if response != 'y':
            print("Setup cancelled.")
            return
    
    print("Setting up developer credentials...")
    print()
    
    # Get username
    default_username = "dev_admin"
    username = input(f"Enter developer username [{default_username}]: ").strip()
    if not username:
        username = default_username
    
    # Get password
    while True:
        password = getpass.getpass("Enter developer password: ")
        if len(password) < 12:
            print("⚠️  Password should be at least 12 characters for security.")
            continue
        
        confirm_password = getpass.getpass("Confirm developer password: ")
        if password != confirm_password:
            print("❌ Passwords do not match. Please try again.")
            continue
        
        break
    
    # Generate password hash
    print("\n🔐 Generating secure password hash...")
    password_hash = DeveloperAuth.generate_password_hash(password)
    
    # Create environment variables
    env_vars = {
        'DEVELOPER_MODE_ENABLED': 'true',
        'DEVELOPER_USERNAME': username,
        'DEVELOPER_PASSWORD_HASH': password_hash
    }
    
    print("\n✅ Developer credentials generated!")
    print("\nEnvironment Variables to Set:")
    print("-" * 40)
    for key, value in env_vars.items():
        print(f"{key}={value}")
    
    print("\n📝 Setup Instructions:")
    print("1. Add the above environment variables to your system")
    print("2. You can add them to a .env file in the project root")
    print("3. Or export them in your shell:")
    print()
    for key, value in env_vars.items():
        print(f"   export {key}='{value}'")
    
    print("\n🔒 Security Notes:")
    print("- Keep these credentials secure and do not commit them to version control")
    print("- The password hash is salted and secure")
    print("- Developer mode provides elevated system privileges")
    print("- Sessions timeout after 30 minutes of inactivity")
    
    # Offer to create .env file
    print()
    response = input("Would you like to create/update a .env file with these variables? (y/N): ").strip().lower()
    if response == 'y':
        create_env_file(env_vars)
    
    print("\n🎉 Developer mode setup complete!")
    print("You can now access developer mode from the authentication menu.")


def create_env_file(env_vars):
    """Create or update .env file with developer variables."""
    env_file_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    
    # Read existing .env file if it exists
    existing_vars = {}
    if os.path.exists(env_file_path):
        try:
            with open(env_file_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        existing_vars[key] = value
        except Exception as e:
            print(f"⚠️  Warning: Could not read existing .env file: {e}")
    
    # Update with new developer variables
    existing_vars.update(env_vars)
    
    # Write updated .env file
    try:
        with open(env_file_path, 'w') as f:
            f.write("# EcoCycle Environment Variables\n")
            f.write("# Generated by setup_developer_mode.py\n\n")
            
            # Write developer variables first
            f.write("# Developer Mode Configuration\n")
            for key in ['DEVELOPER_MODE_ENABLED', 'DEVELOPER_USERNAME', 'DEVELOPER_PASSWORD_HASH']:
                if key in existing_vars:
                    f.write(f"{key}={existing_vars[key]}\n")
            
            f.write("\n# Other Configuration\n")
            for key, value in existing_vars.items():
                if not key.startswith('DEVELOPER_'):
                    f.write(f"{key}={value}\n")
        
        print(f"✅ .env file created/updated at: {env_file_path}")
        
        # Set file permissions (Unix-like systems)
        if os.name != 'nt':
            try:
                os.chmod(env_file_path, 0o600)  # Read/write for owner only
                print("🔒 File permissions set to owner-only access")
            except Exception as e:
                print(f"⚠️  Warning: Could not set file permissions: {e}")
    
    except Exception as e:
        print(f"❌ Error creating .env file: {e}")
        print("Please manually create the .env file with the provided variables.")


def test_developer_auth():
    """Test the developer authentication setup."""
    print("\n🧪 Testing developer authentication...")
    
    try:
        dev_auth = DeveloperAuth()
        
        if not dev_auth.is_enabled():
            print("❌ Developer mode is not enabled or not configured properly.")
            return False
        
        print("✅ Developer mode is enabled and configured.")
        print(f"   Username: {dev_auth.dev_username}")
        print(f"   Session timeout: {dev_auth.session_timeout} seconds")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing developer authentication: {e}")
        return False


if __name__ == "__main__":
    try:
        main()
        
        # Test the setup
        if test_developer_auth():
            print("\n🎯 Setup verification successful!")
        else:
            print("\n⚠️  Setup verification failed. Please check your configuration.")
            
    except KeyboardInterrupt:
        print("\n\nSetup cancelled by user.")
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)
