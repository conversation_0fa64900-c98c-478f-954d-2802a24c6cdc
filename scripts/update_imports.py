#!/usr/bin/env python3
"""
Update Import Statements
Script to update import statements after directory reorganization.
"""

import os
import sys
import re
from pathlib import Path
from typing import Dict, List, Set
import logging

# Add project root to path
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

class ImportUpdater:
    """Updates import statements after reorganization."""
    
    def __init__(self, dry_run: bool = True):
        self.dry_run = dry_run
        self.changes_made = []
        self.setup_logging()
        
        # Define import mappings
        self.import_mappings = {
            'tests': 'tests',
            'examples': 'examples',
            'from tests.': 'from tests.',
            'import tests.': 'import tests.',
            'from examples.': 'from examples.',
            'import examples.': 'import examples.',
        }
    
    def setup_logging(self):
        """Set up logging."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def find_python_files(self) -> List[Path]:
        """Find all Python files in the project."""
        python_files = []
        
        # Search in main directories
        search_dirs = [
            'core', 'apps', 'auth', 'utils', 'config',
            'models', 'views', 'controllers', 'services',
            'tests', 'examples', 'scripts'
        ]
        
        for dir_name in search_dirs:
            dir_path = PROJECT_ROOT / dir_name
            if dir_path.exists():
                python_files.extend(dir_path.rglob('*.py'))
        
        # Also check root level Python files
        for file_path in PROJECT_ROOT.glob('*.py'):
            python_files.append(file_path)
        
        return python_files
    
    def update_file_imports(self, file_path: Path) -> bool:
        """Update imports in a single file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            changes_in_file = []
            
            # Apply each import mapping
            for old_pattern, new_pattern in self.import_mappings.items():
                # Use word boundaries to avoid partial matches
                if old_pattern.startswith('from ') or old_pattern.startswith('import '):
                    # Direct pattern replacement for full import statements
                    pattern = re.escape(old_pattern)
                    if re.search(pattern, content):
                        content = re.sub(pattern, new_pattern, content)
                        changes_in_file.append(f"{old_pattern} -> {new_pattern}")
                else:
                    # Pattern replacement for module names
                    # Match word boundaries to avoid partial replacements
                    pattern = r'\b' + re.escape(old_pattern) + r'\b'
                    if re.search(pattern, content):
                        content = re.sub(pattern, new_pattern, content)
                        changes_in_file.append(f"{old_pattern} -> {new_pattern}")
            
            # Only write if content changed
            if content != original_content:
                if not self.dry_run:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                
                self.changes_made.append({
                    'file': str(file_path.relative_to(PROJECT_ROOT)),
                    'changes': changes_in_file
                })
                
                self.logger.info(f"Updated imports in {file_path.relative_to(PROJECT_ROOT)}")
                for change in changes_in_file:
                    self.logger.info(f"  {change}")
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to update {file_path}: {e}")
            return False
    
    def update_all_imports(self) -> bool:
        """Update imports in all Python files."""
        python_files = self.find_python_files()
        self.logger.info(f"Found {len(python_files)} Python files to check")
        
        success_count = 0
        error_count = 0
        
        for file_path in python_files:
            try:
                if self.update_file_imports(file_path):
                    success_count += 1
            except Exception as e:
                self.logger.error(f"Error processing {file_path}: {e}")
                error_count += 1
        
        self.logger.info(f"Import update completed:")
        self.logger.info(f"  Files updated: {success_count}")
        self.logger.info(f"  Files with errors: {error_count}")
        self.logger.info(f"  Total changes made: {len(self.changes_made)}")
        
        return error_count == 0
    
    def validate_imports(self) -> List[str]:
        """Validate that all imports are working."""
        errors = []
        python_files = self.find_python_files()
        
        for file_path in python_files:
            try:
                # Try to compile the file to check for syntax errors
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                compile(content, str(file_path), 'exec')
                
            except SyntaxError as e:
                errors.append(f"Syntax error in {file_path}: {e}")
            except Exception as e:
                errors.append(f"Error validating {file_path}: {e}")
        
        return errors
    
    def print_summary(self):
        """Print a summary of changes made."""
        if not self.changes_made:
            print("No import changes were needed.")
            return
        
        print(f"\nImport Update Summary:")
        print(f"Files modified: {len(self.changes_made)}")
        print("\nChanges made:")
        
        for change in self.changes_made:
            print(f"\n{change['file']}:")
            for detail in change['changes']:
                print(f"  - {detail}")

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Update import statements after reorganization')
    parser.add_argument('--dry-run', action='store_true', default=True,
                       help='Run in dry-run mode (default)')
    parser.add_argument('--execute', action='store_true',
                       help='Execute changes (overrides dry-run)')
    parser.add_argument('--validate', action='store_true',
                       help='Validate imports after update')
    
    args = parser.parse_args()
    
    # Execute mode overrides dry-run
    dry_run = not args.execute if args.execute else args.dry_run
    
    updater = ImportUpdater(dry_run=dry_run)
    
    print(f"Running in {'DRY-RUN' if dry_run else 'EXECUTE'} mode")
    
    # Update imports
    success = updater.update_all_imports()
    
    # Print summary
    updater.print_summary()
    
    # Validate if requested
    if args.validate:
        print("\nValidating imports...")
        errors = updater.validate_imports()
        if errors:
            print("Validation errors found:")
            for error in errors:
                print(f"  - {error}")
        else:
            print("All imports validated successfully!")
    
    if not success:
        print("Some errors occurred during import updates")
        sys.exit(1)
    
    print("Import update completed successfully!")

if __name__ == '__main__':
    main()
