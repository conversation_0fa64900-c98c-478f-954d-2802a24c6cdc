#!/usr/bin/env python3
"""
EcoCycle Batch 2: Application Module Reorganization
Moves and reorganizes application modules with validation.
"""

import os
import sys
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import subprocess

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('Logs/refactor.log', mode='a'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def move_app_modules(project_root: Path) -> bool:
    """Move application modules to new structure."""
    try:
        logger.info("Starting Batch 2: Application Module Reorganization")
        
        # Define file moves for app modules
        app_moves = [
            # Menu system
            ('apps/menu.py', 'ecocycle/apps/menu/main.py'),
            
            # Carbon footprint
            ('apps/carbon_footprint.py', 'ecocycle/apps/carbon_footprint/calculator.py'),
            
            # Data visualization
            ('apps/data_visualization.py', 'ecocycle/apps/data_viz/visualizer.py'),
            ('apps/data_viz/', 'ecocycle/apps/data_viz/'),  # Directory
            
            # Eco tips
            ('apps/eco_tips.py', 'ecocycle/apps/eco_tips/tips.py'),
            
            # Route planner
            ('apps/route_planner/', 'ecocycle/apps/route_planner/'),  # Directory
            
            # Challenges
            ('apps/challenges/', 'ecocycle/apps/challenges/'),  # Directory
            
            # Gamification
            ('apps/gamification/', 'ecocycle/apps/gamification/'),  # Directory
            ('apps/social_gamification/', 'ecocycle/apps/social_gamification/'),  # Directory
            
            # Dashboard
            ('apps/dashboard/', 'ecocycle/apps/dashboard/'),  # Directory
            
            # Admin
            ('apps/admin/', 'ecocycle/apps/admin/'),  # Directory
            
            # Developer
            ('apps/developer/', 'ecocycle/apps/developer/'),  # Directory
            
            # Apps __init__.py
            ('apps/__init__.py', 'ecocycle/apps/__init__.py'),
        ]
        
        # Move files and directories
        for src_path, dst_path in app_moves:
            src = project_root / src_path
            dst = project_root / dst_path
            
            if src.exists():
                # Ensure destination directory exists
                dst.parent.mkdir(parents=True, exist_ok=True)
                
                if src.is_dir():
                    # Copy directory
                    if dst.exists():
                        shutil.rmtree(dst)
                    shutil.copytree(str(src), str(dst))
                    logger.info(f"Copied directory: {src_path} -> {dst_path}")
                else:
                    # Copy file
                    shutil.copy2(str(src), str(dst))
                    logger.info(f"Copied file: {src_path} -> {dst_path}")
            else:
                logger.warning(f"Source not found: {src_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"App module move failed: {e}")
        return False

def create_app_init_files(project_root: Path) -> bool:
    """Create proper __init__.py files for app modules."""
    try:
        logger.info("Creating app module __init__.py files...")
        
        # Main apps __init__.py
        apps_init = project_root / 'ecocycle/apps/__init__.py'
        apps_init.write_text('''"""EcoCycle application modules."""
from . import menu
from . import carbon_footprint
from . import data_viz
from . import eco_tips
from . import route_planner
from . import challenges
from . import gamification
from . import dashboard
''')
        
        # Menu __init__.py
        menu_init = project_root / 'ecocycle/apps/menu/__init__.py'
        menu_init.write_text('''"""EcoCycle menu system."""
from .main import *
''')
        
        # Carbon footprint __init__.py
        carbon_init = project_root / 'ecocycle/apps/carbon_footprint/__init__.py'
        carbon_init.write_text('''"""EcoCycle carbon footprint calculator."""
from .calculator import *
''')
        
        # Data viz __init__.py
        dataviz_init = project_root / 'ecocycle/apps/data_viz/__init__.py'
        if not dataviz_init.exists():
            dataviz_init.write_text('''"""EcoCycle data visualization."""
from .visualizer import *
''')
        
        # Eco tips __init__.py
        tips_init = project_root / 'ecocycle/apps/eco_tips/__init__.py'
        tips_init.write_text('''"""EcoCycle eco tips."""
from .tips import *
''')
        
        logger.info("App __init__.py files created")
        return True
        
    except Exception as e:
        logger.error(f"Failed to create app __init__.py files: {e}")
        return False

def update_app_imports(project_root: Path) -> bool:
    """Update import statements for moved app modules."""
    try:
        logger.info("Updating app module imports...")
        
        # Import mappings for app modules
        import_mappings = {
            'apps.menu': 'ecocycle.apps.menu',
            'apps.carbon_footprint': 'ecocycle.apps.carbon_footprint',
            'apps.data_visualization': 'ecocycle.apps.data_viz.visualizer',
            'apps.eco_tips': 'ecocycle.apps.eco_tips',
            'apps.route_planner': 'ecocycle.apps.route_planner',
            'apps.challenges': 'ecocycle.apps.challenges',
            'apps.gamification': 'ecocycle.apps.gamification',
            'apps.social_gamification': 'ecocycle.apps.social_gamification',
            'apps.dashboard': 'ecocycle.apps.dashboard',
            'apps.admin': 'ecocycle.apps.admin',
            'apps.developer': 'ecocycle.apps.developer',
        }
        
        # Update imports in new ecocycle directory
        ecocycle_dir = project_root / 'ecocycle'
        if ecocycle_dir.exists():
            for py_file in ecocycle_dir.rglob('*.py'):
                update_imports_in_file(py_file, import_mappings)
        
        # Update imports in remaining files at root level
        root_files = ['main.py', 'cli.py', 'mini.py']
        for file_name in root_files:
            file_path = project_root / file_name
            if file_path.exists():
                update_imports_in_file(file_path, import_mappings)
        
        return True
        
    except Exception as e:
        logger.error(f"App import update failed: {e}")
        return False

def update_imports_in_file(file_path: Path, import_mappings: Dict[str, str]) -> bool:
    """Update import statements in a single file."""
    try:
        if not file_path.exists() or file_path.suffix != '.py':
            return True
        
        content = file_path.read_text()
        original_content = content
        
        # Update imports based on mappings
        for old_import, new_import in import_mappings.items():
            # Handle different import patterns
            import re
            patterns = [
                (rf'^import {re.escape(old_import)}$', f'import {new_import}'),
                (rf'^from {re.escape(old_import)} import', f'from {new_import} import'),
                (rf'^import {re.escape(old_import)} as', f'import {new_import} as'),
                (rf'import {re.escape(old_import)}\.', f'import {new_import}.'),
                (rf'from {re.escape(old_import)}\.', f'from {new_import}.'),
            ]
            
            for pattern, replacement in patterns:
                content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        # Only write if content changed
        if content != original_content:
            file_path.write_text(content)
            logger.info(f"Updated imports in: {file_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to update imports in {file_path}: {e}")
        return False

def validate_app_reorganization(project_root: Path) -> bool:
    """Validate that app reorganization was successful."""
    try:
        logger.info("Validating app module reorganization...")
        
        # Check that key files exist in new locations
        key_files = [
            'ecocycle/apps/menu/main.py',
            'ecocycle/apps/carbon_footprint/calculator.py',
            'ecocycle/apps/data_viz/__init__.py',
            'ecocycle/apps/eco_tips/tips.py',
        ]
        
        for file_path in key_files:
            full_path = project_root / file_path
            if not full_path.exists():
                logger.error(f"Missing file: {file_path}")
                return False
            logger.info(f"✓ File exists: {file_path}")
        
        # Try to import app modules
        logger.info("Testing app module imports...")
        result = subprocess.run([
            sys.executable, '-c', 
            'import sys; sys.path.insert(0, "."); import ecocycle.apps.menu; print("App imports working")'
        ], cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✓ App module imports working")
            return True
        else:
            logger.error(f"✗ App module import test failed: {result.stderr}")
            return False
        
    except Exception as e:
        logger.error(f"App validation failed: {e}")
        return False

def main():
    """Main function for batch 2 refactoring."""
    project_root = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    try:
        # Step 1: Move app modules
        if not move_app_modules(project_root):
            logger.error("Failed to move app modules")
            return False
        
        # Step 2: Create __init__.py files
        if not create_app_init_files(project_root):
            logger.error("Failed to create app __init__.py files")
            return False
        
        # Step 3: Update imports
        if not update_app_imports(project_root):
            logger.error("Failed to update app imports")
            return False
        
        # Step 4: Validate
        if not validate_app_reorganization(project_root):
            logger.error("App validation failed")
            return False
        
        logger.info("✓ Batch 2: Application Module Reorganization completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Batch 2 refactoring failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
