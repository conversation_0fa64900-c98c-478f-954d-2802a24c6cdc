# EcoCycle Deprecation Notice

<p align="center">
    <img src="https://i.postimg.cc/T2qM7Z1T/Eco-Cycle-Logo-Rounded.png" width="300" />
</p>

<p align="center">
  <a href="DEPRECATED.md">
    <img src="https://img.shields.io/badge/status-deprecated-red?style=for-the-badge" alt="Deprecated Badge">
  </a>
  <br>
</p>


> [!CAUTION]
> This project has been deprecated and should no longer be used


## Quick Guide

- [A Heartfelt Thank You](#a-heartfelt-thank-you---the-one-that-you-deserve)
- [Why EcoCycle Is Deprecated](#why-ecocycle-is-deprecated)
- [It's time to stop using EcoCycle](#now-its-time-to-stop-using-ecocycle-no-really-we-mean-it)
- [What You Should Do Now](#what-you-should-do-now)
- [The Legacy Lives On](#the-legacy-lives-on)
- [Final Words](#final-words)
- [Contact](#contact-information)

  
## A Heartfelt Thank You - The One That You Deserve

To our amazing community of environmentally conscious cyclists, supporters, contributors, and users:

We want to express our deepest gratitude for your support, enthusiasm, and dedication to EcoCycle. What started as a hackathon project grew into something more meaningful because of **you**. Your feedback, contributions, and passion for creating a greener tomorrow through cycling have been the driving force behind this project.

Special thanks to:
- Every user who logged their cycling trips and reduced their carbon footprint
- Contributors who helped improve the codebase
- Those who shared EcoCycle with friends and colleagues
- Everyone who provided feedback and suggestions
- The entire cycling community for embracing sustainable transportation

## Why EcoCycle Is Deprecated

After some soul-searching, a few tears and one too many flat tires, we’ve decided to sunset EcoCycle. Technology and sustainability keep moving forward—and so must we. It’s time for us to unclip our shoes and cycle off into the sunset.

## Now, it's time to **Stop Using EcoCycle**. (No, really. We mean it.)

**We strongly urge all users to discontinue using EcoCycle immediately.** The software will no longer receive:
- Security updates
- Bug fixes
- Feature enhancements
- Technical support
- Spare tire tubes

Continuing to use deprecated software may expose you to security vulnerabilities and reliability issues. We care about your digital safety and experience, which is why we recommend transitioning away from EcoCycle.

## What You Should Do Now

1. **Backup your data**: If you have important cycling data stored in EcoCycle, please export or backup this information before uninstalling.

2. **Uninstall EcoCycle**:
   ```bash
   pip uninstall ecocycle
   ```
   Or remove the application from your system if you installed it manually.
   _(Bonus points if you do it while wearing a helmet 😉.)_

4. **Explore alternatives**: Consider using other cycling and carbon footprint tracking applications that are actively maintained. Some options include:
   - Strava
   - MapMyRide
   - Komoot
   - Specialized Ride
   - Various carbon footprint calculators available online

## The Legacy Lives On

While EcoCycle as a software project is ending, the mission of promoting sustainable transportation and reducing carbon footprints continues. We hope that your experience with EcoCycle has inspired lasting habits that contribute to a healthier planet and that it has made you think more deeply about your impact in the world that we all love and how you ultimately play an often understated role in that. 

We plan to make it so the source code remains available as an educational resource and a testament to what can be accomplished when technology and environmental consciousness come together.

## Final Words

Cycling is still awesome. The planet still needs you. And we still love you (platonically, of course). Keep pedaling toward a cleaner, greener tomorrow.

Thank you for riding with us. Really. 

With more gratitude than a cyclist feels at the top of a hill,

The EcoCycle Team  
**Shirish Pothi, Ryan Eng, Ashlesha Sahoo, and Rochelle Joseph**  

---

## Contact Information
For any questions regarding this deprecation or this repository, please contact:  
Shirish Pothi: <EMAIL>
