[flake8]
max-line-length = 100
max-complexity = 10
exclude = 
    venv,
    eco_venv,
    .venv,
    build,
    dist,
    *.egg-info,
    __pycache__,
    Tests,
    backups,
    temp,
    cache,
    data,
    migrations,
    .git,
    .tox,
    .pytest_cache
ignore = 
    E203,  # whitespace before ':'
    E501,  # line too long (handled by max-line-length)
    W503,  # line break before binary operator
    W504,  # line break after binary operator
    F401,  # imported but unused (common in __init__.py files)
    F403,  # star imports
    F405   # name may be undefined due to star imports
per-file-ignores =
    __init__.py:F401,F403,F405
    */migrations/*:E501,F401
    */tests/*:F401,F403,F405
    setup.py:F401
