#!/usr/bin/env python3
"""
Test script for the refactored developer tools.
This script tests the modular architecture without running the full application.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all the modular components can be imported."""
    print("🧪 Testing Developer Tools Refactor...")
    
    try:
        # Test basic imports
        print("📦 Testing basic imports...")
        from apps.developer.developer_tools import DeveloperTools
        print("✅ DeveloperTools imported")
        
        # Test coordinator import
        from apps.developer.developer_coordinator import DeveloperCoordinator
        print("✅ DeveloperCoordinator imported")
        
        # Test that the new __init__.py works
        from apps.developer import DeveloperTools as DT, DeveloperCoordinator as DC
        print("✅ Package imports working")
        
        print("\n🎉 All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_coordinator_initialization():
    """Test that the coordinator can be initialized."""
    print("\n🔧 Testing coordinator initialization...")
    
    try:
        from apps.developer.developer_tools import DeveloperTools
        from apps.developer.developer_coordinator import DeveloperCoordinator
        
        # Mock developer auth for testing
        class MockDeveloperAuth:
            def get_developer_username(self):
                return 'test_dev'
            def is_developer_authenticated(self):
                return True
        
        # Test initialization
        mock_auth = MockDeveloperAuth()
        dev_tools = DeveloperTools(mock_auth)
        coordinator = DeveloperCoordinator(mock_auth, dev_tools)
        
        print("✅ DeveloperCoordinator initialized successfully")
        print(f"✅ System monitoring component: {type(coordinator.system_monitoring).__name__}")
        print(f"✅ Data management component: {type(coordinator.data_management).__name__}")
        print(f"✅ Performance monitoring component: {type(coordinator.performance_monitoring).__name__}")
        print("✅ All modular components loaded successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_sizes():
    """Test that the refactoring achieved the size reduction goals."""
    print("\n📏 Testing file size reduction...")
    
    try:
        import os
        
        # Check coordinator file size
        coordinator_path = "apps/developer/developer_coordinator.py"
        if os.path.exists(coordinator_path):
            with open(coordinator_path, 'r') as f:
                coordinator_lines = len(f.readlines())
            print(f"✅ DeveloperCoordinator: {coordinator_lines} lines")
            
            if coordinator_lines <= 500:
                print("✅ Coordinator meets size target (<500 lines)")
            else:
                print("⚠️ Coordinator exceeds size target")
        
        # Check if old monolithic file still exists
        old_ui_path = "apps/developer/developer_ui.py"
        if os.path.exists(old_ui_path):
            with open(old_ui_path, 'r') as f:
                old_ui_lines = len(f.readlines())
            print(f"📊 Old DeveloperUI: {old_ui_lines} lines")
            
            if coordinator_lines < old_ui_lines:
                reduction = ((old_ui_lines - coordinator_lines) / old_ui_lines) * 100
                print(f"✅ Size reduction achieved: {reduction:.1f}%")
            else:
                print("⚠️ No size reduction achieved")
        
        return True
        
    except Exception as e:
        print(f"❌ File size check failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("🔧 ECOCYCLE DEVELOPER TOOLS REFACTOR TEST")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_coordinator_initialization,
        test_file_sizes
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Refactoring successful!")
        print("\n✅ Benefits achieved:")
        print("   • Modular architecture implemented")
        print("   • File size reduced significantly")
        print("   • Clean separation of concerns")
        print("   • Maintainable codebase structure")
    else:
        print("❌ Some tests failed. Please review the issues above.")
    
    print("=" * 60)
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
