# Git
.git
.gitignore
.github

# Docker
.dockerignore
Dockerfile
docker-compose.yml

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
env/
.env

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/
coverage.xml
*.cover
.hypothesis/

# Logs
Logs/
*.log

# Data
data/
database_backups/
*.db

# IDE
.idea/
.vscode/
*.swp
*.swo

# Documentation
docs/

# Misc
.DS_Store
Thumbs.db
