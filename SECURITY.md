# Security Policy

<p align="center">
  <a href="DEPRECATED.md">
    <img src="https://img.shields.io/badge/repository status-deprecated-red?style=for-the-badge" alt="Deprecated Badge">
  </a>
  <br>
</p>


<p align="center">
  <b><i>We regret to inform you that this repository is no longer maintained.</i></b><br>
  <b>Thank you</b> to everyone who supported, used, or contributed to EcoCycle.<br>
  <br>
  <i>No further updates will be provided.</i>
</p>

--- 
## Supported Versions - _N/A_

| Version        | Supported         |
|----------------|-------------------|
 | 3.0            | :x:|
| 2.5            | :x: |
| 2.0            | :x: |
| 1.1            | :x: |
| < 1.0          | :x:               |

## Current Status 
<a href="https://github.com/shirishpothi/ecocycle/actions/workflows/ci.yml">
    <img alt="CI Status" src="https://img.shields.io/github/workflow/status/shirishpothi/ecocycle/EcoCycle%20CI?style=for-the-badge&logo=github-actions&logoColor=white&label=CI">
</a>
<a href="https://codecov.io/gh/shirishpothi/ecocycle">
    <img alt="Code Coverage" src="https://img.shields.io/codecov/c/github/shirishpothi/ecocycle?style=for-the-badge&logo=codecov&logoColor=white">
</a>
<a href="https://pypi.org/project/ecocycle/">
    <img alt="PyPI Version" src="https://img.shields.io/pypi/v/ecocycle?style=for-the-badge&logo=pypi&logoColor=white">
</a>

## Reporting a Vulnerability.  
#### Use either of the following methods to report a security vulnerability.

In the event that you come across a security vulnerability, please help to improve the safety of the _EcoCycle_ program by reporting it using this button: 
 
<a href="https://github.com/shirishpothi/ecocycle/issues" target="_blank">
    <img src="https://img.shields.io/badge/Report%20Vulnerability%20on%20Github%20Issues-%23D32F2F?style=for-the-badge&logo=github&logoColor=white" alt="Report an Issue">
</a>

<hr style="border: 1px solid #D32F2F; margin: 10px 0;">

Should you choose to email me through this button, you can expect a response directly on email and a new release on the releases tab, which will also be emailed directly to you. Lastly, a statement will be published on the issues tab of _EcoCycle_: 

<a href="https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>&su=Immediate%20GitHub%20Security%20Vulnerability&body=Dear%20Dev,%0A%0AI%20hope%20this%20email%20finds%20you%20well.%0A%0AThis%20email%20serves%20as%20notification%20of%20a%20critical%20security%20vulnerability%20affecting%20your%20GitHub%20EcoCycle%20repository.%20Specifically,%20the%20vulnerability%20in%20question%20is%20[describe%20vulnerability%20and%20what%20you%20were%20doing%20when%20you%20noticed%20it,%20what%20effect%20it%20had%20on%20your%20system%20or%20the%20way%20the%20program%20was%20run%20and%20what-if%20any%20remediation%20steps%20were%20taken].%0APlease%20take%20immediate%20action%20to%20mitigate%20the%20risk.%20Feel%20free%20to%20contact%20me%20at%20my%20email:%20[insert%20your%20email%20address].%0A%0ASincerely,%0A[your%20name%20and%20as%20many%20contact%20details%20as%20possible]" target="_blank">
    <img src="https://img.shields.io/badge/Report%20Vulnerability%20via%20Gmail-%23FF5733?style=for-the-badge&logo=gmail&logoColor=white" alt="Report Security Issue">
</a>

 

<hr style="border: 1px solid #D32F2F; margin: 10px 0;">

If a successful security patch can be created, a new, patched version of the software will be released and it is strongly recommended to remove the current _EcoCycle_ program & its dependancies.

The patched version will be available for download on the releases tab.

###### If not, the infected _EcoCycle_ version will be removed from the releases tab and the _EcoCycle_ team will work on a new version of the software that is not vulnerable to the security issue, under a new version number to avoid confusion.
> [!IMPORTANT]  
> Until the said issue has been resolved, the _EcoCyle Team_ **strongly** recommends not hosting it and reverting back to the most recent secure version.
