#!/usr/bin/env python3
"""
Comprehensive Error Handler tests
tests error handling, exception management, logging, and recovery mechanisms
across all application components.
"""

import os
import sys
import unittest
import tempfile
import logging
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

from tests import EcoCycleTestCase, get_test_config
from core.error_handler import (
    handle_error, DatabaseError, AuthenticationError, ValidationError,
    NetworkError, ConfigurationError, PermissionError as EcoPermissionError
)
from core.logging_manager import LoggingManager

class TestErrorHandlerComprehensive(EcoCycleTestCase):
    """Comprehensive tests for error handling system."""
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        
        # Create temporary log file
        self.temp_log = tempfile.NamedTemporaryFile(delete=False, suffix='.log')
        self.temp_log.close()
        
        # Initialize logging manager
        self.logging_manager = LoggingManager(log_file=self.temp_log.name)
        
        # Set up test logger
        self.test_logger = logging.getLogger('test_error_handler')
        
    def tearDown(self):
        """Clean up test fixtures."""
        super().tearDown()
        
        # Remove temporary log file
        if os.path.exists(self.temp_log.name):
            try:
                os.unlink(self.temp_log.name)
            except OSError:
                pass
    
    def test_database_error_handling(self):
        """Test database error handling and recovery."""
        # Test DatabaseError creation and handling
        db_error = DatabaseError("Connection failed", error_code="DB001")
        
        self.assertEqual(str(db_error), "Connection failed")
        self.assertEqual(db_error.error_code, "DB001")
        
        # Test error handling with logging
        with patch('core.error_handler.logger') as mock_logger:
            handle_error(db_error)
            mock_logger.error.assert_called()
        
        # Test database connection error recovery
        with patch('sqlite3.connect', side_effect=Exception("Database locked")):
            try:
                # Simulate database operation that fails
                raise DatabaseError("Database operation failed")
            except DatabaseError as e:
                handled_error = handle_error(e)
                self.assertIsNotNone(handled_error)
        
        # Test database corruption handling
        corruption_error = DatabaseError("Database file is corrupted", error_code="DB002")
        with patch('core.error_handler.logger') as mock_logger:
            handle_error(corruption_error)
            mock_logger.critical.assert_called()
    
    def test_authentication_error_handling(self):
        """Test authentication error handling."""
        # Test AuthenticationError creation
        auth_error = AuthenticationError("Invalid credentials", user="test_user")
        
        self.assertEqual(str(auth_error), "Invalid credentials")
        self.assertEqual(auth_error.user, "test_user")
        
        # Test failed login handling
        with patch('core.error_handler.logger') as mock_logger:
            handle_error(auth_error)
            mock_logger.warning.assert_called()
        
        # Test session timeout handling
        session_error = AuthenticationError("Session expired", error_code="AUTH002")
        with patch('core.error_handler.logger') as mock_logger:
            handle_error(session_error)
            mock_logger.info.assert_called()
        
        # Test brute force protection
        brute_force_error = AuthenticationError("Too many failed attempts", error_code="AUTH003")
        with patch('core.error_handler.logger') as mock_logger:
            handle_error(brute_force_error)
            mock_logger.warning.assert_called()
    
    def test_validation_error_handling(self):
        """Test input validation error handling."""
        # Test ValidationError creation
        validation_error = ValidationError("Invalid email format", field="email", value="invalid_email")
        
        self.assertEqual(str(validation_error), "Invalid email format")
        self.assertEqual(validation_error.field, "email")
        self.assertEqual(validation_error.value, "invalid_email")
        
        # Test form validation errors
        form_errors = [
            ValidationError("Username too short", field="username"),
            ValidationError("Password too weak", field="password"),
            ValidationError("Email already exists", field="email")
        ]
        
        for error in form_errors:
            with patch('core.error_handler.logger') as mock_logger:
                handle_error(error)
                mock_logger.warning.assert_called()
        
        # Test data type validation
        type_error = ValidationError("Expected integer, got string", field="age", value="not_a_number")
        with patch('core.error_handler.logger') as mock_logger:
            handle_error(type_error)
            mock_logger.warning.assert_called()
    
    def test_network_error_handling(self):
        """Test network error handling and retry mechanisms."""
        # Test NetworkError creation
        network_error = NetworkError("Connection timeout", url="https://api.example.com", status_code=408)
        
        self.assertEqual(str(network_error), "Connection timeout")
        self.assertEqual(network_error.url, "https://api.example.com")
        self.assertEqual(network_error.status_code, 408)
        
        # Test API request failures
        api_errors = [
            NetworkError("API rate limit exceeded", status_code=429),
            NetworkError("Service unavailable", status_code=503),
            NetworkError("DNS resolution failed", status_code=0)
        ]
        
        for error in api_errors:
            with patch('core.error_handler.logger') as mock_logger:
                handle_error(error)
                mock_logger.error.assert_called()
        
        # Test retry mechanism simulation
        retry_count = 0
        max_retries = 3
        
        def simulate_network_operation():
            nonlocal retry_count
            retry_count += 1
            if retry_count < max_retries:
                raise NetworkError("Temporary network failure")
            return "Success"
        
        # Test retry logic
        result = None
        for attempt in range(max_retries):
            try:
                result = simulate_network_operation()
                break
            except NetworkError as e:
                if attempt == max_retries - 1:
                    handle_error(e)
                    raise
                else:
                    # Log retry attempt
                    self.test_logger.info(f"Retry attempt {attempt + 1}")
        
        self.assertEqual(result, "Success")
        self.assertEqual(retry_count, max_retries)
    
    def test_configuration_error_handling(self):
        """Test configuration error handling."""
        # Test ConfigurationError creation
        config_error = ConfigurationError("Missing API key", config_key="GEMINI_API_KEY")
        
        self.assertEqual(str(config_error), "Missing API key")
        self.assertEqual(config_error.config_key, "GEMINI_API_KEY")
        
        # Test missing configuration handling
        missing_configs = [
            ConfigurationError("Database URL not configured", config_key="DATABASE_URL"),
            ConfigurationError("Email settings incomplete", config_key="EMAIL_CONFIG"),
            ConfigurationError("Invalid log level", config_key="LOG_LEVEL")
        ]
        
        for error in missing_configs:
            with patch('core.error_handler.logger') as mock_logger:
                handle_error(error)
                mock_logger.error.assert_called()
        
        # Test configuration validation
        invalid_config_error = ConfigurationError("Invalid configuration value", config_key="MAX_CONNECTIONS", value=-1)
        with patch('core.error_handler.logger') as mock_logger:
            handle_error(invalid_config_error)
            mock_logger.error.assert_called()
    
    def test_permission_error_handling(self):
        """Test permission and access error handling."""
        # Test PermissionError creation
        perm_error = EcoPermissionError("Access denied", resource="admin_panel", user="regular_user")
        
        self.assertEqual(str(perm_error), "Access denied")
        self.assertEqual(perm_error.resource, "admin_panel")
        self.assertEqual(perm_error.user, "regular_user")
        
        # Test file permission errors
        file_perm_error = EcoPermissionError("Cannot write to file", resource="/protected/file.txt")
        with patch('core.error_handler.logger') as mock_logger:
            handle_error(file_perm_error)
            mock_logger.error.assert_called()
        
        # Test admin access errors
        admin_error = EcoPermissionError("Admin privileges required", user="guest")
        with patch('core.error_handler.logger') as mock_logger:
            handle_error(admin_error)
            mock_logger.warning.assert_called()
    
    def test_error_logging_and_reporting(self):
        """Test error logging and reporting mechanisms."""
        # Test error logging with different severity levels
        errors = [
            (DatabaseError("Critical database failure"), "critical"),
            (AuthenticationError("Login failed"), "warning"),
            (ValidationError("Invalid input"), "warning"),
            (NetworkError("API timeout"), "error"),
            (ConfigurationError("Missing config"), "error")
        ]
        
        for error, expected_level in errors:
            with patch('core.error_handler.logger') as mock_logger:
                handle_error(error)
                
                # Verify appropriate logging level was used
                if expected_level == "critical":
                    mock_logger.critical.assert_called()
                elif expected_level == "error":
                    mock_logger.error.assert_called()
                elif expected_level == "warning":
                    mock_logger.warning.assert_called()
        
        # Test error context logging
        context_error = DatabaseError("Query failed", context={"query": "SELECT * FROM users", "params": ["test"]})
        with patch('core.error_handler.logger') as mock_logger:
            handle_error(context_error)
            mock_logger.error.assert_called()
    
    def test_error_recovery_mechanisms(self):
        """Test error recovery and fallback mechanisms."""
        # Test database connection recovery
        def simulate_db_recovery():
            attempts = 0
            max_attempts = 3
            
            while attempts < max_attempts:
                try:
                    attempts += 1
                    if attempts < max_attempts:
                        raise DatabaseError("Connection failed")
                    return "Connected"
                except DatabaseError as e:
                    if attempts >= max_attempts:
                        handle_error(e)
                        raise
                    # Wait and retry (simulated)
                    continue
        
        result = simulate_db_recovery()
        self.assertEqual(result, "Connected")
        
        # Test fallback mechanism for external services
        def simulate_service_fallback():
            primary_service_failed = True
            
            if primary_service_failed:
                try:
                    raise NetworkError("Primary service unavailable")
                except NetworkError as e:
                    handle_error(e)
                    # Use fallback service
                    return "Fallback service response"
            
            return "Primary service response"
        
        result = simulate_service_fallback()
        self.assertEqual(result, "Fallback service response")
    
    def test_error_aggregation_and_analysis(self):
        """Test error aggregation and analysis features."""
        # Simulate multiple errors over time
        error_log = []
        
        # Generate various errors
        for i in range(10):
            if i % 3 == 0:
                error = DatabaseError(f"DB error {i}")
            elif i % 3 == 1:
                error = NetworkError(f"Network error {i}")
            else:
                error = ValidationError(f"Validation error {i}")
            
            error_log.append({
                'timestamp': datetime.now(),
                'error_type': type(error).__name__,
                'message': str(error),
                'severity': 'high' if isinstance(error, DatabaseError) else 'medium'
            })
        
        # Analyze error patterns
        error_counts = {}
        for log_entry in error_log:
            error_type = log_entry['error_type']
            error_counts[error_type] = error_counts.get(error_type, 0) + 1
        
        # Verify error distribution
        self.assertIn('DatabaseError', error_counts)
        self.assertIn('NetworkError', error_counts)
        self.assertIn('ValidationError', error_counts)
        
        # Test error rate calculation
        high_severity_errors = sum(1 for log in error_log if log['severity'] == 'high')
        error_rate = high_severity_errors / len(error_log)
        
        self.assertGreater(error_rate, 0)
        self.assertLessEqual(error_rate, 1)
    
    def test_graceful_degradation(self):
        """Test graceful degradation when components fail."""
        # Test application behavior when database is unavailable
        with patch('sqlite3.connect', side_effect=Exception("Database unavailable")):
            try:
                # Simulate operation that requires database
                raise DatabaseError("Database connection failed")
            except DatabaseError as e:
                handled_error = handle_error(e)
                # Application should continue with limited functionality
                self.assertIsNotNone(handled_error)
        
        # Test behavior when external APIs are unavailable
        with patch('requests.get', side_effect=Exception("Network unavailable")):
            try:
                # Simulate API call that fails
                raise NetworkError("API call failed")
            except NetworkError as e:
                handled_error = handle_error(e)
                # Application should use cached data or default values
                self.assertIsNotNone(handled_error)
        
        # Test behavior when email service is unavailable
        email_error = NetworkError("Email service unavailable")
        with patch('core.error_handler.logger') as mock_logger:
            handle_error(email_error)
            # Application should queue emails for later or use alternative notification
            mock_logger.error.assert_called()

if __name__ == '__main__':
    unittest.main()
