#!/usr/bin/env python3
"""
Comprehensive Testing Report for EcoCycle Backend
This module provides a detailed analysis of the current testing state and recommendations.
"""

import os
import sys
import unittest
import json
from datetime import datetime
from typing import Dict, List, Any

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

class EcoCycleTestingReport:
    """Comprehensive testing report generator for EcoCycle."""
    
    def __init__(self):
        self.report_data = {
            'timestamp': datetime.now().isoformat(),
            'application_analysis': {},
            'testing_coverage': {},
            'issues_found': [],
            'recommendations': [],
            'test_results': {}
        }
    
    def analyze_application_structure(self):
        """Analyze the current application structure."""
        structure_analysis = {
            'core_modules': {
                'database_manager': self._check_module('core.database_manager'),
                'error_handler': self._check_module('core.error_handler'),
                'logging_manager': self._check_module('core.logging_manager'),
                'dependency_manager': self._check_module('core.dependency.dependency_manager'),
                'plugin_manager': self._check_module('core.plugin.plugin_manager')
            },
            'auth_modules': {
                'user_manager': self._check_module('auth.user_management.user_manager'),
                'email_verification': self._check_module('auth.email_verification'),
                'developer_auth': self._check_module('auth.developer_auth')
            },
            'app_modules': {
                'menu': self._check_module('apps.menu'),
                'route_planner': self._check_module('apps.route_planner.ai_route_planner'),
                'carbon_footprint': self._check_module('apps.carbon_footprint'),
                'data_visualization': self._check_module('apps.data_visualization')
            },
            'service_modules': {
                'sheets_manager': self._check_module('services.sheets.sheets_manager'),
                'sync_service': self._check_module('services.sync.sync_service'),
                'notification_system': self._check_module('services.notifications.notification_system')
            }
        }
        
        self.report_data['application_analysis'] = structure_analysis
        return structure_analysis
    
    def _check_module(self, module_name: str) -> Dict[str, Any]:
        """Check if a module can be imported and analyze its structure."""
        try:
            module = __import__(module_name, fromlist=[module_name.split('.')[-1]])
            
            # Get module attributes
            attributes = [attr for attr in dir(module) if not attr.startswith('_')]
            classes = [attr for attr in attributes if hasattr(getattr(module, attr), '__class__') 
                      and str(type(getattr(module, attr))) == "<class 'type'>"]
            functions = [attr for attr in attributes if callable(getattr(module, attr)) 
                        and not attr in classes]
            
            return {
                'status': 'available',
                'path': getattr(module, '__file__', 'unknown'),
                'classes': classes,
                'functions': functions,
                'total_attributes': len(attributes)
            }
        except ImportError as e:
            return {
                'status': 'import_error',
                'error': str(e),
                'classes': [],
                'functions': [],
                'total_attributes': 0
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'classes': [],
                'functions': [],
                'total_attributes': 0
            }
    
    def analyze_database_structure(self):
        """Analyze the database structure and available functions."""
        try:
            import core.database_manager as db_manager
            
            # Get all database functions
            db_functions = [attr for attr in dir(db_manager) 
                           if callable(getattr(db_manager, attr)) and not attr.startswith('_')]
            
            # Categorize functions
            user_functions = [f for f in db_functions if 'user' in f.lower()]
            trip_functions = [f for f in db_functions if 'trip' in f.lower()]
            stats_functions = [f for f in db_functions if 'stat' in f.lower()]
            backup_functions = [f for f in db_functions if 'backup' in f.lower()]
            
            database_analysis = {
                'status': 'available',
                'total_functions': len(db_functions),
                'user_functions': user_functions,
                'trip_functions': trip_functions,
                'stats_functions': stats_functions,
                'backup_functions': backup_functions,
                'all_functions': db_functions
            }
            
            # Check database file
            database_file = getattr(db_manager, 'DATABASE_FILE', 'unknown')
            database_analysis['database_file'] = database_file
            database_analysis['database_exists'] = os.path.exists(database_file) if database_file != 'unknown' else False
            
        except Exception as e:
            database_analysis = {
                'status': 'error',
                'error': str(e),
                'total_functions': 0
            }
        
        self.report_data['database_analysis'] = database_analysis
        return database_analysis
    
    def analyze_testing_coverage(self):
        """Analyze current testing coverage."""
        test_dir = os.path.join(PROJECT_ROOT, 'tests')
        
        if not os.path.exists(test_dir):
            coverage_analysis = {
                'status': 'no_test_directory',
                'existing_tests': [],
                'total_tests': 0
            }
        else:
            # Find all test files
            test_files = [f for f in os.listdir(test_dir) 
                         if f.startswith('test_') and f.endswith('.py')]
            
            # Analyze each test file
            test_analysis = {}
            for test_file in test_files:
                test_path = os.path.join(test_dir, test_file)
                try:
                    with open(test_path, 'r') as f:
                        content = f.read()
                        
                    # Count test methods
                    test_methods = content.count('def test_')
                    
                    test_analysis[test_file] = {
                        'status': 'exists',
                        'test_methods': test_methods,
                        'file_size': len(content),
                        'lines': content.count('\n')
                    }
                except Exception as e:
                    test_analysis[test_file] = {
                        'status': 'error',
                        'error': str(e)
                    }
            
            coverage_analysis = {
                'status': 'partial',
                'existing_tests': test_files,
                'total_tests': len(test_files),
                'test_analysis': test_analysis,
                'total_test_methods': sum(t.get('test_methods', 0) for t in test_analysis.values())
            }
        
        self.report_data['testing_coverage'] = coverage_analysis
        return coverage_analysis
    
    def identify_issues(self):
        """Identify issues found during analysis."""
        issues = []
        
        # Check application structure issues
        app_analysis = self.report_data.get('application_analysis', {})
        for category, modules in app_analysis.items():
            for module_name, module_info in modules.items():
                if module_info['status'] != 'available':
                    issues.append({
                        'type': 'module_import_error',
                        'severity': 'high',
                        'module': f"{category}.{module_name}",
                        'description': f"Cannot import {module_name}: {module_info.get('error', 'unknown error')}"
                    })
        
        # Check database issues
        db_analysis = self.report_data.get('database_analysis', {})
        if db_analysis.get('status') != 'available':
            issues.append({
                'type': 'database_error',
                'severity': 'critical',
                'description': f"Database manager not available: {db_analysis.get('error', 'unknown error')}"
            })
        
        # Check testing coverage issues
        test_coverage = self.report_data.get('testing_coverage', {})
        if test_coverage.get('total_test_methods', 0) < 50:
            issues.append({
                'type': 'insufficient_test_coverage',
                'severity': 'medium',
                'description': f"Only {test_coverage.get('total_test_methods', 0)} test methods found, need comprehensive coverage"
            })
        
        self.report_data['issues_found'] = issues
        return issues
    
    def generate_recommendations(self):
        """Generate recommendations based on analysis."""
        recommendations = []
        
        # Module-specific recommendations
        recommendations.extend([
            {
                'category': 'testing_infrastructure',
                'priority': 'high',
                'title': 'Create Comprehensive Test Suite',
                'description': 'Implement unit tests for all core modules with proper mocking and isolation',
                'actions': [
                    'Create test_database_functions.py for database operations',
                    'Create test_user_management.py for authentication',
                    'Create test_route_planning.py for route planning features',
                    'Create test_error_handling.py for error scenarios'
                ]
            },
            {
                'category': 'database_testing',
                'priority': 'high',
                'title': 'Fix Database Testing Issues',
                'description': 'Resolve database schema conflicts and function availability',
                'actions': [
                    'Create proper test database setup with temporary files',
                    'Mock external dependencies (Google Sheets, APIs)',
                    'Test database CRUD operations with actual functions',
                    'Implement database migration testing'
                ]
            },
            {
                'category': 'integration_testing',
                'priority': 'medium',
                'title': 'Implement Integration tests',
                'description': 'Test component interactions and data flow',
                'actions': [
                    'Test authentication flow end-to-end',
                    'Test route planning with weather integration',
                    'Test data persistence and retrieval',
                    'Test email verification workflow'
                ]
            },
            {
                'category': 'performance_testing',
                'priority': 'medium',
                'title': 'Add Performance Testing',
                'description': 'Ensure application performs well under load',
                'actions': [
                    'Test database performance with bulk operations',
                    'Test memory usage during large data processing',
                    'Test concurrent user operations',
                    'Test API response times'
                ]
            },
            {
                'category': 'error_handling',
                'priority': 'high',
                'title': 'Comprehensive Error Testing',
                'description': 'Test all error scenarios and recovery mechanisms',
                'actions': [
                    'Test network failure scenarios',
                    'Test invalid input handling',
                    'Test database connection failures',
                    'Test graceful degradation'
                ]
            }
        ])
        
        self.report_data['recommendations'] = recommendations
        return recommendations
    
    def run_basic_functionality_tests(self):
        """Run basic functionality tests to verify core operations."""
        test_results = {
            'database_connection': self._test_database_connection(),
            'module_imports': self._test_module_imports(),
            'configuration_loading': self._test_configuration_loading(),
            'basic_operations': self._test_basic_operations()
        }
        
        self.report_data['test_results'] = test_results
        return test_results
    
    def _test_database_connection(self):
        """Test basic database connectivity."""
        try:
            import core.database_manager as db_manager
            
            # Test connection creation
            conn = db_manager.create_connection()
            if conn:
                conn.close()
                return {'status': 'pass', 'message': 'Database connection successful'}
            else:
                return {'status': 'fail', 'message': 'Could not create database connection'}
        except Exception as e:
            return {'status': 'error', 'message': f'Database connection error: {e}'}
    
    def _test_module_imports(self):
        """Test critical module imports."""
        critical_modules = [
            'core.database_manager',
            'auth.user_management.user_manager',
            'apps.menu',
            'config.config'
        ]
        
        results = {}
        for module_name in critical_modules:
            try:
                __import__(module_name)
                results[module_name] = {'status': 'pass', 'message': 'Import successful'}
            except Exception as e:
                results[module_name] = {'status': 'fail', 'message': f'Import failed: {e}'}
        
        overall_status = 'pass' if all(r['status'] == 'pass' for r in results.values()) else 'fail'
        return {'status': overall_status, 'details': results}
    
    def _test_configuration_loading(self):
        """Test configuration loading."""
        try:
            import config.config as config
            
            # Check for essential config values
            essential_configs = ['DATABASE_FILE', 'LOG_DIR', 'VERSION']
            missing_configs = []
            
            for config_name in essential_configs:
                if not hasattr(config, config_name):
                    missing_configs.append(config_name)
            
            if missing_configs:
                return {'status': 'fail', 'message': f'Missing configurations: {missing_configs}'}
            else:
                return {'status': 'pass', 'message': 'All essential configurations loaded'}
        except Exception as e:
            return {'status': 'error', 'message': f'Configuration loading error: {e}'}
    
    def _test_basic_operations(self):
        """Test basic application operations."""
        try:
            # Test user manager creation
            from auth.user_management.user_manager import UserManager
            user_manager = UserManager(None)  # No sheets manager for testing
            
            return {'status': 'pass', 'message': 'Basic operations functional'}
        except Exception as e:
            return {'status': 'error', 'message': f'Basic operations error: {e}'}
    
    def generate_report(self):
        """Generate the complete testing report."""
        print("Generating Comprehensive EcoCycle Testing Report...")
        
        # Run all analyses
        self.analyze_application_structure()
        self.analyze_database_structure()
        self.analyze_testing_coverage()
        self.identify_issues()
        self.generate_recommendations()
        self.run_basic_functionality_tests()
        
        return self.report_data
    
    def save_report(self, filename: str = None):
        """Save the report to a JSON file."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"ecocycle_testing_report_{timestamp}.json"
        
        report_path = os.path.join(PROJECT_ROOT, 'tests', 'test_logs', filename)
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump(self.report_data, f, indent=2)
        
        return report_path
    
    def print_summary(self):
        """Print a summary of the testing report."""
        print("\n" + "="*80)
        print("ECOCYCLE COMPREHENSIVE TESTING ANALYSIS")
        print("="*80)
        
        # Application Structure Summary
        app_analysis = self.report_data.get('application_analysis', {})
        total_modules = sum(len(modules) for modules in app_analysis.values())
        available_modules = sum(1 for modules in app_analysis.values() 
                               for module in modules.values() 
                               if module['status'] == 'available')
        
        print(f"Application Modules: {available_modules}/{total_modules} available")
        
        # Database Analysis Summary
        db_analysis = self.report_data.get('database_analysis', {})
        print(f"Database Status: {db_analysis.get('status', 'unknown')}")
        print(f"Database Functions: {db_analysis.get('total_functions', 0)}")
        
        # Testing Coverage Summary
        test_coverage = self.report_data.get('testing_coverage', {})
        print(f"Test Files: {test_coverage.get('total_tests', 0)}")
        print(f"Test Methods: {test_coverage.get('total_test_methods', 0)}")
        
        # Issues Summary
        issues = self.report_data.get('issues_found', [])
        critical_issues = len([i for i in issues if i.get('severity') == 'critical'])
        high_issues = len([i for i in issues if i.get('severity') == 'high'])
        medium_issues = len([i for i in issues if i.get('severity') == 'medium'])
        
        print(f"\nIssues Found:")
        print(f"  Critical: {critical_issues}")
        print(f"  High: {high_issues}")
        print(f"  Medium: {medium_issues}")
        
        # Test Results Summary
        test_results = self.report_data.get('test_results', {})
        passed_tests = len([t for t in test_results.values() if t.get('status') == 'pass'])
        total_tests = len(test_results)
        
        print(f"\nBasic Functionality tests: {passed_tests}/{total_tests} passed")
        
        # Recommendations Summary
        recommendations = self.report_data.get('recommendations', [])
        high_priority = len([r for r in recommendations if r.get('priority') == 'high'])
        
        print(f"High Priority Recommendations: {high_priority}")
        
        print("\n" + "="*80)

def main():
    """Main function to run the comprehensive testing report."""
    reporter = EcoCycleTestingReport()
    
    try:
        # Generate the complete report
        report_data = reporter.generate_report()
        
        # Print summary
        reporter.print_summary()
        
        # Save detailed report
        report_path = reporter.save_report()
        print(f"\nDetailed report saved to: {report_path}")
        
        # Determine exit code based on critical issues
        issues = report_data.get('issues_found', [])
        critical_issues = [i for i in issues if i.get('severity') == 'critical']
        
        if critical_issues:
            print(f"\n❌ {len(critical_issues)} critical issues found!")
            return 1
        else:
            print("\n✅ No critical issues found!")
            return 0
            
    except Exception as e:
        print(f"\n❌ Report generation failed: {e}")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
