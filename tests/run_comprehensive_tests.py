#!/usr/bin/env python3
"""
Comprehensive Test Runner for EcoCycle Backend
Executes all test categories with detailed reporting and coverage analysis.
"""

import os
import sys
import unittest
import time
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import traceback

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

# Import test configuration
from tests import setup_test_environment, cleanup_test_environment, get_test_config

class ComprehensiveTestRunner:
    """Comprehensive test runner with detailed reporting."""

    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.results = {}
        self.test_categories = {
            'unit_tests': [
                'test_user_manager_comprehensive',
                'test_database_manager_comprehensive',
                'test_config_manager_comprehensive',
                'test_error_handler_comprehensive',
                'test_dependency_manager_comprehensive',
                'test_logging_manager_comprehensive'
            ],
            'integration_tests': [
                'test_auth_integration',
                'test_route_planning_integration',
                'test_weather_integration',
                'test_email_integration',
                'test_database_integration'
            ],
            'feature_tests': [
                'test_authentication_features',
                'test_route_planning_features',
                'test_statistics_features',
                'test_developer_tools_features',
                'test_backup_restore_features'
            ],
            'performance_tests': [
                'test_performance_comprehensive',
                'test_database_performance'
            ],
            'security_tests': [
                'test_security_comprehensive',
                'test_auth_integration'
            ],
            'error_handling_tests': [
                'test_network_failures',
                'test_invalid_inputs',
                'test_edge_cases',
                'test_exception_handling'
            ]
        }

    def setup_logging(self):
        """Set up comprehensive test logging."""
        log_dir = get_test_config('log_dir')
        log_file = os.path.join(log_dir, f'comprehensive_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')

        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )

        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Starting comprehensive test run - Log file: {log_file}")

    def run_test_category(self, category: str, test_modules: List[str]) -> Dict[str, Any]:
        """Run a specific category of tests."""
        self.logger.info(f"Running {category} tests...")
        category_results = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'errors': 0,
            'skipped': 0,
            'execution_time': 0,
            'test_results': [],
            'failures': [],
            'errors_list': []
        }

        start_time = time.time()

        for module_name in test_modules:
            try:
                # Import and run the test module
                module_result = self.run_single_test_module(module_name)
                category_results['test_results'].append(module_result)

                # Aggregate results
                category_results['total_tests'] += module_result.get('total_tests', 0)
                category_results['passed'] += module_result.get('passed', 0)
                category_results['failed'] += module_result.get('failed', 0)
                category_results['errors'] += module_result.get('errors', 0)
                category_results['skipped'] += module_result.get('skipped', 0)

                if module_result.get('failures'):
                    category_results['failures'].extend(module_result['failures'])
                if module_result.get('errors_list'):
                    category_results['errors_list'].extend(module_result['errors_list'])

            except Exception as e:
                self.logger.error(f"Failed to run test module {module_name}: {e}")
                category_results['errors'] += 1
                category_results['errors_list'].append({
                    'module': module_name,
                    'error': str(e),
                    'traceback': traceback.format_exc()
                })

        category_results['execution_time'] = time.time() - start_time
        return category_results

    def run_single_test_module(self, module_name: str) -> Dict[str, Any]:
        """Run a single test module and return results."""
        try:
            # Try to import the test module
            test_module = __import__(f'tests.{module_name}', fromlist=[module_name])

            # Discover tests in the module
            loader = unittest.TestLoader()
            suite = loader.loadTestsFromModule(test_module)

            # Run the tests
            runner = unittest.TextTestRunner(stream=open(os.devnull, 'w'), verbosity=0)
            result = runner.run(suite)

            return {
                'module': module_name,
                'total_tests': result.testsRun,
                'passed': result.testsRun - len(result.failures) - len(result.errors),
                'failed': len(result.failures),
                'errors': len(result.errors),
                'skipped': len(result.skipped) if hasattr(result, 'skipped') else 0,
                'failures': [{'test': str(test), 'error': error} for test, error in result.failures],
                'errors_list': [{'test': str(test), 'error': error} for test, error in result.errors]
            }

        except ImportError as e:
            self.logger.warning(f"Test module {module_name} not found, creating placeholder: {e}")
            return {
                'module': module_name,
                'total_tests': 0,
                'passed': 0,
                'failed': 0,
                'errors': 1,
                'skipped': 0,
                'failures': [],
                'errors_list': [{'test': module_name, 'error': f'Module not found: {e}'}]
            }

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all test categories and return comprehensive results."""
        self.start_time = time.time()
        self.logger.info("Starting comprehensive EcoCycle backend test suite...")

        # Set up test environment
        setup_test_environment()

        try:
            # Run each test category
            for category, test_modules in self.test_categories.items():
                self.results[category] = self.run_test_category(category, test_modules)

                # Log category summary
                cat_result = self.results[category]
                self.logger.info(f"{category} completed: {cat_result['passed']}/{cat_result['total_tests']} passed")

        finally:
            # Clean up test environment
            cleanup_test_environment()

        self.end_time = time.time()
        return self.generate_final_report()

    def generate_final_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        total_execution_time = (self.end_time or 0) - (self.start_time or 0)

        # Aggregate all results
        total_tests = sum(cat['total_tests'] for cat in self.results.values())
        total_passed = sum(cat['passed'] for cat in self.results.values())
        total_failed = sum(cat['failed'] for cat in self.results.values())
        total_errors = sum(cat['errors'] for cat in self.results.values())
        total_skipped = sum(cat['skipped'] for cat in self.results.values())

        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0

        report = {
            'summary': {
                'total_tests': total_tests,
                'passed': total_passed,
                'failed': total_failed,
                'errors': total_errors,
                'skipped': total_skipped,
                'success_rate': round(success_rate, 2),
                'execution_time': round(total_execution_time, 2),
                'timestamp': datetime.now().isoformat()
            },
            'category_results': self.results,
            'recommendations': self.generate_recommendations()
        }

        # Save report to file
        report_file = os.path.join(get_test_config('log_dir'),
                                 f'test_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)

        self.logger.info(f"Test report saved to: {report_file}")
        return report

    def generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results."""
        recommendations = []

        # Check for high failure rates
        for category, results in self.results.items():
            if results['total_tests'] > 0:
                failure_rate = (results['failed'] + results['errors']) / results['total_tests']
                if failure_rate > 0.2:  # More than 20% failure rate
                    recommendations.append(f"High failure rate in {category} ({failure_rate:.1%}) - requires immediate attention")

        # Check for missing test modules
        missing_modules = []
        for category, test_modules in self.test_categories.items():
            for module in test_modules:
                if any(error.get('error', '').startswith('Module not found')
                      for error in self.results[category].get('errors_list', [])):
                    missing_modules.append(module)

        if missing_modules:
            recommendations.append(f"Missing test modules: {', '.join(missing_modules)} - should be implemented")

        # Performance recommendations
        slow_categories = [cat for cat, results in self.results.items()
                          if results.get('execution_time', 0) > 30]
        if slow_categories:
            recommendations.append(f"Slow test categories: {', '.join(slow_categories)} - consider optimization")

        return recommendations

    def print_summary(self, report: Dict[str, Any]):
        """Print a formatted summary of test results."""
        print("\n" + "="*80)
        print("ECOCYCLE COMPREHENSIVE TEST RESULTS")
        print("="*80)

        summary = report['summary']
        print(f"Total tests: {summary['total_tests']}")
        print(f"Passed: {summary['passed']} ({summary['success_rate']:.1f}%)")
        print(f"Failed: {summary['failed']}")
        print(f"Errors: {summary['errors']}")
        print(f"Skipped: {summary['skipped']}")
        print(f"Execution Time: {summary['execution_time']:.2f} seconds")

        print("\nCategory Breakdown:")
        print("-" * 40)
        for category, results in report['category_results'].items():
            status = "✓" if results['failed'] + results['errors'] == 0 else "✗"
            print(f"{status} {category}: {results['passed']}/{results['total_tests']} passed")

        if report['recommendations']:
            print("\nRecommendations:")
            print("-" * 40)
            for i, rec in enumerate(report['recommendations'], 1):
                print(f"{i}. {rec}")

        print("\n" + "="*80)

def main():
    """Main function to run comprehensive tests."""
    runner = ComprehensiveTestRunner()
    runner.setup_logging()

    try:
        report = runner.run_all_tests()
        runner.print_summary(report)

        # Exit with appropriate code
        if report['summary']['failed'] + report['summary']['errors'] == 0:
            print("All tests passed! ✓")
            sys.exit(0)
        else:
            print("Some tests failed! ✗")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\nTest run interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Test run failed with error: {e}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
