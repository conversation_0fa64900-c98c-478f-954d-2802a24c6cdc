#!/usr/bin/env python3
"""
Authentication Integration tests
tests the complete authentication flow including registration, login,
email verification, session management, and security features.
"""

import os
import sys
import unittest
import tempfile
import json
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

from tests import EcoCycleTestCase, get_test_config
from auth.user_management.user_manager import UserManager
from auth.email_verification import EmailVerification
from auth.developer_auth import DeveloperAuth
from core.database_manager import DatabaseManager

class TestAuthIntegration(EcoCycleTestCase):
    """Integration tests for authentication system."""
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        
        # Create temporary files
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        self.temp_users_file = tempfile.NamedTemporaryFile(delete=False, suffix='.json')
        self.temp_users_file.close()
        
        # Mock external dependencies
        self.mock_sheets_manager = Mock()
        
        # Initialize components
        self.db_manager = DatabaseManager(self.temp_db.name)
        self.db_manager.initialize_database()
        
        self.user_manager = UserManager(self.mock_sheets_manager)
        self.user_manager.user_data_manager.users_file = self.temp_users_file.name
        
        self.email_verification = EmailVerification()
        self.developer_auth = DeveloperAuth()
        
    def tearDown(self):
        """Clean up test fixtures."""
        super().tearDown()
        
        # Clean up temporary files
        for temp_file in [self.temp_db.name, self.temp_users_file.name]:
            if os.path.exists(temp_file):
                try:
                    os.unlink(temp_file)
                except OSError:
                    pass
    
    def test_complete_registration_flow(self):
        """Test complete user registration with email verification."""
        test_username = 'integration_test_user'
        test_email = '<EMAIL>'
        test_password = 'SecurePassword123!'
        
        # Mock email sending
        with patch.object(self.email_verification, 'send_verification_email') as mock_send:
            mock_send.return_value = True
            
            # Mock user input for registration
            with patch('builtins.input', side_effect=[test_username, test_email, test_password, test_password]):
                with patch('getpass.getpass', return_value=test_password):
                    # Register user
                    result = self.user_manager.register_new_user()
                    
        self.assertTrue(result)
        self.assertIn(test_username, self.user_manager.users)
        
        # Verify user data
        user_data = self.user_manager.users[test_username]
        self.assertEqual(user_data['username'], test_username)
        self.assertEqual(user_data['email'], test_email)
        self.assertFalse(user_data.get('email_verified', False))
        
        # Test email verification
        verification_code = '123456'
        with patch.object(self.email_verification, 'verify_code') as mock_verify:
            mock_verify.return_value = True
            
            # Verify email
            verification_result = self.email_verification.verify_code(test_email, verification_code)
            self.assertTrue(verification_result)
            
            # Update user verification status
            user_data['email_verified'] = True
            self.user_manager.users[test_username] = user_data
        
        # Test login after verification
        with patch('getpass.getpass', return_value=test_password):
            login_result = self.user_manager.authenticate_user(test_username)
            
        self.assertTrue(login_result)
        self.assertEqual(self.user_manager.current_user, test_username)
    
    def test_login_with_email_verification_required(self):
        """Test login flow when email verification is required."""
        test_username = 'email_req_user'
        test_email = '<EMAIL>'
        test_password = 'TestPassword123!'
        
        # Create user without email verification
        user_data = self.user_manager.user_data_manager.create_user_data(
            username=test_username,
            email=test_email,
            password=test_password
        )
        user_data['email_verified'] = False
        self.user_manager.users[test_username] = user_data
        
        # Set email verification requirement
        user_data['preferences'] = {'require_email_verification': True}
        
        # Mock email verification process
        with patch.object(self.email_verification, 'send_verification_email') as mock_send:
            mock_send.return_value = True
            
            with patch.object(self.email_verification, 'verify_code') as mock_verify:
                mock_verify.return_value = True
                
                # Attempt login - should trigger email verification
                with patch('getpass.getpass', return_value=test_password):
                    with patch('builtins.input', return_value='123456'):  # Verification code
                        login_result = self.user_manager.authenticate_user(test_username)
                        
        self.assertTrue(login_result)
        mock_send.assert_called_once()
        mock_verify.assert_called_once()
    
    def test_two_factor_authentication(self):
        """Test two-factor authentication flow."""
        test_username = '2fa_test_user'
        test_email = '<EMAIL>'
        test_password = 'TwoFactorPassword123!'
        
        # Create user with 2FA enabled
        user_data = self.user_manager.user_data_manager.create_user_data(
            username=test_username,
            email=test_email,
            password=test_password
        )
        user_data['email_verified'] = True
        user_data['preferences'] = {'enable_two_factor': True}
        self.user_manager.users[test_username] = user_data
        
        # Mock 2FA code generation and sending
        with patch.object(self.email_verification, 'generate_verification_code') as mock_gen:
            mock_gen.return_value = '654321'
            
            with patch.object(self.email_verification, 'send_two_factor_code') as mock_send:
                mock_send.return_value = True
                
                with patch.object(self.email_verification, 'verify_code') as mock_verify:
                    mock_verify.return_value = True
                    
                    # Attempt login with 2FA
                    with patch('getpass.getpass', return_value=test_password):
                        with patch('builtins.input', return_value='654321'):  # 2FA code
                            login_result = self.user_manager.authenticate_user(test_username)
                            
        self.assertTrue(login_result)
        mock_send.assert_called_once()
        mock_verify.assert_called_once()
    
    def test_session_persistence(self):
        """Test session creation and persistence across application restarts."""
        test_username = 'session_test_user'
        test_password = 'SessionTestPassword123!'
        
        # Create and authenticate user
        user_data = self.user_manager.user_data_manager.create_user_data(
            username=test_username,
            email='<EMAIL>',
            password=test_password
        )
        self.user_manager.users[test_username] = user_data
        
        # Authenticate user
        with patch('getpass.getpass', return_value=test_password):
            login_result = self.user_manager.authenticate_user(test_username)
            
        self.assertTrue(login_result)
        self.assertEqual(self.user_manager.current_user, test_username)
        
        # Create session
        session_created = self.user_manager.auth_handler.create_session(test_username)
        self.assertTrue(session_created)
        
        # Simulate application restart by creating new UserManager instance
        new_user_manager = UserManager(self.mock_sheets_manager)
        new_user_manager.user_data_manager.users_file = self.temp_users_file.name
        new_user_manager.load_users()
        
        # Check if session is restored
        session_user = new_user_manager.auth_handler.restore_session()
        if session_user:
            self.assertEqual(session_user, test_username)
    
    def test_guest_authentication(self):
        """Test guest user authentication and limitations."""
        # Test guest login
        guest_result = self.user_manager.login_as_guest()
        self.assertTrue(guest_result)
        self.assertEqual(self.user_manager.current_user, 'guest')
        
        # Verify guest user properties
        guest_user = self.user_manager.users['guest']
        self.assertTrue(guest_user.get('is_guest', False))
        self.assertFalse(guest_user.get('is_admin', False))
        
        # Test guest limitations (should not be able to access admin features)
        self.assertFalse(self.user_manager.is_admin())
        
        # Test guest data persistence (should be limited)
        stats_result = self.user_manager.update_user_stats(5.0, 1.0, 250, 15.0)
        self.assertTrue(stats_result)  # Guests can log trips but data may not persist
    
    def test_developer_authentication(self):
        """Test developer mode authentication."""
        # Test developer authentication with correct password
        with patch('getpass.getpass', return_value='developer123'):
            dev_result = self.developer_auth.authenticate_developer()
            
        self.assertTrue(dev_result)
        
        # Test developer authentication with wrong password
        with patch('getpass.getpass', return_value='wrong_password'):
            dev_result = self.developer_auth.authenticate_developer()
            
        self.assertFalse(dev_result)
        
        # Test developer mode availability
        with patch.dict(os.environ, {'DEVELOPER_MODE_ENABLED': 'true'}):
            self.assertTrue(self.developer_auth.is_developer_mode_enabled())
            
        with patch.dict(os.environ, {'DEVELOPER_MODE_ENABLED': 'false'}):
            self.assertFalse(self.developer_auth.is_developer_mode_enabled())
    
    def test_password_reset_flow(self):
        """Test password reset functionality."""
        test_username = 'reset_test_user'
        test_email = '<EMAIL>'
        old_password = 'OldPassword123!'
        new_password = 'NewPassword456!'
        
        # Create user
        user_data = self.user_manager.user_data_manager.create_user_data(
            username=test_username,
            email=test_email,
            password=old_password
        )
        user_data['email_verified'] = True
        self.user_manager.users[test_username] = user_data
        
        # Mock password reset email
        with patch.object(self.email_verification, 'send_password_reset_email') as mock_send:
            mock_send.return_value = True
            
            with patch.object(self.email_verification, 'verify_reset_code') as mock_verify:
                mock_verify.return_value = True
                
                # Initiate password reset
                reset_result = self.user_manager.initiate_password_reset(test_email)
                self.assertTrue(reset_result)
                
                # Complete password reset
                with patch('getpass.getpass', side_effect=[new_password, new_password]):
                    with patch('builtins.input', return_value='123456'):  # Reset code
                        complete_result = self.user_manager.complete_password_reset(test_email, '123456', new_password)
                        self.assertTrue(complete_result)
        
        # Test login with new password
        with patch('getpass.getpass', return_value=new_password):
            login_result = self.user_manager.authenticate_user(test_username)
            
        self.assertTrue(login_result)
        
        # Test that old password no longer works
        with patch('getpass.getpass', return_value=old_password):
            old_login_result = self.user_manager.authenticate_user(test_username)
            
        self.assertFalse(old_login_result)
    
    def test_account_lockout_security(self):
        """Test account lockout after multiple failed login attempts."""
        test_username = 'lockout_test_user'
        test_password = 'LockoutTestPassword123!'
        
        # Create user
        user_data = self.user_manager.user_data_manager.create_user_data(
            username=test_username,
            email='<EMAIL>',
            password=test_password
        )
        self.user_manager.users[test_username] = user_data
        
        # Attempt multiple failed logins
        failed_attempts = 0
        max_attempts = 5
        
        for i in range(max_attempts + 1):
            with patch('getpass.getpass', return_value='wrong_password'):
                result = self.user_manager.authenticate_user(test_username)
                
            if not result:
                failed_attempts += 1
                
            # Check if account is locked after max attempts
            if failed_attempts >= max_attempts:
                user_data = self.user_manager.users[test_username]
                # Account should be locked or have lockout timestamp
                self.assertTrue(
                    user_data.get('account_locked', False) or 
                    user_data.get('lockout_until') is not None
                )
                break
    
    def test_security_headers_and_validation(self):
        """Test security measures and input validation."""
        # Test password strength validation
        weak_passwords = ['123', 'password', 'abc123', '']
        strong_password = 'StrongPassword123!@#'
        
        for weak_password in weak_passwords:
            is_strong = self.user_manager.auth_handler.validate_password_strength(weak_password)
            self.assertFalse(is_strong, f"Password '{weak_password}' should be considered weak")
        
        is_strong = self.user_manager.auth_handler.validate_password_strength(strong_password)
        self.assertTrue(is_strong, f"Password '{strong_password}' should be considered strong")
        
        # Test email validation
        invalid_emails = ['invalid', 'test@', '@example.com', 'test.example.com']
        valid_email = '<EMAIL>'
        
        for invalid_email in invalid_emails:
            is_valid = self.user_manager.auth_handler.validate_email(invalid_email)
            self.assertFalse(is_valid, f"Email '{invalid_email}' should be considered invalid")
        
        is_valid = self.user_manager.auth_handler.validate_email(valid_email)
        self.assertTrue(is_valid, f"Email '{valid_email}' should be considered valid")
        
        # Test username validation
        invalid_usernames = ['', 'a', 'user@name', 'user name', '123456789012345678901']
        valid_username = 'valid_username'
        
        for invalid_username in invalid_usernames:
            is_valid = self.user_manager.auth_handler.validate_username(invalid_username)
            self.assertFalse(is_valid, f"Username '{invalid_username}' should be considered invalid")
        
        is_valid = self.user_manager.auth_handler.validate_username(valid_username)
        self.assertTrue(is_valid, f"Username '{valid_username}' should be considered valid")

if __name__ == '__main__':
    unittest.main()
