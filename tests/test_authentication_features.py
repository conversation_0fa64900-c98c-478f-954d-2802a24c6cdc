#!/usr/bin/env python3
"""
Authentication Features tests
tests all authentication-related features including login, registration,
email verification, password management, and security features.
"""

import os
import sys
import unittest
import tempfile
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

from tests import EcoCycleTestCase, get_test_config
from auth.user_management.user_manager import UserManager
from auth.email_verification import EmailVerification

class TestAuthenticationFeatures(EcoCycleTestCase):
    """Feature tests for authentication system."""
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        
        # Create temporary files
        self.temp_users_file = tempfile.NamedTemporaryFile(delete=False, suffix='.json')
        self.temp_users_file.close()
        
        # Mock external dependencies
        self.mock_sheets_manager = Mock()
        
        # Initialize user manager
        self.user_manager = UserManager(self.mock_sheets_manager)
        self.user_manager.user_data_manager.users_file = self.temp_users_file.name
        
        # Initialize email verification
        self.email_verification = EmailVerification()
        
    def tearDown(self):
        """Clean up test fixtures."""
        super().tearDown()
        
        if os.path.exists(self.temp_users_file.name):
            try:
                os.unlink(self.temp_users_file.name)
            except OSError:
                pass
    
    def test_user_registration_feature(self):
        """Test complete user registration feature."""
        test_data = {
            'username': 'feature_test_user',
            'email': '<EMAIL>',
            'password': 'FeatureTestPassword123!',
            'confirm_password': 'FeatureTestPassword123!'
        }
        
        # Mock user input
        with patch('builtins.input', side_effect=[
            test_data['username'], 
            test_data['email'], 
            test_data['password'], 
            test_data['confirm_password']
        ]):
            with patch('getpass.getpass', return_value=test_data['password']):
                with patch.object(self.user_manager.user_registration, 'send_verification_email', return_value=True):
                    result = self.user_manager.register_new_user()
        
        self.assertTrue(result)
        self.assertIn(test_data['username'], self.user_manager.users)
        
        # Verify user data structure
        user = self.user_manager.users[test_data['username']]
        required_fields = ['username', 'email', 'password_hash', 'salt', 'registration_date']
        for field in required_fields:
            self.assertIn(field, user)
        
        # Verify password is hashed
        self.assertNotEqual(user['password_hash'], test_data['password'])
        
    def test_email_verification_feature(self):
        """Test email verification feature."""
        test_email = '<EMAIL>'
        
        # Test verification code generation
        code = self.email_verification.generate_verification_code()
        self.assertIsNotNone(code)
        self.assertEqual(len(code), 6)
        self.assertTrue(code.isdigit())
        
        # Test email template loading
        with patch.object(self.email_verification, 'load_email_template') as mock_load:
            mock_load.return_value = "Your verification code is: {code}"
            
            template = self.email_verification.load_email_template('email_verification')
            self.assertIsNotNone(template)
            self.assertIn('{code}', template)
        
        # Test verification code storage and validation
        verification_code = '123456'
        
        # Store verification code
        self.email_verification.store_verification_code(test_email, verification_code)
        
        # Verify code
        is_valid = self.email_verification.verify_code(test_email, verification_code)
        self.assertTrue(is_valid)
        
        # Test invalid code
        is_invalid = self.email_verification.verify_code(test_email, '654321')
        self.assertFalse(is_invalid)
        
        # Test expired code
        with patch.object(self.email_verification, 'is_code_expired', return_value=True):
            is_expired = self.email_verification.verify_code(test_email, verification_code)
            self.assertFalse(is_expired)
    
    def test_login_feature(self):
        """Test user login feature with various scenarios."""
        test_username = 'login_test_user'
        test_password = 'LoginTestPassword123!'
        
        # Create test user
        user_data = self.user_manager.user_data_manager.create_user_data(
            username=test_username,
            email='<EMAIL>',
            password=test_password
        )
        self.user_manager.users[test_username] = user_data
        
        # Test successful login
        with patch('getpass.getpass', return_value=test_password):
            result = self.user_manager.authenticate_user(test_username)
        
        self.assertTrue(result)
        self.assertEqual(self.user_manager.current_user, test_username)
        self.assertTrue(self.user_manager.is_authenticated())
        
        # Test login with wrong password
        self.user_manager.logout()
        with patch('getpass.getpass', return_value='wrong_password'):
            result = self.user_manager.authenticate_user(test_username)
        
        self.assertFalse(result)
        self.assertIsNone(self.user_manager.current_user)
        
        # Test login with non-existent user
        result = self.user_manager.authenticate_user('non_existent_user')
        self.assertFalse(result)
    
    def test_guest_login_feature(self):
        """Test guest login feature."""
        # Test guest login
        result = self.user_manager.login_as_guest()
        self.assertTrue(result)
        
        # Verify guest user properties
        self.assertEqual(self.user_manager.current_user, 'guest')
        self.assertTrue(self.user_manager.is_authenticated())
        
        guest_user = self.user_manager.users['guest']
        self.assertTrue(guest_user.get('is_guest', False))
        self.assertFalse(guest_user.get('is_admin', False))
        
        # Test guest user limitations
        self.assertFalse(self.user_manager.is_admin())
        
        # Test multiple guest logins (should create unique guest accounts)
        self.user_manager.logout()
        
        # Create additional guest users
        for i in range(1, 4):
            guest_result = self.user_manager.login_as_guest()
            self.assertTrue(guest_result)
            
            current_guest = self.user_manager.current_user
            self.assertTrue(current_guest.startswith('guest'))
            
            self.user_manager.logout()
    
    def test_password_management_features(self):
        """Test password-related features."""
        # Test password strength validation
        weak_passwords = [
            '123',           # Too short
            'password',      # Common word
            '********',      # Only numbers
            'abcdefgh',      # Only letters
            'Password',      # Missing special chars and numbers
        ]
        
        strong_passwords = [
            'StrongPass123!',
            'MySecure@Password456',
            'Complex#Pass789$',
        ]
        
        for weak_pass in weak_passwords:
            is_strong = self.user_manager.auth_handler.validate_password_strength(weak_pass)
            self.assertFalse(is_strong, f"Password '{weak_pass}' should be weak")
        
        for strong_pass in strong_passwords:
            is_strong = self.user_manager.auth_handler.validate_password_strength(strong_pass)
            self.assertTrue(is_strong, f"Password '{strong_pass}' should be strong")
        
        # Test password hashing and verification
        test_password = 'TestPassword123!'
        password_hash, salt = self.user_manager.auth_handler.hash_password(test_password)
        
        self.assertIsNotNone(password_hash)
        self.assertIsNotNone(salt)
        self.assertNotEqual(password_hash, test_password)
        
        # Verify password
        is_valid = self.user_manager.auth_handler.verify_password(test_password, password_hash, salt)
        self.assertTrue(is_valid)
        
        # Test wrong password
        is_invalid = self.user_manager.auth_handler.verify_password('WrongPassword', password_hash, salt)
        self.assertFalse(is_invalid)
    
    def test_session_management_features(self):
        """Test session management features."""
        test_username = 'session_feature_user'
        test_password = 'SessionPassword123!'
        
        # Create and authenticate user
        user_data = self.user_manager.user_data_manager.create_user_data(
            username=test_username,
            email='<EMAIL>',
            password=test_password
        )
        self.user_manager.users[test_username] = user_data
        
        with patch('getpass.getpass', return_value=test_password):
            login_result = self.user_manager.authenticate_user(test_username)
        
        self.assertTrue(login_result)
        
        # Test session creation
        session_result = self.user_manager.auth_handler.create_session(test_username)
        self.assertTrue(session_result)
        
        # Test session validation
        is_valid = self.user_manager.auth_handler.validate_session(test_username)
        self.assertTrue(is_valid)
        
        # Test session timeout
        with patch.object(self.user_manager.auth_handler, 'is_session_expired', return_value=True):
            is_expired = self.user_manager.auth_handler.validate_session(test_username)
            self.assertFalse(is_expired)
        
        # Test logout and session cleanup
        self.user_manager.logout()
        self.assertIsNone(self.user_manager.current_user)
        self.assertFalse(self.user_manager.is_authenticated())
    
    def test_user_preferences_features(self):
        """Test user preferences management features."""
        test_username = 'prefs_feature_user'
        
        # Create and authenticate user
        user_data = self.user_manager.user_data_manager.create_user_data(
            username=test_username,
            email='<EMAIL>',
            password='PrefsPassword123!'
        )
        self.user_manager.users[test_username] = user_data
        self.user_manager.current_user = test_username
        
        # Test setting various preferences
        preferences = {
            'weight_kg': 75.5,
            'default_transport_mode': 'e-bike',
            'theme': 'dark',
            'units': 'metric',
            'require_email_verification': True,
            'enable_two_factor': False,
            'notification_frequency': 'weekly'
        }
        
        for key, value in preferences.items():
            self.user_manager.update_user_preference(key, value)
            retrieved_value = self.user_manager.get_user_preference(key)
            self.assertEqual(retrieved_value, value)
        
        # Test preference persistence
        self.user_manager.save_users()
        
        # Create new user manager and load data
        new_user_manager = UserManager(self.mock_sheets_manager)
        new_user_manager.user_data_manager.users_file = self.temp_users_file.name
        new_user_manager.load_users()
        new_user_manager.current_user = test_username
        
        # Verify preferences were persisted
        for key, expected_value in preferences.items():
            actual_value = new_user_manager.get_user_preference(key)
            self.assertEqual(actual_value, expected_value)
    
    def test_security_features(self):
        """Test security-related features."""
        # Test input validation
        test_inputs = {
            'usernames': {
                'valid': ['user123', 'test_user', 'valid-user'],
                'invalid': ['', 'a', 'user@name', 'user name', 'x' * 50]
            },
            'emails': {
                'valid': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                'invalid': ['invalid', 'test@', '@example.com', 'test.example.com', '']
            }
        }
        
        # Test username validation
        for valid_username in test_inputs['usernames']['valid']:
            is_valid = self.user_manager.auth_handler.validate_username(valid_username)
            self.assertTrue(is_valid, f"Username '{valid_username}' should be valid")
        
        for invalid_username in test_inputs['usernames']['invalid']:
            is_valid = self.user_manager.auth_handler.validate_username(invalid_username)
            self.assertFalse(is_valid, f"Username '{invalid_username}' should be invalid")
        
        # Test email validation
        for valid_email in test_inputs['emails']['valid']:
            is_valid = self.user_manager.auth_handler.validate_email(valid_email)
            self.assertTrue(is_valid, f"Email '{valid_email}' should be valid")
        
        for invalid_email in test_inputs['emails']['invalid']:
            is_valid = self.user_manager.auth_handler.validate_email(invalid_email)
            self.assertFalse(is_valid, f"Email '{invalid_email}' should be invalid")
        
        # Test rate limiting simulation
        test_username = 'rate_limit_user'
        failed_attempts = 0
        max_attempts = 5
        
        for i in range(max_attempts + 2):
            # Simulate failed login attempt
            with patch('getpass.getpass', return_value='wrong_password'):
                result = self.user_manager.authenticate_user(test_username)
            
            if not result:
                failed_attempts += 1
            
            # Check if rate limiting would be triggered
            if failed_attempts >= max_attempts:
                # In a real implementation, this would trigger rate limiting
                self.assertGreaterEqual(failed_attempts, max_attempts)
                break
    
    def test_account_recovery_features(self):
        """Test account recovery and password reset features."""
        test_username = 'recovery_test_user'
        test_email = '<EMAIL>'
        old_password = 'OldPassword123!'
        new_password = 'NewPassword456!'
        
        # Create user
        user_data = self.user_manager.user_data_manager.create_user_data(
            username=test_username,
            email=test_email,
            password=old_password
        )
        user_data['email_verified'] = True
        self.user_manager.users[test_username] = user_data
        
        # Test password reset initiation
        with patch.object(self.email_verification, 'send_password_reset_email') as mock_send:
            mock_send.return_value = True
            
            reset_result = self.user_manager.initiate_password_reset(test_email)
            self.assertTrue(reset_result)
            mock_send.assert_called_once()
        
        # Test password reset completion
        reset_code = '123456'
        with patch.object(self.email_verification, 'verify_reset_code') as mock_verify:
            mock_verify.return_value = True
            
            with patch('getpass.getpass', side_effect=[new_password, new_password]):
                complete_result = self.user_manager.complete_password_reset(test_email, reset_code, new_password)
                self.assertTrue(complete_result)
        
        # Verify password was changed
        user_data = self.user_manager.users[test_username]
        new_hash = user_data['password_hash']
        
        # Test login with new password
        with patch('getpass.getpass', return_value=new_password):
            login_result = self.user_manager.authenticate_user(test_username)
        
        self.assertTrue(login_result)

if __name__ == '__main__':
    unittest.main()
