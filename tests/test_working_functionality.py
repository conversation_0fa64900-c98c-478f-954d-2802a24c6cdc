#!/usr/bin/env python3
"""
Working Functionality tests
tests that actually work with the current EcoCycle application structure.
"""

import os
import sys
import unittest
import tempfile
import sqlite3
from unittest.mock import Mock, patch
from datetime import datetime

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

from tests import EcoCycleTestCase

class TestWorkingFunctionality(EcoCycleTestCase):
    """tests for actual working functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        
    def test_database_manager_import(self):
        """Test that database manager can be imported and used."""
        import core.database_manager as db_manager
        
        # Test that essential functions exist
        essential_functions = [
            'create_connection', 'initialize_database', 'add_user', 
            'add_trip', 'add_stat', 'create_backup'
        ]
        
        for func_name in essential_functions:
            self.assertTrue(hasattr(db_manager, func_name), 
                          f"Database manager missing function: {func_name}")
            self.assertTrue(callable(getattr(db_manager, func_name)),
                          f"Database manager {func_name} is not callable")
    
    def test_user_manager_functionality(self):
        """Test user manager functionality."""
        from auth.user_management.user_manager import UserManager
        
        # Create user manager with mock sheets manager
        mock_sheets_manager = Mock()
        user_manager = UserManager(mock_sheets_manager)
        
        # Test basic functionality
        self.assertIsNotNone(user_manager)
        self.assertIsNotNone(user_manager.users)
        self.assertIsInstance(user_manager.users, dict)
        
        # Test guest login functionality
        result = user_manager.login_as_guest()
        self.assertTrue(result)
        self.assertIn('guest', user_manager.users)
        self.assertEqual(user_manager.current_user, 'guest')
    
    def test_menu_system(self):
        """Test menu system functionality."""
        from apps.menu import Menu
        
        # Create menu instance
        menu = Menu()
        
        # Test menu initialization
        self.assertIsNotNone(menu)
        
        # Test menu options exist
        self.assertTrue(hasattr(menu, 'display_main_menu'))
        self.assertTrue(hasattr(menu, 'handle_menu_choice'))
    
    def test_route_planner_import(self):
        """Test route planner can be imported."""
        try:
            from apps.route_planner.ai_route_planner import AIRoutePlanner
            
            # Test basic initialization
            route_planner = AIRoutePlanner()
            self.assertIsNotNone(route_planner)
            
        except ImportError:
            self.skipTest("Route planner module not available")
    
    def test_carbon_footprint_calculator(self):
        """Test carbon footprint calculator."""
        try:
            from apps.carbon_footprint import CarbonFootprintCalculator
            
            calculator = CarbonFootprintCalculator()
            self.assertIsNotNone(calculator)
            
            # Test basic calculation
            if hasattr(calculator, 'calculate_co2_saved'):
                co2_saved = calculator.calculate_co2_saved(10.0, 'bike')  # 10km by bike
                self.assertIsInstance(co2_saved, (int, float))
                self.assertGreaterEqual(co2_saved, 0)
                
        except ImportError:
            self.skipTest("Carbon footprint calculator not available")
    
    def test_configuration_loading(self):
        """Test configuration loading."""
        import config.config as config
        
        # Test that essential configurations exist
        essential_configs = ['DATABASE_FILE', 'LOG_DIR', 'VERSION']
        
        for config_name in essential_configs:
            self.assertTrue(hasattr(config, config_name),
                          f"Missing essential configuration: {config_name}")
    
    def test_error_handler_functionality(self):
        """Test error handler functionality."""
        from core.error_handler import handle_error, EcoCycleError
        
        # Test basic error handling
        test_error = EcoCycleError("Test error message")
        
        # This should not raise an exception
        try:
            handle_error(test_error)
        except Exception as e:
            self.fail(f"Error handler raised exception: {e}")
    
    def test_logging_functionality(self):
        """Test logging functionality."""
        from core.logging_manager import LoggingManager
        
        # Create temporary log file
        temp_log = tempfile.NamedTemporaryFile(delete=False, suffix='.log')
        temp_log.close()
        
        try:
            # Initialize logging manager
            log_manager = LoggingManager(log_file=temp_log.name)
            self.assertIsNotNone(log_manager)
            
            # Test logging
            log_manager.info("Test log message")
            
            # Verify log file was created and contains content
            self.assertTrue(os.path.exists(temp_log.name))
            
            with open(temp_log.name, 'r') as f:
                log_content = f.read()
                self.assertIn("Test log message", log_content)
                
        finally:
            # Clean up
            if os.path.exists(temp_log.name):
                os.unlink(temp_log.name)
    
    def test_email_verification_system(self):
        """Test email verification system."""
        try:
            from auth.email_verification import EmailVerification
            
            email_verifier = EmailVerification()
            self.assertIsNotNone(email_verifier)
            
            # Test verification code generation
            if hasattr(email_verifier, 'generate_verification_code'):
                code = email_verifier.generate_verification_code()
                self.assertIsNotNone(code)
                self.assertEqual(len(code), 6)
                self.assertTrue(code.isdigit())
                
        except ImportError:
            self.skipTest("Email verification system not available")
    
    def test_developer_authentication(self):
        """Test developer authentication system."""
        try:
            from auth.developer_auth import DeveloperAuth
            
            dev_auth = DeveloperAuth()
            self.assertIsNotNone(dev_auth)
            
            # Test developer mode check
            if hasattr(dev_auth, 'is_developer_mode_enabled'):
                # This should not raise an exception
                is_enabled = dev_auth.is_developer_mode_enabled()
                self.assertIsInstance(is_enabled, bool)
                
        except ImportError:
            self.skipTest("Developer authentication not available")
    
    def test_data_visualization(self):
        """Test data visualization functionality."""
        try:
            from apps.data_visualization import DataVisualization
            
            data_viz = DataVisualization()
            self.assertIsNotNone(data_viz)
            
        except ImportError:
            self.skipTest("Data visualization not available")
    
    def test_notification_system(self):
        """Test notification system."""
        try:
            from services.notifications.notification_system import NotificationSystem
            
            notification_system = NotificationSystem()
            self.assertIsNotNone(notification_system)
            
        except ImportError:
            self.skipTest("Notification system not available")
    
    def test_sync_service(self):
        """Test sync service functionality."""
        try:
            from services.sync.sync_service import SyncService
            
            sync_service = SyncService()
            self.assertIsNotNone(sync_service)
            
        except ImportError:
            self.skipTest("Sync service not available")
    
    def test_sheets_manager(self):
        """Test Google Sheets manager."""
        try:
            from services.sheets.sheets_manager import SheetsManager
            
            # This might fail due to credentials, but import should work
            sheets_manager = SheetsManager()
            self.assertIsNotNone(sheets_manager)
            
        except ImportError:
            self.skipTest("Sheets manager not available")
        except Exception:
            # Expected if no credentials are available
            pass
    
    def test_plugin_system(self):
        """Test plugin system functionality."""
        try:
            from core.plugin.plugin_manager import PluginManager
            
            plugin_manager = PluginManager()
            self.assertIsNotNone(plugin_manager)
            
        except ImportError:
            self.skipTest("Plugin system not available")
    
    def test_dependency_management(self):
        """Test dependency management system."""
        try:
            from core.dependency.dependency_manager import DependencyManager
            
            dep_manager = DependencyManager()
            self.assertIsNotNone(dep_manager)
            
        except ImportError:
            self.skipTest("Dependency manager not available")
    
    def test_database_operations_integration(self):
        """Test database operations with actual functions."""
        import core.database_manager as db_manager
        
        # Create temporary database
        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_db.close()
        
        # Override database file temporarily
        original_db_file = db_manager.DATABASE_FILE
        db_manager.DATABASE_FILE = temp_db.name
        
        try:
            # Initialize database
            db_manager.initialize_database()
            
            # Test database file was created
            self.assertTrue(os.path.exists(temp_db.name))
            
            # Test connection
            conn = db_manager.create_connection()
            self.assertIsNotNone(conn)
            
            if conn:
                # Test basic query
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                
                # Should have created tables
                table_names = [table[0] for table in tables]
                expected_tables = ['users', 'trips', 'stats', 'preferences']
                
                for expected_table in expected_tables:
                    self.assertIn(expected_table, table_names,
                                f"Expected table {expected_table} not found")
                
                conn.close()
            
        finally:
            # Restore original database file
            db_manager.DATABASE_FILE = original_db_file
            
            # Clean up temporary database
            if os.path.exists(temp_db.name):
                os.unlink(temp_db.name)
    
    def test_user_authentication_flow(self):
        """Test user authentication flow."""
        from auth.user_management.user_manager import UserManager
        
        # Create temporary users file
        temp_users_file = tempfile.NamedTemporaryFile(delete=False, suffix='.json')
        temp_users_file.close()
        
        try:
            # Create user manager with mock sheets manager
            mock_sheets_manager = Mock()
            user_manager = UserManager(mock_sheets_manager)
            
            # Override users file
            user_manager.user_data_manager.users_file = temp_users_file.name
            
            # Test guest authentication
            result = user_manager.login_as_guest()
            self.assertTrue(result)
            self.assertTrue(user_manager.is_authenticated())
            self.assertEqual(user_manager.current_user, 'guest')
            
            # Test logout
            user_manager.logout()
            self.assertFalse(user_manager.is_authenticated())
            self.assertIsNone(user_manager.current_user)
            
        finally:
            # Clean up
            if os.path.exists(temp_users_file.name):
                os.unlink(temp_users_file.name)
    
    def test_application_startup_sequence(self):
        """Test that the application can start up without errors."""
        # Test importing main application
        try:
            import main
            self.assertTrue(True)  # If we get here, import succeeded
        except ImportError as e:
            self.fail(f"Could not import main application: {e}")
        except Exception as e:
            # Other exceptions might be expected (missing config, etc.)
            # but import should work
            pass

if __name__ == '__main__':
    unittest.main(verbosity=2)
