#!/usr/bin/env python3
"""
EcoCycle - Comprehensive Security Testing Module
tests authentication penetration, input validation, and session management security.
"""

import os
import sys
import unittest
import tempfile
import time
import threading
import hashlib
import hmac
import json
import random
import string
import base64
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from concurrent.futures import ThreadPoolExecutor, as_completed
try:
    from faker import Faker
except ImportError:
    # Fallback if faker is not available
    class Faker:
        def __init__(self):
            pass

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

from tests import EcoCycleTestCase, get_test_config
from auth.user_management.user_manager import UserManager
from auth.user_management.password_security import PasswordSecurity
from auth.user_management.session_manager import SessionManager
from auth.email_verification import EmailVerification
from core.database_manager import DatabaseManager
from web.web_app import app

class TestSecurityComprehensive(EcoCycleTestCase):
    """Comprehensive security tests for EcoCycle application."""

    def setUp(self):
        """Set up test fixtures."""
        super().setUp()

        # Initialize faker for generating test data
        self.fake = Faker()

        # Create temporary database
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()

        # Initialize components
        self.db_manager = DatabaseManager(self.temp_db.name)
        self.db_manager.initialize_database()
        self.user_manager = UserManager()
        self.password_security = PasswordSecurity()
        self.session_manager = SessionManager()
        self.email_verification = EmailVerification()

        # Flask test client
        app.config['TESTING'] = True
        self.client = app.test_client()

        # Security test results storage
        self.security_results = {
            'authentication_tests': [],
            'input_validation_tests': [],
            'session_management_tests': [],
            'penetration_test_results': []
        }

        # Create test user for security testing
        self.test_user = self._create_test_user()

    def tearDown(self):
        """Clean up test fixtures."""
        super().tearDown()
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)

    def _create_test_user(self):
        """Create a test user for security testing."""
        username = 'security_test_user'
        password = 'SecureTestPassword123!'

        # Create user data directly
        salt = self.password_security.generate_salt()
        password_hash = self.password_security.hash_password(password, salt)

        user_data = {
            'username': username,
            'email': '<EMAIL>',
            'password_hash': password_hash,
            'salt': salt,
            'name': 'Security Test User',
            'is_admin': False,
            'is_guest': False,
            'registration_date': datetime.now().isoformat(),
            'failed_login_attempts': 0,
            'account_locked': False
        }

        # Add user to user manager
        self.user_manager.users[username] = user_data
        return {'username': username, 'password': password, 'data': user_data}

    def test_authentication_penetration_testing(self):
        """Test authentication system against various attack vectors."""
        test_results = []

        # Test 1: Brute Force Attack Simulation
        brute_force_result = self._test_brute_force_attack()
        test_results.append(brute_force_result)

        # Test 2: Password Strength Validation
        password_strength_result = self._test_password_strength_validation()
        test_results.append(password_strength_result)

        # Test 3: Account Lockout Testing
        account_lockout_result = self._test_account_lockout()
        test_results.append(account_lockout_result)

        # Test 4: Session Hijacking Prevention
        session_hijacking_result = self._test_session_hijacking_prevention()
        test_results.append(session_hijacking_result)

        # Store results
        self.security_results['authentication_tests'] = test_results

        # Verify all tests passed security requirements
        for result in test_results:
            self.assertTrue(result['security_passed'],
                          f"Security test failed: {result['test_name']} - {result.get('failure_reason', '')}")

    def _test_brute_force_attack(self):
        """Simulate brute force attack on authentication."""
        test_name = "Brute Force Attack Simulation"
        username = self.test_user['username']

        # Common passwords to try
        common_passwords = [
            'password', '123456', 'password123', 'admin', 'qwerty',
            'letmein', 'welcome', 'monkey', '**********', 'password1'
        ]

        failed_attempts = 0
        successful_attempts = 0
        start_time = time.time()

        for password in common_passwords:
            try:
                # Attempt authentication
                with patch('getpass.getpass', return_value=password):
                    result = self.user_manager.authenticate_user(username)

                if result:
                    successful_attempts += 1
                else:
                    failed_attempts += 1

                # Add small delay to simulate real attack
                time.sleep(0.1)

            except Exception as e:
                failed_attempts += 1

        attack_duration = time.time() - start_time

        # Security assertion: Brute force should not succeed
        security_passed = successful_attempts == 0

        return {
            'test_name': test_name,
            'failed_attempts': failed_attempts,
            'successful_attempts': successful_attempts,
            'attack_duration': attack_duration,
            'security_passed': security_passed,
            'failure_reason': 'Brute force attack succeeded' if not security_passed else None
        }

    def _test_password_strength_validation(self):
        """Test password strength validation against weak passwords."""
        test_name = "Password Strength Validation"

        weak_passwords = [
            '123', 'password', 'abc123', 'qwerty', '111111',
            'password123', 'admin', 'user', 'test', ''
        ]

        strong_passwords = [
            'StrongPassword123!', 'MySecure@Pass2024', 'Complex#Password99',
            'Unbreakable$2024', 'SecureAuth!123'
        ]

        weak_rejected = 0
        strong_accepted = 0

        # Test weak passwords (should be rejected)
        for password in weak_passwords:
            is_strong = self.password_security.check_password_strength(password)
            if not is_strong['is_strong']:
                weak_rejected += 1

        # Test strong passwords (should be accepted)
        for password in strong_passwords:
            is_strong = self.password_security.check_password_strength(password)
            if is_strong['is_strong']:
                strong_accepted += 1

        # Security assertion: All weak passwords should be rejected, all strong accepted
        security_passed = (weak_rejected == len(weak_passwords) and
                          strong_accepted == len(strong_passwords))

        return {
            'test_name': test_name,
            'weak_passwords_tested': len(weak_passwords),
            'weak_passwords_rejected': weak_rejected,
            'strong_passwords_tested': len(strong_passwords),
            'strong_passwords_accepted': strong_accepted,
            'security_passed': security_passed,
            'failure_reason': 'Password strength validation failed' if not security_passed else None
        }

    def _test_account_lockout(self):
        """Test account lockout after multiple failed attempts."""
        test_name = "Account Lockout Testing"

        # Create a temporary user for lockout testing
        lockout_username = 'lockout_test_user'
        lockout_password = 'LockoutTestPassword123!'

        user_data = {
            'username': lockout_username,
            'email': '<EMAIL>',
            'password_hash': self.password_security.hash_password(lockout_password, 'test_salt'),
            'salt': 'test_salt',
            'is_admin': 0,
            'is_guest': 0,
            'registration_date': datetime.now().isoformat(),
            'failed_login_attempts': 0,
            'account_locked': False
        }

        self.user_manager.users[lockout_username] = user_data

        # Simulate multiple failed login attempts
        failed_attempts = 0
        max_attempts = 5

        for attempt in range(max_attempts + 2):  # Try more than max allowed
            with patch('getpass.getpass', return_value='wrong_password'):
                result = self.user_manager.authenticate_user(lockout_username)

            if not result:
                failed_attempts += 1

        # Check if account is locked after max attempts
        user_data = self.user_manager.users.get(lockout_username, {})
        account_locked = user_data.get('account_locked', False)

        # Security assertion: Account should be locked after max failed attempts
        security_passed = account_locked and failed_attempts >= max_attempts

        return {
            'test_name': test_name,
            'failed_attempts': failed_attempts,
            'max_allowed_attempts': max_attempts,
            'account_locked': account_locked,
            'security_passed': security_passed,
            'failure_reason': 'Account lockout mechanism failed' if not security_passed else None
        }

    def _test_session_hijacking_prevention(self):
        """Test session security against hijacking attempts."""
        test_name = "Session Hijacking Prevention"

        username = self.test_user['username']

        # Create legitimate session
        session_saved = self.session_manager.save_session(username)
        self.assertTrue(session_saved, "Failed to create test session")

        # Load session to get session data
        loaded_user = self.session_manager.load_session()
        self.assertEqual(loaded_user, username, "Session loading failed")

        # Test 1: Session tampering
        session_file = self.session_manager.SESSION_FILE
        if os.path.exists(session_file):
            with open(session_file, 'r') as f:
                session_data = json.load(f)

            # Tamper with session data
            original_verifier = session_data.get('session_verifier')
            session_data['session_verifier'] = 'tampered_verifier'
            session_data['username'] = 'malicious_user'

            with open(session_file, 'w') as f:
                json.dump(session_data, f)

            # Try to load tampered session
            tampered_user = self.session_manager.load_session()

            # Security assertion: Tampered session should be rejected
            session_tamper_prevented = tampered_user is None
        else:
            session_tamper_prevented = False

        # Test 2: Session timeout
        # Simulate old session
        if os.path.exists(session_file):
            with open(session_file, 'r') as f:
                session_data = json.load(f)

            # Set session to be very old
            old_time = time.time() - (24 * 60 * 60 + 1)  # More than 24 hours old
            session_data['created_at'] = old_time
            session_data['last_accessed'] = old_time

            with open(session_file, 'w') as f:
                json.dump(session_data, f)

            # Try to load expired session
            expired_user = self.session_manager.load_session()

            # Security assertion: Expired session should be rejected
            session_timeout_working = expired_user is None
        else:
            session_timeout_working = False

        security_passed = session_tamper_prevented and session_timeout_working

        return {
            'test_name': test_name,
            'session_tamper_prevented': session_tamper_prevented,
            'session_timeout_working': session_timeout_working,
            'security_passed': security_passed,
            'failure_reason': 'Session security mechanisms failed' if not security_passed else None
        }

    def test_input_validation_testing(self):
        """Test input validation against various injection attacks."""
        test_results = []

        # Test 1: SQL Injection Testing
        sql_injection_result = self._test_sql_injection()
        test_results.append(sql_injection_result)

        # Test 2: XSS Vulnerability Testing
        xss_result = self._test_xss_vulnerability()
        test_results.append(xss_result)

        # Test 3: File Upload Security
        file_upload_result = self._test_file_upload_security()
        test_results.append(file_upload_result)

        # Test 4: CSRF Protection
        csrf_result = self._test_csrf_protection()
        test_results.append(csrf_result)

        # Store results
        self.security_results['input_validation_tests'] = test_results

        # Verify all tests passed security requirements
        for result in test_results:
            self.assertTrue(result['security_passed'],
                          f"Input validation test failed: {result['test_name']} - {result.get('failure_reason', '')}")

    def _test_sql_injection(self):
        """Test SQL injection vulnerability."""
        test_name = "SQL Injection Testing"

        # Common SQL injection payloads
        sql_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "' UNION SELECT * FROM users --",
            "'; INSERT INTO users VALUES ('hacker', 'password'); --",
            "' OR 1=1 --",
            "admin'--",
            "' OR 'x'='x",
            "'; EXEC xp_cmdshell('dir'); --"
        ]

        injection_attempts_blocked = 0

        for payload in sql_payloads:
            try:
                # Test SQL injection in username field
                with patch('getpass.getpass', return_value='test_password'):
                    result = self.user_manager.authenticate_user(payload)

                # If authentication fails (as it should), injection was blocked
                if not result:
                    injection_attempts_blocked += 1

            except Exception as e:
                # Exception indicates injection was blocked
                injection_attempts_blocked += 1

        # Security assertion: All SQL injection attempts should be blocked
        security_passed = injection_attempts_blocked == len(sql_payloads)

        return {
            'test_name': test_name,
            'injection_attempts': len(sql_payloads),
            'attempts_blocked': injection_attempts_blocked,
            'security_passed': security_passed,
            'failure_reason': 'SQL injection vulnerability detected' if not security_passed else None
        }

    def _test_xss_vulnerability(self):
        """Test XSS vulnerability in web interface."""
        test_name = "XSS Vulnerability Testing"

        # Common XSS payloads
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>",
            "javascript:alert('XSS')",
            "<iframe src=javascript:alert('XSS')></iframe>",
            "<body onload=alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>",
            "<select onfocus=alert('XSS') autofocus>"
        ]

        xss_attempts_blocked = 0

        # Test XSS in various form fields
        for payload in xss_payloads:
            try:
                # Test in trip logging form
                with self.client.session_transaction() as sess:
                    sess['username'] = self.test_user['username']

                trip_data = {
                    'date': datetime.now().isoformat(),
                    'distance': payload,  # XSS payload in distance field
                    'duration': '20.0',
                    'co2_saved': '1.0',
                    'calories': '300'
                }

                response = self.client.post('/api/trips',
                                          json=trip_data,
                                          content_type='application/json')

                # Check if payload was sanitized or rejected
                if response.status_code == 400 or 'error' in response.get_json(silent=True) or {}:
                    xss_attempts_blocked += 1
                else:
                    # Check if response contains unsanitized payload
                    response_text = response.get_data(as_text=True)
                    if payload not in response_text:
                        xss_attempts_blocked += 1

            except Exception as e:
                # Exception indicates XSS was blocked
                xss_attempts_blocked += 1

        # Security assertion: All XSS attempts should be blocked
        security_passed = xss_attempts_blocked >= len(xss_payloads) * 0.8  # Allow 80% success rate

        return {
            'test_name': test_name,
            'xss_attempts': len(xss_payloads),
            'attempts_blocked': xss_attempts_blocked,
            'security_passed': security_passed,
            'failure_reason': 'XSS vulnerability detected' if not security_passed else None
        }

    def _test_file_upload_security(self):
        """Test file upload security against malicious files."""
        test_name = "File Upload Security Testing"

        # Malicious file types and content
        malicious_files = [
            {'name': 'malicious.php', 'content': b'<?php system($_GET["cmd"]); ?>', 'type': 'application/x-php'},
            {'name': 'script.js', 'content': b'alert("XSS");', 'type': 'application/javascript'},
            {'name': 'exploit.exe', 'content': b'MZ\x90\x00\x03\x00\x00\x00', 'type': 'application/x-msdownload'},
            {'name': 'shell.jsp', 'content': b'<%@ page import="java.io.*" %>', 'type': 'application/x-jsp'},
            {'name': 'backdoor.asp', 'content': b'<%eval request("cmd")%>', 'type': 'application/x-asp'},
            {'name': '../../../etc/passwd', 'content': b'root:x:0:0:root:/root:/bin/bash', 'type': 'text/plain'},
            {'name': 'huge_file.txt', 'content': b'A' * (20 * 1024 * 1024), 'type': 'text/plain'}  # 20MB file
        ]

        uploads_blocked = 0

        for malicious_file in malicious_files:
            try:
                # Create temporary file
                with tempfile.NamedTemporaryFile(delete=False, suffix='.tmp') as tmp_file:
                    tmp_file.write(malicious_file['content'])
                    tmp_file_path = tmp_file.name

                # Test file upload through web interface
                with self.client.session_transaction() as sess:
                    sess['username'] = self.test_user['username']

                with open(tmp_file_path, 'rb') as test_file:
                    response = self.client.post('/api/upload',
                                              data={'file': (test_file, malicious_file['name'])},
                                              content_type='multipart/form-data')

                # Check if upload was blocked
                if response.status_code >= 400:
                    uploads_blocked += 1
                elif 'error' in response.get_json(silent=True) or {}:
                    uploads_blocked += 1

                # Clean up
                os.unlink(tmp_file_path)

            except Exception as e:
                # Exception indicates upload was blocked
                uploads_blocked += 1

        # Security assertion: All malicious uploads should be blocked
        security_passed = uploads_blocked >= len(malicious_files) * 0.8  # Allow 80% success rate

        return {
            'test_name': test_name,
            'malicious_uploads': len(malicious_files),
            'uploads_blocked': uploads_blocked,
            'security_passed': security_passed,
            'failure_reason': 'File upload security vulnerability detected' if not security_passed else None
        }

    def _test_csrf_protection(self):
        """Test CSRF protection mechanisms."""
        test_name = "CSRF Protection Testing"

        csrf_attempts_blocked = 0
        total_attempts = 0

        # Test CSRF on sensitive operations
        sensitive_operations = [
            {'method': 'POST', 'url': '/api/trips', 'data': {'distance': '5.0'}},
            {'method': 'DELETE', 'url': '/api/trips/1', 'data': {}},
            {'method': 'POST', 'url': '/api/routes', 'data': {'name': 'Test Route'}},
            {'method': 'PUT', 'url': '/api/user/settings', 'data': {'theme': 'dark'}}
        ]

        for operation in sensitive_operations:
            try:
                total_attempts += 1

                # Attempt operation without proper CSRF token
                if operation['method'] == 'POST':
                    response = self.client.post(operation['url'],
                                              json=operation['data'],
                                              content_type='application/json')
                elif operation['method'] == 'DELETE':
                    response = self.client.delete(operation['url'])
                elif operation['method'] == 'PUT':
                    response = self.client.put(operation['url'],
                                             json=operation['data'],
                                             content_type='application/json')

                # Check if CSRF protection blocked the request
                if response.status_code == 403 or response.status_code == 401:
                    csrf_attempts_blocked += 1
                elif 'csrf' in response.get_data(as_text=True).lower():
                    csrf_attempts_blocked += 1

            except Exception as e:
                # Exception indicates CSRF protection worked
                csrf_attempts_blocked += 1

        # Security assertion: CSRF protection should block unauthorized requests
        security_passed = csrf_attempts_blocked >= total_attempts * 0.5  # Allow 50% success rate

        return {
            'test_name': test_name,
            'csrf_attempts': total_attempts,
            'attempts_blocked': csrf_attempts_blocked,
            'security_passed': security_passed,
            'failure_reason': 'CSRF protection insufficient' if not security_passed else None
        }

    def test_session_management_testing(self):
        """Test session management security features."""
        test_results = []

        # Test 1: Session Timeout Validation
        session_timeout_result = self._test_session_timeout()
        test_results.append(session_timeout_result)

        # Test 2: Session Fixation Prevention
        session_fixation_result = self._test_session_fixation()
        test_results.append(session_fixation_result)

        # Test 3: Concurrent Session Handling
        concurrent_session_result = self._test_concurrent_sessions()
        test_results.append(concurrent_session_result)

        # Test 4: Session Data Integrity
        session_integrity_result = self._test_session_data_integrity()
        test_results.append(session_integrity_result)

        # Store results
        self.security_results['session_management_tests'] = test_results

        # Verify all tests passed security requirements
        for result in test_results:
            self.assertTrue(result['security_passed'],
                          f"Session management test failed: {result['test_name']} - {result.get('failure_reason', '')}")

    def _test_session_timeout(self):
        """Test session timeout functionality."""
        test_name = "Session Timeout Validation"

        username = self.test_user['username']

        # Create session
        session_created = self.session_manager.save_session(username)
        self.assertTrue(session_created, "Failed to create test session")

        # Verify session is valid initially
        loaded_user = self.session_manager.load_session()
        initial_session_valid = loaded_user == username

        # Simulate session timeout by modifying session file
        session_file = self.session_manager.SESSION_FILE
        if os.path.exists(session_file):
            with open(session_file, 'r') as f:
                session_data = json.load(f)

            # Set session to be expired (older than timeout period)
            expired_time = time.time() - (25 * 60 * 60)  # 25 hours ago
            session_data['created_at'] = expired_time
            session_data['last_accessed'] = expired_time

            with open(session_file, 'w') as f:
                json.dump(session_data, f)

            # Try to load expired session
            expired_user = self.session_manager.load_session()
            session_timeout_working = expired_user is None
        else:
            session_timeout_working = False

        security_passed = initial_session_valid and session_timeout_working

        return {
            'test_name': test_name,
            'initial_session_valid': initial_session_valid,
            'session_timeout_working': session_timeout_working,
            'security_passed': security_passed,
            'failure_reason': 'Session timeout mechanism failed' if not security_passed else None
        }

    def _test_session_fixation(self):
        """Test session fixation prevention."""
        test_name = "Session Fixation Prevention"

        username = self.test_user['username']

        # Create initial session
        initial_session = self.session_manager.save_session(username)

        # Get initial session verifier
        session_file = self.session_manager.SESSION_FILE
        initial_verifier = None
        if os.path.exists(session_file):
            with open(session_file, 'r') as f:
                session_data = json.load(f)
            initial_verifier = session_data.get('session_verifier')

        # Simulate login (should regenerate session)
        time.sleep(0.1)  # Small delay to ensure different timestamp
        new_session = self.session_manager.save_session(username)

        # Get new session verifier
        new_verifier = None
        if os.path.exists(session_file):
            with open(session_file, 'r') as f:
                session_data = json.load(f)
            new_verifier = session_data.get('session_verifier')

        # Security assertion: Session verifier should change after login
        session_regenerated = initial_verifier != new_verifier

        return {
            'test_name': test_name,
            'initial_session_created': initial_session,
            'new_session_created': new_session,
            'session_regenerated': session_regenerated,
            'security_passed': session_regenerated,
            'failure_reason': 'Session fixation vulnerability detected' if not session_regenerated else None
        }

    def _test_concurrent_sessions(self):
        """Test concurrent session handling."""
        test_name = "Concurrent Session Handling"

        username = self.test_user['username']

        # Create multiple sessions concurrently
        def create_session(session_id):
            return self.session_manager.save_session(f"{username}_{session_id}")

        # Test concurrent session creation
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(create_session, i) for i in range(5)]
            results = [future.result() for future in futures]

        successful_sessions = sum(1 for result in results if result)

        # Security assertion: System should handle concurrent sessions gracefully
        concurrent_handling_ok = successful_sessions >= 3  # At least 3 out of 5 should succeed

        return {
            'test_name': test_name,
            'concurrent_sessions_attempted': 5,
            'successful_sessions': successful_sessions,
            'concurrent_handling_ok': concurrent_handling_ok,
            'security_passed': concurrent_handling_ok,
            'failure_reason': 'Concurrent session handling failed' if not concurrent_handling_ok else None
        }

    def _test_session_data_integrity(self):
        """Test session data integrity and encryption."""
        test_name = "Session Data Integrity"

        username = self.test_user['username']

        # Create session
        session_created = self.session_manager.save_session(username)

        # Check session file exists and has proper structure
        session_file = self.session_manager.SESSION_FILE
        session_structure_valid = False
        session_verifier_present = False

        if os.path.exists(session_file):
            try:
                with open(session_file, 'r') as f:
                    session_data = json.load(f)

                # Check required fields
                required_fields = ['username', 'session_verifier', 'created_at', 'last_accessed']
                session_structure_valid = all(field in session_data for field in required_fields)

                # Check if session verifier is present and not empty
                verifier = session_data.get('session_verifier', '')
                session_verifier_present = bool(verifier and len(verifier) > 10)

            except (json.JSONDecodeError, KeyError):
                session_structure_valid = False

        security_passed = session_created and session_structure_valid and session_verifier_present

        return {
            'test_name': test_name,
            'session_created': session_created,
            'session_structure_valid': session_structure_valid,
            'session_verifier_present': session_verifier_present,
            'security_passed': security_passed,
            'failure_reason': 'Session data integrity compromised' if not security_passed else None
        }

if __name__ == '__main__':
    unittest.main()
