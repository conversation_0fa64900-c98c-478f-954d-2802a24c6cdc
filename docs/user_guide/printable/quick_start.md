# EcoCycle Quick Start Guide

## Getting Started with EcoCycle

This printable guide provides essential information to help you start using EcoCycle quickly and effectively.

---

## Installation

```bash
pip install ecocycle
```

## First Login

1. Launch EcoCycle by running `ecocycle` in your terminal
2. Create a new account or sign in with Google
3. Complete your user profile with cycling preferences

---

## Key Features

### Route Planning
- **Create Route**: Dashboard > Routes > New Route
- **Save Favorite**: Click star icon on any route
- **View Elevation**: Toggle elevation chart on route map

### Activity Tracking
- **Start Tracking**: Dashboard > Activities > Start New
- **Pause/Resume**: Press Spacebar during tracking
- **Save Activity**: Complete tracking and add notes

### Environmental Impact
- **View Impact**: Dashboard > Impact tab
- **Set Goals**: Dashboard > Impact > Set New Goal
- **Share Results**: Impact screen > Share button

---

## Keyboard Shortcuts

| Key       | Function                  |
|-----------|---------------------------|
| F1        | Open Help                 |
| Ctrl+N    | New Route                 |
| Ctrl+S    | Save Current              |
| Spacebar  | Start/Pause Tracking      |
| Esc       | Cancel Current Operation  |
| Tab       | Navigate Between Fields   |

---

## Common Tasks

### Importing External Data
1. Go to Settings > Import
2. Select data source (Strava, GPX file, etc.)
3. Follow the import wizard

### Syncing Devices
1. Log in to the same account on all devices
2. Go to Settings > Sync
3. Enable automatic syncing
4. For manual sync, use "Sync Now" button

---

## Troubleshooting

### Application Won't Start
- Verify Python 3.8+ is installed
- Check internet connection
- Try reinstalling with `pip install --force-reinstall ecocycle`

### Sync Issues
- Ensure all devices are on the same account
- Check internet connection on all devices
- Go to Settings > Sync > Reset Sync Data

### GPS Problems
- Verify location services are enabled
- Go outdoors for better signal
- Try Settings > GPS > Recalibrate

---

## Getting Help

- In-app help: Press F1 from any screen
- Documentation: [docs.ecocycle.org](https://docs.ecocycle.org)
- Support forum: [community.ecocycle.org](https://community.ecocycle.org)
- Email support: <EMAIL>

---

*Print this page for easy reference. For complete documentation, visit our website or access the help system within the app.*

**Version: 2.5.0 | May 2025**
