# Getting Started with EcoCycle

## Introduction
EcoCycle is a comprehensive application designed to help you track your cycling activity, plan eco-friendly routes, and contribute to environmental sustainability. This guide will help you get up and running with EcoCycle quickly.

## System Requirements
- Python 3.8 or higher
- 4GB RAM (minimum)
- 500MB free disk space
- Internet connection for weather data and sync features

## Installation
EcoCycle features an advanced dependency management system that automatically installs required packages when needed. You only need to install the core application:

```bash
pip install ecocycle
# or from source
git clone https://github.com/ecocycle/ecocycle.git
cd ecocycle
python setup.py install
```

## First Launch
When you first launch EcoCycle, you'll be guided through:
1. Creating your user account or signing in with Google
2. Setting up your profile and preferences
3. A quick tour of the main features

## Authentication Options
EcoCycle offers multiple authentication methods:
- Username/password with secure Argon2 hashing
- Google OAuth integration
- Guest login for trying out basic features

## Quick Tips
- Use the dashboard to see your environmental impact at a glance
- Add regular routes to quickly log recurring trips
- Enable location services for automatic trip tracking
- Check the weather forecast before planning longer rides

## Next Steps
- Explore the [Interactive Tutorials](../tutorials/interactive.md) to learn about specific features
- Join the [Community Forum](https://ecocycle.org/forum) to connect with other users
- Check out the [FAQ](../faq/index.md) for answers to common questions
