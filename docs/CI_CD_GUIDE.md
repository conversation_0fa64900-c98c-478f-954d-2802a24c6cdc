# EcoCycle CI/CD Guide

## Overview

This document explains the CI/CD pipeline setup for EcoCycle and how to work with it effectively.

## CI/CD Pipeline Structure

### 1. Main CI Pipeline (`.github/workflows/ci.yml`)

**Triggers:** Push to `main`/`develop`, Pull Requests
**Duration:** ~5-10 minutes
**Purpose:** Fast feedback for development

**Jobs:**
- **Lint Job** (15 min timeout)
  - Syntax checking with flake8
  - Code formatting with black (main files only)
  - Import sorting with isort (main files only)
  - Type checking with mypy (main files only)
  - Pylint analysis (main files only)

- **Test Job** (20 min timeout)
  - Python 3.9, 3.10, 3.11 matrix
  - Quick CI tests
  - Basic functionality tests
  - Core dependency installation only

- **Security Job** (10 min timeout)
  - Bandit security scanning (main files only)
  - Safety dependency checking

- **Build Job**
  - Package building and validation
  - Artifact upload

### 2. Comprehensive Tests (`.github/workflows/comprehensive-tests.yml`)

**Triggers:** Daily schedule, manual dispatch, test file changes
**Duration:** ~30-60 minutes
**Purpose:** Thorough testing without blocking development

**Features:**
- Full test suite execution
- Performance testing
- Security testing
- Multiple Python versions

## Local Development

### Quick Local CI Check

```bash
# Run quick local CI simulation
python scripts/run_local_ci.py

# Run quick CI tests only
python scripts/quick_ci_test.py
```

### Manual Testing Commands

```bash
# Syntax check
flake8 main.py cli.py setup.py --count --select=E9,F63,F7,F82

# Security scan
bandit -r main.py cli.py setup.py

# Basic tests
python main.py --help
python cli.py --help
```

## Configuration Files

### `.flake8`
- Excludes large directories for performance
- Focuses on critical syntax errors
- Ignores common false positives

### `.bandit`
- Excludes test and cache directories
- Skips low-priority security warnings
- Focuses on medium+ severity issues

### `pytest.ini`
- Configures test discovery
- Sets timeouts and failure limits
- Defines test markers

### Requirements Files
- `requirements.txt` - Production dependencies
- `requirements-dev.txt` - Development tools
- `requirements-minimal.txt` - CI-only dependencies

## Performance Optimizations

### 1. Selective File Scanning
- Only scan main files for linting/security
- Exclude cache, backup, and test directories
- Use timeouts to prevent hanging

### 2. Dependency Management
- Minimal dependencies in CI
- Cached pip installations
- Separate dev/prod requirements

### 3. Test Optimization
- Quick tests in main pipeline
- Comprehensive tests in separate workflow
- Parallel test execution where possible

## Troubleshooting

### Common Issues

1. **Flake8 Timeout**
   - Check `.flake8` exclusions
   - Reduce scope to main files only
   - Increase timeout if needed

2. **Bandit Hanging**
   - Verify `.bandit` configuration
   - Exclude large directories
   - Use JSON output for faster processing

3. **Test Failures**
   - Check dependency compatibility
   - Verify Python version support
   - Review test timeouts

4. **Import Errors**
   - Ensure minimal dependencies are installed
   - Check for circular imports
   - Verify module structure

### Debug Commands

```bash
# Check syntax manually
python -m py_compile main.py

# Test imports
python -c "import main; print('OK')"

# Run specific test
pytest Tests/test_basic_functionality.py -v

# Check dependencies
pip list | grep -E "(rich|colorama|python-dotenv)"
```

## Best Practices

### For Developers

1. **Before Pushing:**
   ```bash
   python scripts/run_local_ci.py
   ```

2. **For Large Changes:**
   - Run comprehensive tests locally
   - Check multiple Python versions
   - Test with minimal dependencies

3. **When Adding Dependencies:**
   - Update appropriate requirements file
   - Test CI compatibility
   - Consider CI performance impact

### For CI Maintenance

1. **Monitor Performance:**
   - Check job durations regularly
   - Optimize slow steps
   - Update timeouts as needed

2. **Dependency Updates:**
   - Test compatibility before updating
   - Update all requirements files
   - Verify CI still passes

3. **Configuration Updates:**
   - Test changes locally first
   - Update documentation
   - Monitor for regressions

## Future Improvements

1. **Caching Enhancements:**
   - Cache more build artifacts
   - Implement dependency caching
   - Cache test results

2. **Parallel Execution:**
   - Split tests into smaller groups
   - Run linting in parallel
   - Optimize job dependencies

3. **Quality Gates:**
   - Add coverage thresholds
   - Implement quality metrics
   - Add performance benchmarks
