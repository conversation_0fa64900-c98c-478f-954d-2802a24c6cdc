# EcoCycle Development Guidelines

This document provides guidelines and information for developers working on the EcoCycle project.

## Build/Configuration Instructions

### Setting Up the Development Environment

1. **Python Version**: EcoCycle requires Python 3.11 or higher.

2. **Dependency Management**:
   - The project uses Poetry for dependency management. Install Poetry following the instructions at [python-poetry.org](https://python-poetry.org/docs/#installation).
   - Alternatively, you can use pip with the requirements.txt file.

3. **Installing Dependencies**:
   ```bash
   # Using Poetry
   poetry install

   # Using pip
   pip install -r requirements.txt
   ```

4. **Environment Variables**:
   - Copy the template.env file to .env and fill in the required values:
   ```bash
   cp template.env .env
   ```

5. **Debug Mode**:
   - Set the DEBUG environment variable to enable debug mode:
   ```bash
   export DEBUG=true
   ```

## Testing Information

### Running Tests

1. **Running All Tests**:
   ```bash
   # Using Python's unittest module
   python -m unittest discover Tests

   # Using pytest
   pytest
   ```

2. **Running Specific Tests**:
   ```bash
   # Running a specific test file
   python -m Tests.test_eco_tips_updated

   # Running a specific test class
   python -m unittest Tests.test_eco_tips_updated.TestEcoTips

   # Running a specific test method
   python -m unittest Tests.test_eco_tips_updated.TestEcoTips.test_get_random_tip
   ```

3. **Test Coverage**:
   ```bash
   # Generate coverage report
   pytest --cov=. Tests/
   ```

### Adding New Tests

1. **Test File Structure**:
   - Create test files in the Tests directory
   - Name test files with the prefix `test_` (e.g., `test_module_name.py`)
   - Test classes should inherit from `unittest.TestCase`
   - Test methods should start with `test_`

2. **Test Example**:
   ```python
   import unittest
   import sys
   import os

   # Add parent directory to path to import modules
   sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

   import module_to_test

   class TestModuleName(unittest.TestCase):
       def test_function_name(self):
           # Arrange
           expected_result = "expected value"
           
           # Act
           actual_result = module_to_test.function_name()
           
           # Assert
           self.assertEqual(expected_result, actual_result)

   if __name__ == '__main__':
       unittest.main()
   ```

3. **Mocking External Dependencies**:
   - Use `unittest.mock` to mock external dependencies
   - Example:
   ```python
   from unittest import mock

   @mock.patch('module_to_test.requests')
   def test_api_call(self, mock_requests):
       mock_response = mock.MagicMock()
       mock_response.status_code = 200
       mock_response.json.return_value = {"key": "value"}
       mock_requests.get.return_value = mock_response
       
       result = module_to_test.api_call()
       
       self.assertEqual(result, {"key": "value"})
   ```

## Additional Development Information

### Code Style and Linting

1. **Code Formatting**:
   - The project uses Black for code formatting
   - Run Black before committing changes:
   ```bash
   black .
   ```

2. **Linting**:
   - Use Flake8 for linting:
   ```bash
   flake8 .
   ```

3. **Type Checking**:
   - Use MyPy for type checking:
   ```bash
   mypy .
   ```

4. **Ruff Configuration**:
   - The project uses Ruff for additional linting with the following rules:
     - E, W, F, I, B, C4, ARG, SIM
   - Ignored rules: W291, W292, W293

### Project Structure

1. **Main Components**:
   - `main.py`: Entry point for the application
   - `cli.py`: Command-line interface
   - `user_manager.py`: User management functionality
   - `eco_challenges.py`: Eco challenges functionality
   - `eco_tips.py`: Eco tips functionality
   - `weather_route_planner.py`: Weather and route planning functionality
   - `data_visualization.py`: Data visualization functionality

2. **Configuration**:
   - `config.py`: Contains configuration constants and settings
   - Environment variables can be set in the .env file

3. **Logging**:
   - Logs are stored in the Logs directory
   - Debug logs are stored in Logs/ecocycle_debug.log
   - Set DEBUG=true to enable debug logging

### Debugging

1. **Debug Mode**:
   - Set the DEBUG environment variable to enable debug mode:
   ```bash
   export DEBUG=true
   ```

2. **Logs**:
   - Check the log files in the Logs directory for debugging information
   - Debug logs contain detailed information about function calls and parameters

### Documentation

1. **Docstrings**:
   - Use Google-style docstrings for functions and classes
   - Example:
   ```python
   def function_name(param1, param2):
       """
       Function description.
       
       Args:
           param1 (type): Description of param1
           param2 (type): Description of param2
           
       Returns:
           type: Description of return value
       """
   ```