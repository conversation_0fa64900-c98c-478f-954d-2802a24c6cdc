#!/usr/bin/env python3
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_system_repair():
    try:
        from utils.system_repair import SystemRepair
        from utils.ai_system_diagnostics import AISystemDiagnostics
        
        print("✅ System Repair module imported successfully")
        
        # Test SystemRepair
        repair = SystemRepair()
        if hasattr(repair, 'run_ai_enhanced_diagnostics'):
            print("✅ AI-enhanced diagnostics available")
        else:
            print("❌ AI-enhanced diagnostics not available")
            
        # Test AI Diagnostics
        ai_diag = AISystemDiagnostics()
        if ai_diag.is_ai_available():
            print("✅ AI diagnostics fully functional")
        else:
            print("⚠️ AI diagnostics not available (expected if no Gemini API key)")
            
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Quick System Repair Test")
    print("=" * 40)
    success = test_system_repair()
    print("=" * 40)
    if success:
        print("✅ System Repair should be available in Developer Mode!")
    else:
        print("❌ System Repair has issues. Check the implementation.")
