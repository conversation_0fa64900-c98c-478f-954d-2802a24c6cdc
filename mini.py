#!/usr/bin/env python3
"""
EcoCycle - Minimal Entry Point
Minimal entry point that uses the new package structure.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """Minimal entry point."""
    try:
        # Use new package structure
        from ecocycle import main as ecocycle_main
        return ecocycle_main()
    except ImportError as e:
        print(f"Error importing EcoCycle: {e}")
        return 1
    except Exception as e:
        print(f"Error running EcoCycle: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
