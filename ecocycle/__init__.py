"""
EcoCycle - Cycle Into A Greener Tomorrow
A comprehensive cycling tracking and environmental impact application.
"""

__version__ = "3.4.0"
__author__ = "EcoCycle Team"
__email__ = "<EMAIL>"

# Import main components for easy access
from . import core
from . import apps
from . import auth
from . import services
from . import utils
from . import config
from . import models

# Optional imports that may have missing dependencies
try:
    from . import web
except ImportError:
    web = None

# Main entry point function
def main():
    """Main entry point for EcoCycle."""
    from .cli.main import main as cli_main
    return cli_main()

__all__ = [
    'core', 'apps', 'auth', 'services', 'utils',
    'config', 'models', 'main'
]

# Add web to __all__ if it was imported successfully
if web is not None:
    __all__.append('web')
