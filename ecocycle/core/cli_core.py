"""
EcoCycle - CLI Core Module
Shared functionality for both main.py and cli.py to eliminate code duplication.
"""
import os
import logging
import json
import datetime
from typing import Dict, Optional, Tuple, Any
import ecocycle.core.database.manager as database_manager
from ecocycle.utils.app_functions import import_local_modules


class CLICore:
    """Core functionality shared between main.py and cli.py."""

    def __init__(self, logger_name: str = __name__):
        """Initialize CLI core with logger."""
        self.logger = logging.getLogger(logger_name)
        self.user_manager = None
        self.modules = None

    def initialize_managers(self) -> Tuple[Any, Any]:
        """Initialize user manager and sheets manager instances."""
        self.modules = import_local_modules()
        self.user_manager = self.modules['user_manager'].UserManager()

        # Create sheets manager if available
        sheets_manager_instance = None
        google_sheets_available = False  # This should come from config

        if google_sheets_available:
            # Check if sheets_manager module was actually loaded
            if self.modules.get('sheets_manager'):
                try:
                    sheets_manager_instance = self.modules['sheets_manager'].SheetsManager()
                    # Attach sheets manager to user manager if available and initialized correctly
                    if sheets_manager_instance.is_available():
                        self.user_manager.sheets_manager = sheets_manager_instance
                except Exception as sheet_init_error:
                    self.logger.error(f"Failed to initialize SheetsManager: {sheet_init_error}", exc_info=True)
                    # Ensure it's None if initialization failed
                    sheets_manager_instance = None
            else:
                self.logger.warning("Sheets manager module not imported or available.")

        return self.user_manager, sheets_manager_instance

    def ensure_authentication(self, action_description: str = "perform this action") -> bool:
        """
        Ensure user is authenticated, prompt for authentication if needed.

        Args:
            action_description: Description of the action requiring authentication

        Returns:
            True if authenticated, False otherwise
        """
        if not self.user_manager:
            self.initialize_managers()

        if self.user_manager and not self.user_manager.is_authenticated():
            print(f"Authentication required to {action_description}.")
            if not self.user_manager.authenticate():
                print("Authentication failed.")
                return False
        return self.user_manager is not None

    def log_trip_to_database(self, username: str, date: str, distance: float,
                           duration: float, co2_saved: float, calories: int) -> bool:
        """
        Log a trip to the database.

        Returns:
            True if successful, False otherwise
        """
        conn = database_manager.create_connection()
        if not conn:
            print("Warning: Could not connect to database. Trip saved to memory only.")
            return False

        try:
            # Get user ID from username
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM users WHERE username = ?", (username,))
            user_row = cursor.fetchone()

            if user_row:
                user_id = user_row[0]
                # Add trip with user_id instead of username
                trip_id = database_manager.add_trip(conn, (user_id, date, distance, duration, co2_saved, calories))
                if trip_id:
                    print("Trip logged to database successfully!")
                    print(f"Trip saved with ID: {trip_id}")
                    return True
                else:
                    print("Warning: Failed to save trip to database.")
                    return False
            else:
                print(f"Warning: User '{username}' not found in database. Trip saved to memory only.")
                return False
        except Exception as e:
            print(f"Warning: Could not log trip to database: {e}")
            return False
        finally:
            conn.close()

    def calculate_trip_stats(self, distance: float, duration: float, weight: float) -> Dict[str, Any]:
        """
        Calculate trip statistics.

        Returns:
            Dictionary containing calculated stats
        """
        import utils.general_utils as utils

        speed = utils.calculate_average_speed(distance, duration)
        calories = utils.calculate_calories(distance, speed, int(weight))
        co2_saved = utils.calculate_co2_saved(distance)

        return {
            'speed': speed,
            'calories': calories,
            'co2_saved': co2_saved
        }

    def display_trip_summary(self, date: str, distance: float, duration: float,
                           speed: float, calories: int, co2_saved: float):
        """Display a formatted trip summary."""
        import utils.general_utils as utils

        print("\nTrip Summary:")
        print(f"Date: {date}")
        print(f"Distance: {utils.format_distance(distance)}")
        print(f"Duration: {duration:.1f} minutes")
        print(f"Average Speed: {speed:.1f} km/h")
        print(f"Calories Burned: {utils.format_calories(calories)}")
        print(f"CO2 Saved: {utils.format_co2(co2_saved)}")

    def get_user_weight(self, prompt_if_missing: bool = True) -> Optional[float]:
        """
        Get user weight from preferences or prompt for it.

        Args:
            prompt_if_missing: Whether to prompt user if weight is not set

        Returns:
            User weight in kg, or None if not available and not prompted
        """
        if not self.user_manager:
            return None

        weight = self.user_manager.get_user_preference('weight_kg', None)
        if weight is None and prompt_if_missing:
            try:
                weight = float(input("Your weight (kg) for calorie calculation: "))
                if self.user_manager:
                    self.user_manager.update_user_preference('weight_kg', weight)
            except ValueError:
                print("Invalid weight entered.")
                return None
        return weight

    def handle_trip_logging(self, date: Optional[str] = None, distance: Optional[float] = None,
                          duration: Optional[float] = None, auto_save: bool = False) -> bool:
        """
        Handle the complete trip logging process.

        Returns:
            True if trip was logged successfully, False otherwise
        """
        if not self.ensure_authentication("log a cycling trip"):
            return False

        # Get trip details
        if not date:
            date = datetime.datetime.now().strftime("%Y-%m-%d")

        if distance is None:
            try:
                distance = float(input("Distance (km): "))
            except ValueError:
                print("Invalid distance entered.")
                return False

        if duration is None:
            try:
                duration = float(input("Duration (minutes): "))
            except ValueError:
                print("Invalid duration entered.")
                return False

        # Get user weight
        weight = self.get_user_weight()
        if weight is None:
            print("Weight is required for calorie calculation.")
            return False

        # Calculate stats
        stats = self.calculate_trip_stats(distance, duration, weight)

        # Display summary
        self.display_trip_summary(date, distance, duration, stats['speed'],
                                stats['calories'], stats['co2_saved'])

        # Confirm and save
        if not auto_save:
            confirm = input("\nSave this trip? (y/n): ")
            save = confirm.lower() == 'y'
        else:
            save = True
            print("\nAutomatically saving trip...")

        if save:
            # Update user stats (this includes adding the trip to in-memory data)
            if self.user_manager and self.user_manager.update_user_stats(distance, stats['co2_saved'],
                                                 stats['calories'], duration):
                print("Trip saved to memory successfully!")

                # Log to database
                if self.user_manager:
                    username = self.user_manager.get_current_user().get('username')
                    self.log_trip_to_database(username, date, distance, duration,
                                            stats['co2_saved'], stats['calories'])
                return True
            else:
                print("Error saving trip data.")
                return False
        else:
            print("Trip not saved.")
            return False


class ConfigManager:
    """Manages configuration for CLI operations."""

    def __init__(self, config_file: str = ".ecocycle.config"):
        """Initialize config manager with config file path."""
        self.config_file = config_file
        self.logger = logging.getLogger(__name__)

    def load_config(self) -> Dict:
        """Load configuration from file."""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    return json.load(f)
            except json.JSONDecodeError:
                self.logger.error(f"Error parsing config file: {self.config_file}")
                return {}
        return {}

    def save_config(self, config: Dict) -> bool:
        """Save configuration to file."""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            return True
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")
            return False

    def get_config_value(self, key: str, default: Any = None) -> Any:
        """Get a configuration value."""
        config = self.load_config()
        return config.get(key, default)

    def set_config_value(self, key: str, value: Any) -> bool:
        """Set a configuration value."""
        config = self.load_config()
        config[key] = value
        return self.save_config(config)
