"""
EcoCycle - Enhanced Doctor UI Module
Provides comprehensive system diagnostics with Rich UI components.
"""
import os
import sys
import json
import time
import platform
import sqlite3
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

# Import Rich components with fallbacks
try:
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
    from rich.prompt import Prompt, Confirm
    from rich.text import Text
    from rich.layout import Layout
    from rich.align import Align
    from rich.rule import Rule
    from rich.tree import Tree
    from rich.columns import Columns
    from rich import box
    HAS_RICH = True
    console = Console()
except ImportError:
    HAS_RICH = False
    console = None

# Import core modules
try:
    import core.dependency.dependency_manager as dependency_manager
    HAS_DEPENDENCY_MANAGER = True
except ImportError:
    HAS_DEPENDENCY_MANAGER = False

try:
    from health_check import HealthCheckHandler
    HAS_HEALTH_CHECK = True
except ImportError:
    HAS_HEALTH_CHECK = False

# Import configuration
try:
    from config.config_loader import load_config, save_config
    HAS_CONFIG = True
except ImportError:
    HAS_CONFIG = False

logger = logging.getLogger(__name__)


class EnhancedDoctorUI:
    """Enhanced Doctor UI with Rich components and comprehensive diagnostics."""

    def __init__(self):
        """Initialize the Enhanced Doctor UI."""
        self.console = console if HAS_RICH else None
        self.issues_found = []
        self.repairs_applied = []
        self.start_time = time.time()

    def run_comprehensive_diagnostics(self, fix_issues=False):
        """Run comprehensive system diagnostics with enhanced UI.

        Args:
            fix_issues (bool): Whether to attempt automatic fixes for detected issues
        """
        self.fix_issues = fix_issues
        if HAS_RICH and self.console:
            self._run_rich_diagnostics()
        else:
            self._run_basic_diagnostics()

    def _run_rich_diagnostics(self):
        """Run diagnostics with Rich UI components."""
        # Clear screen and show header
        self.console.clear()
        self._display_header()

        # Create main layout
        layout = Layout()
        layout.split_column(
            Layout(name="header", size=5),
            Layout(name="main", ratio=1),
            Layout(name="footer", size=3)
        )

        # Show header
        header_panel = Panel(
            Align.center(Text("EcoCycle System Doctor", style="bold cyan")),
            subtitle="Comprehensive System Health Check",
            border_style="cyan",
            box=box.DOUBLE
        )
        layout["header"].update(header_panel)
        self.console.print(layout["header"])

        # Run diagnostic steps with progress tracking
        diagnostic_steps = [
            ("System Information", self._check_system_info),
            ("Python Environment", self._check_python_environment),
            ("Dependencies", self._check_dependencies),
            ("Database Health", self._check_database_health),
            ("Configuration", self._check_configuration),
            ("File System", self._check_file_system),
            ("Performance", self._check_performance),
            ("Security", self._check_security)
        ]

        with Progress(
            SpinnerColumn(),
            TextColumn("[bold blue]{task.description}"),
            BarColumn(bar_width=40),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=self.console
        ) as progress:

            main_task = progress.add_task("Running diagnostics...", total=len(diagnostic_steps))

            results = {}
            for step_name, step_func in diagnostic_steps:
                progress.update(main_task, description=f"Checking {step_name.lower()}...")
                results[step_name] = step_func()
                progress.advance(main_task)
                time.sleep(0.5)  # Brief pause for visual effect

        # Display results
        self._display_results(results)

        # Show summary and options
        self._display_summary()

    def _display_header(self):
        """Display the diagnostic header."""
        if HAS_RICH and self.console:
            self.console.print(Rule("EcoCycle System Doctor", style="cyan"))
            self.console.print()

    def _check_system_info(self) -> Dict[str, Any]:
        """Check basic system information."""
        try:
            info = {
                "platform": platform.system(),
                "platform_version": platform.version(),
                "architecture": platform.architecture()[0],
                "python_version": platform.python_version(),
                "python_implementation": platform.python_implementation(),
                "hostname": platform.node(),
                "status": "healthy"
            }

            # Try to get processor info, but don't hang if it fails
            try:
                info["processor"] = platform.processor() or "Unknown"
            except Exception:
                info["processor"] = "Unknown"

            # Check for potential issues
            if sys.version_info < (3, 7):
                info["status"] = "warning"
                info["issues"] = ["Python version is below recommended 3.7+"]

            return info
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def _check_python_environment(self) -> Dict[str, Any]:
        """Check Python environment health."""
        try:
            env_info = {
                "python_executable": sys.executable,
                "python_path": sys.path[:3],  # First 3 entries
                "virtual_env": os.environ.get("VIRTUAL_ENV"),
                "pip_available": False,
                "status": "healthy"
            }

            # Check pip availability
            try:
                import subprocess
                result = subprocess.run([sys.executable, "-m", "pip", "--version"],
                                      capture_output=True, text=True, timeout=10)
                env_info["pip_available"] = result.returncode == 0
                if result.returncode == 0:
                    env_info["pip_version"] = result.stdout.strip()
            except Exception:
                env_info["pip_available"] = False

            if not env_info["pip_available"]:
                env_info["status"] = "warning"
                env_info["issues"] = ["pip is not available"]

            return env_info
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def _check_dependencies(self) -> Dict[str, Any]:
        """Check application dependencies."""
        if not HAS_DEPENDENCY_MANAGER:
            return {"status": "warning", "error": "Dependency manager not available"}

        try:
            results = dependency_manager.check_all_dependencies()

            dep_info = {
                "total_features": len(results),
                "available_features": 0,
                "missing_packages": [],
                "feature_status": {},
                "status": "healthy"
            }

            for feature, status in results.items():
                dep_info["feature_status"][feature] = status
                if status["available"]:
                    dep_info["available_features"] += 1
                else:
                    dep_info["missing_packages"].extend(status.get("missing", []))

            # Determine overall status
            if dep_info["available_features"] == 0:
                dep_info["status"] = "critical"
            elif dep_info["missing_packages"]:
                dep_info["status"] = "warning"

            return dep_info
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def _check_database_health(self) -> Dict[str, Any]:
        """Check database health and integrity."""
        try:
            db_info = {
                "database_exists": False,
                "connection_ok": False,
                "tables_count": 0,
                "integrity_ok": False,
                "size_mb": 0,
                "status": "healthy"
            }

            # Check if database file exists
            db_path = "data/ecocycle.db"
            if os.path.exists(db_path):
                db_info["database_exists"] = True
                db_info["size_mb"] = round(os.path.getsize(db_path) / (1024 * 1024), 2)

                # Test connection
                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()

                    # Check tables
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    tables = cursor.fetchall()
                    db_info["tables_count"] = len(tables)
                    db_info["connection_ok"] = True

                    # Basic integrity check
                    cursor.execute("PRAGMA integrity_check;")
                    integrity_result = cursor.fetchone()
                    db_info["integrity_ok"] = integrity_result[0] == "ok"

                    conn.close()
                except Exception as e:
                    db_info["status"] = "error"
                    db_info["error"] = str(e)
            else:
                db_info["status"] = "warning"
                db_info["issues"] = ["Database file does not exist"]

            return db_info
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def _check_configuration(self) -> Dict[str, Any]:
        """Check application configuration."""
        if not HAS_CONFIG:
            return {"status": "warning", "error": "Configuration loader not available"}

        try:
            config_info = {
                "config_exists": False,
                "config_valid": False,
                "sections_count": 0,
                "missing_sections": [],
                "status": "healthy"
            }

            config = load_config()
            if config:
                config_info["config_exists"] = True
                config_info["config_valid"] = True
                config_info["sections_count"] = len(config)

                # Check for required sections
                required_sections = ["user_preferences", "api_keys", "database"]
                for section in required_sections:
                    if section not in config:
                        config_info["missing_sections"].append(section)

                if config_info["missing_sections"]:
                    config_info["status"] = "warning"
            else:
                config_info["status"] = "warning"
                config_info["issues"] = ["No configuration found"]

            return config_info
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def _check_file_system(self) -> Dict[str, Any]:
        """Check file system health."""
        try:
            fs_info = {
                "data_dirs_exist": True,
                "log_files_exist": True,
                "permissions_ok": True,
                "disk_space_ok": True,
                "status": "healthy"
            }

            # Check required directories
            required_dirs = ["data", "config", "logs", "cache"]
            missing_dirs = []
            for dir_name in required_dirs:
                if not os.path.exists(dir_name):
                    missing_dirs.append(dir_name)

            if missing_dirs:
                fs_info["data_dirs_exist"] = False
                fs_info["missing_dirs"] = missing_dirs
                fs_info["status"] = "warning"

            # Check disk space (basic check)
            try:
                import shutil
                total, used, free = shutil.disk_usage(".")
                free_gb = free / (1024**3)
                if free_gb < 1:  # Less than 1GB free
                    fs_info["disk_space_ok"] = False
                    fs_info["status"] = "warning"
                fs_info["free_space_gb"] = round(free_gb, 2)
            except Exception:
                pass

            return fs_info
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def _check_performance(self) -> Dict[str, Any]:
        """Check system performance metrics."""
        try:
            perf_info = {
                "memory_usage_mb": 0,
                "cpu_count": os.cpu_count(),
                "load_average": None,
                "status": "healthy"
            }

            # Get memory usage
            try:
                import psutil
                process = psutil.Process()
                perf_info["memory_usage_mb"] = round(process.memory_info().rss / (1024 * 1024), 2)

                # Get system load if available
                if hasattr(os, 'getloadavg'):
                    perf_info["load_average"] = os.getloadavg()[0]
            except ImportError:
                perf_info["status"] = "warning"
                perf_info["issues"] = ["psutil not available for detailed performance metrics"]

            return perf_info
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def _check_security(self) -> Dict[str, Any]:
        """Check basic security configurations."""
        try:
            security_info = {
                "file_permissions_ok": True,
                "sensitive_files_protected": True,
                "env_vars_secure": True,
                "status": "healthy"
            }

            # Check for sensitive files with proper permissions
            sensitive_files = [".env", "config/api_keys.json", "data/users.json"]
            for file_path in sensitive_files:
                if os.path.exists(file_path):
                    stat_info = os.stat(file_path)
                    # Check if file is readable by others (basic check)
                    if stat_info.st_mode & 0o044:  # Others can read
                        security_info["file_permissions_ok"] = False
                        security_info["status"] = "warning"
                        break

            return security_info
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def _display_results(self, results: Dict[str, Any]):
        """Display diagnostic results in a structured format."""
        if not HAS_RICH or not self.console:
            self._display_basic_results(results)
            return

        self.console.print("\n")
        self.console.print(Rule("Diagnostic Results", style="green"))

        # Create summary table
        summary_table = Table(title="System Health Summary", box=box.ROUNDED)
        summary_table.add_column("Component", style="cyan", no_wrap=True)
        summary_table.add_column("Status", justify="center")
        summary_table.add_column("Details", style="dim")

        for component, data in results.items():
            status = data.get("status", "unknown")

            # Format status with colors
            if status == "healthy":
                status_text = "[green]✓ Healthy[/green]"
            elif status == "warning":
                status_text = "[yellow]⚠ Warning[/yellow]"
            elif status == "critical":
                status_text = "[red]✗ Critical[/red]"
            elif status == "error":
                status_text = "[red]✗ Error[/red]"
            else:
                status_text = "[dim]? Unknown[/dim]"

            # Format details
            details = []
            if "error" in data:
                details.append(f"Error: {data['error']}")
            if "issues" in data:
                details.extend(data["issues"])
            if "missing_packages" in data and data["missing_packages"]:
                details.append(f"Missing: {', '.join(data['missing_packages'][:3])}")

            details_text = "; ".join(details) if details else "No issues detected"
            if len(details_text) > 50:
                details_text = details_text[:47] + "..."

            summary_table.add_row(component, status_text, details_text)

        self.console.print(summary_table)

        # Display detailed information for critical/warning items
        self._display_detailed_issues(results)

    def _display_detailed_issues(self, results: Dict[str, Any]):
        """Display detailed information for components with issues."""
        if not HAS_RICH or not self.console:
            return

        issues_found = False
        for component, data in results.items():
            status = data.get("status", "unknown")
            if status in ["warning", "critical", "error"]:
                if not issues_found:
                    self.console.print("\n")
                    self.console.print(Rule("Detailed Issue Analysis", style="yellow"))
                    issues_found = True

                # Create detailed panel for this component
                issue_content = []

                if "error" in data:
                    issue_content.append(f"[red]Error:[/red] {data['error']}")

                if "issues" in data:
                    issue_content.append("[yellow]Issues:[/yellow]")
                    for issue in data["issues"]:
                        issue_content.append(f"  • {issue}")

                if "missing_packages" in data and data["missing_packages"]:
                    issue_content.append("[yellow]Missing Packages:[/yellow]")
                    for pkg in data["missing_packages"]:
                        issue_content.append(f"  • {pkg}")

                if "feature_status" in data:
                    issue_content.append("[yellow]Feature Status:[/yellow]")
                    for feature, status in data["feature_status"].items():
                        if not status["available"]:
                            missing = ", ".join(status.get("missing", []))
                            issue_content.append(f"  • {feature}: Missing {missing}")

                panel_content = "\n".join(issue_content) if issue_content else "No specific details available"

                panel = Panel(
                    panel_content,
                    title=f"{component} Issues",
                    border_style="yellow" if status == "warning" else "red",
                    expand=False
                )
                self.console.print(panel)

        if not issues_found:
            success_panel = Panel(
                "[green]All system checks passed successfully![/green]\n"
                "Your EcoCycle installation appears to be healthy.",
                title="System Status",
                border_style="green"
            )
            self.console.print("\n")
            self.console.print(success_panel)

    def _display_summary(self):
        """Display summary and available actions."""
        if not HAS_RICH or not self.console:
            self._display_basic_summary()
            return

        elapsed_time = time.time() - self.start_time

        self.console.print("\n")
        self.console.print(Rule("Summary & Actions", style="blue"))

        # Summary panel
        summary_text = f"Diagnostic completed in {elapsed_time:.1f} seconds\n"
        summary_text += f"Issues found: {len(self.issues_found)}\n"
        summary_text += f"Repairs applied: {len(self.repairs_applied)}"

        summary_panel = Panel(
            summary_text,
            title="Diagnostic Summary",
            border_style="blue"
        )
        self.console.print(summary_panel)

        # Available actions
        if self.issues_found:
            actions_text = "Available actions:\n"
            actions_text += "• Run with --fix flag to attempt automatic repairs\n"
            actions_text += "• Check the detailed analysis above for manual fixes\n"
            actions_text += "• Consult the documentation for specific issues"
        else:
            actions_text = "No actions required - system is healthy!"

        actions_panel = Panel(
            actions_text,
            title="Recommended Actions",
            border_style="cyan"
        )
        self.console.print(actions_panel)

        # Prompt to continue
        if HAS_RICH:
            self.console.print("\n")
            Prompt.ask("Press Enter to continue", default="")
        else:
            input("\nPress Enter to continue...")

    def _display_basic_results(self, results: Dict[str, Any]):
        """Display results in basic text format (fallback)."""
        print("\nDiagnostic Results:")
        print("=" * 50)

        for component, data in results.items():
            status = data.get("status", "unknown")
            print(f"\n{component}: {status.upper()}")

            if "error" in data:
                print(f"  Error: {data['error']}")
            if "issues" in data:
                for issue in data["issues"]:
                    print(f"  Issue: {issue}")
            if "missing_packages" in data and data["missing_packages"]:
                print(f"  Missing packages: {', '.join(data['missing_packages'])}")

    def _display_basic_summary(self):
        """Display basic summary (fallback)."""
        elapsed_time = time.time() - self.start_time
        print(f"\nDiagnostic completed in {elapsed_time:.1f} seconds")
        print(f"Issues found: {len(self.issues_found)}")
        print(f"Repairs applied: {len(self.repairs_applied)}")
        input("\nPress Enter to continue...")

    def _run_basic_diagnostics(self):
        """Run basic diagnostics without Rich UI (fallback)."""
        print("EcoCycle System Doctor - Basic Mode")
        print("=" * 40)

        # Run basic checks
        print("\nChecking system information...")
        sys_info = self._check_system_info()
        print(f"Status: {sys_info.get('status', 'unknown')}")

        print("\nChecking dependencies...")
        dep_info = self._check_dependencies()
        print(f"Status: {dep_info.get('status', 'unknown')}")

        print("\nChecking database...")
        db_info = self._check_database_health()
        print(f"Status: {db_info.get('status', 'unknown')}")

        print("\nChecking configuration...")
        config_info = self._check_configuration()
        print(f"Status: {config_info.get('status', 'unknown')}")

        print("\nDiagnostics completed.")
        input("Press Enter to continue...")
