"""
EcoCycle - Web Application Module
Handles web dashboard and API endpoints for mobile and web extensions.
"""
import os
import json
import logging
import traceback
from flask import Flask, render_template, request, jsonify, redirect, url_for, session, send_from_directory, flash
from flask_cors import CORS
from werkzeug.utils import secure_filename
import sys
import time
from datetime import datetime, timezone

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import EcoCycle modules
import config
from auth.user_management.user_manager import UserManager
import core.database_manager as database_manager
from core.dependency.dependency_manager import ensure_packages
from ecocycle.models.statistics import Statistics
from ecocycle.models.routes import RouteManager
from services.sync.sync_service import get_sync_service
from services.sheets.sheets_manager import SheetsManager

# Configure logger
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__,
    static_folder='static',
    template_folder='templates'
)
CORS(app)  # Enable CORS for API endpoints

# Configure Flask app
app.secret_key = os.environ.get('FLASK_SECRET_KEY', 'ecocycle-web-secret')
app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'uploads')
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max upload

# Create upload directory if it doesn't exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'photos'), exist_ok=True)
os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'videos'), exist_ok=True)

# Initialize components
sheets_manager = SheetsManager()
user_manager = UserManager(sheets_manager=sheets_manager)
statistics = Statistics()
route_manager = RouteManager()

# Initialize sync service
sync_service = get_sync_service(user_manager=user_manager, sheets_manager=sheets_manager)

@app.route('/')
def index():
    """Render the dashboard homepage."""
    if 'username' not in session:
        return redirect(url_for('login'))

    try:
        username = session['username']
        user_data = None

        # Try to get user data from sync service
        try:
            user_data = sync_service.get_user_data(username)
        except Exception as e:
            logger.error(f"Error getting user data from sync service: {e}")
            user_data = None

        # Fall back to user manager if sync fails
        if not user_data:
            if hasattr(user_manager, 'get_user') and callable(user_manager.get_user):
                user_data = user_manager.get_user(username)
            else:
                user_data = user_manager.users.get(username, {})

        # Ensure we have basic user structure
        if not user_data:
            user_data = {
                'username': username,
                'stats': {
                    'total_trips': 0,
                    'total_distance': 0.0,
                    'total_co2_saved': 0.0,
                    'total_calories': 0,
                    'trips': []
                },
                'preferences': {}
            }

        # Ensure stats and preferences exist
        if 'stats' not in user_data:
            user_data['stats'] = {
                'total_trips': 0,
                'total_distance': 0.0,
                'total_co2_saved': 0.0,
                'total_calories': 0,
                'trips': []
            }

        if 'preferences' not in user_data:
            user_data['preferences'] = {}

        # Update session with user data
        session['user_data'] = user_data

        # Pass current date for trip logging form
        now = datetime.now()

        return render_template('dashboard.html', username=username, user=user_data, now=now)

    except Exception as e:
        logger.error(f"Error in index route: {e}")
        flash('An error occurred while loading your data. Please try again.', 'error')
        return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Handle user login."""
    # Clear any existing session data
    session.clear()
    error = None

    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')

        if not username or not password:
            error = 'Username and password are required.'
        else:
            try:
                # Authenticate user
                if user_manager.authenticate(username, password):
                    # Set session data
                    session['username'] = username
                    session['_fresh'] = True
                    session.permanent = True

                    # Get user data
                    user_data = None
                    try:
                        user_data = sync_service.get_user_data(username)
                    except Exception as e:
                        logger.error(f"Error getting user data during login: {e}")

                    if not user_data and hasattr(user_manager, 'get_user'):
                        user_data = user_manager.get_user(username)

                    # Store basic user data in session
                    if user_data:
                        session['user_data'] = {
                            'username': username,
                            'is_admin': user_data.get('is_admin', False),
                            'email': user_data.get('email', '')
                        }

                    # Redirect to intended page or dashboard
                    next_page = request.args.get('next')
                    return redirect(next_page or url_for('index'))
                else:
                    error = 'Invalid credentials. Please try again.'

            except Exception as e:
                logger.error(f"Login error for user {username}: {e}")
                error = 'An error occurred during login. Please try again.'

    return render_template('login.html', error=error)

@app.route('/logout')
def logout():
    """Handle user logout."""
    try:
        username = session.get('username')
        if username:
            # Call user manager's logout if it exists
            if hasattr(user_manager, 'logout') and callable(user_manager.logout):
                user_manager.logout()

            # Log the logout
            logger.info(f"User {username} logged out")

            # Clear session data
            session.clear()

            # Clear any session cookies
            response = redirect(url_for('login'))
            response.delete_cookie('session')
            response.delete_cookie('remember_token')

            flash('You have been logged out successfully.', 'info')
            return response

    except Exception as e:
        logger.error(f"Error during logout: {e}")

    # Ensure session is cleared even if there was an error
    session.clear()
    return redirect(url_for('login'))

@app.route('/auth/google')
def google_auth():
    """Initiate Google OAuth authentication."""
    try:
        # Use the existing UserManager's Google OAuth implementation
        success, username, user_data = user_manager.google_auth_handler.authenticate_with_google(user_manager.users)

        if success and username and user_data:
            # Update the user manager's users dictionary
            user_manager.users[username] = user_data
            user_manager.current_user = username
            user_manager.save_users()

            # Set session
            session['username'] = username

            # Synchronize the user data across platforms
            sync_service.queue_sync_task('sync_new_user', data=user_data)

            return redirect(url_for('index'))
        else:
            return render_template('login.html', error='Google authentication failed. Please try again.')
    except Exception as e:
        logger.error(f"Error during Google authentication: {e}")
        return render_template('login.html', error='Google authentication failed. Please try again.')

@app.route('/api/stats/<username>')
def get_user_stats(username):
    """
    API endpoint to get user statistics.
    Requires authentication or API key.
    """
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    # Only allow users to access their own stats unless they're an admin
    current_user = session['username']
    user_data = user_manager.users.get(current_user, {})
    is_admin = user_data.get('is_admin', False)

    if current_user != username and not is_admin:
        return jsonify({'error': 'Access denied'}), 403

    # Check if detailed stats are requested
    detailed = request.args.get('detailed', '0') == '1'

    try:
        # Get user stats from sync service first
        synced_user = sync_service.get_user_data(username)
        if synced_user and 'stats' in synced_user:
            stats = synced_user['stats']
            # Check if stats is a JSON string that needs parsing
            if isinstance(stats, str):
                try:
                    stats = json.loads(stats)
                except json.JSONDecodeError:
                    logger.warning(f"Failed to decode stats JSON for user '{username}'")
                    stats = {}
        else:
            # Fallback to user manager
            if username in user_manager.users:
                stats = user_manager.users[username].get('stats', {})
            else:
                stats = {}

        # Ensure basic stats structure
        if not isinstance(stats, dict):
            stats = {}

        # Get basic stats
        user_stats = {
            'total_trips': stats.get('total_trips', 0),
            'total_distance': stats.get('total_distance', 0.0),
            'total_co2_saved': stats.get('total_co2_saved', 0.0),
            'total_calories': stats.get('total_calories', 0),
            'trips': stats.get('trips', [])
        }

        # Add detailed information if requested
        if detailed:
            from datetime import datetime, timedelta

            # Calculate weekly distances for the last 7 days
            today = datetime.now()
            weekly_distances = [0] * 7  # Mon-Sun

            trips = user_stats['trips']
            if trips:
                for trip in trips:
                    try:
                        trip_date = datetime.fromisoformat(trip.get('date', '').replace('Z', '+00:00'))
                        days_diff = (today - trip_date).days

                        if 0 <= days_diff < 7:
                            # Map to weekday (0=Monday, 6=Sunday)
                            weekday = trip_date.weekday()
                            weekly_distances[weekday] += trip.get('distance', 0)
                    except:
                        continue

            user_stats['weekly_distances'] = weekly_distances

            # Add environmental impact data
            total_co2_saved = user_stats.get('total_co2_saved', 0)
            total_distance = user_stats.get('total_distance', 0)

            user_stats['environmental_impact'] = {
                'co2_saved': total_co2_saved,
                'trees_equivalent': total_co2_saved / 21 if total_co2_saved > 0 else 0,  # 1 tree absorbs ~21kg CO2 per year
                'car_trips_avoided': total_distance / 10 if total_distance > 0 else 0,  # ~10km per car trip
                'gasoline_saved': total_distance * 0.08 if total_distance > 0 else 0  # ~0.08 liters per km
            }

        return jsonify(user_stats)

    except Exception as e:
        logger.error(f"Error getting user stats: {e}")
        return jsonify({'error': 'Failed to retrieve user statistics'}), 500

@app.route('/api/trips/<username>')
def get_user_trips(username):
    """
    API endpoint to get user cycling trips.
    Requires authentication or API key.
    """
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    # Only allow users to access their own trips unless they're an admin
    current_user = session['username']
    user_data = user_manager.users.get(current_user, {})
    is_admin = user_data.get('is_admin', False)

    if current_user != username and not is_admin:
        return jsonify({'error': 'Access denied'}), 403

    try:
        # FIRST SOURCE (HIGHEST PRIORITY): Get trips directly from database
        # This ensures we always get the most up-to-date trip data even if logged via CLI
        try:
            trips_list = []
            with database_manager.get_connection() as conn:
                # Get user ID
                cursor = conn.cursor()
                cursor.execute("SELECT id FROM users WHERE username = ?", (username,))
                user_row = cursor.fetchone()

                if user_row:
                    user_id = user_row[0]

                    # Get trips from database
                    db_trips = database_manager.get_user_trips(conn, user_id)

                    # Convert database trips to dictionary format
                    for trip_row in db_trips:
                        trip_dict = {
                            'id': trip_row[0],
                            'date': trip_row[2],
                            'distance': float(trip_row[3]) if trip_row[3] else 0.0,
                            'duration': float(trip_row[4]) if trip_row[4] else 0.0,
                            'co2_saved': float(trip_row[5]) if trip_row[5] else 0.0,
                            'calories': int(trip_row[6]) if trip_row[6] else 0,
                            'route_data': trip_row[7] if len(trip_row) > 7 else None,
                            'weather_data': trip_row[8] if len(trip_row) > 8 else None
                        }
                        trips_list.append(trip_dict)

            if trips_list:
                logger.info(f"Retrieved {len(trips_list)} trips from database for user '{username}'")
                return jsonify(trips_list)
        except Exception as db_error:
            logger.warning(f"Database retrieval failed for user '{username}': {db_error}")
            # Continue to other data sources if database fails

        # SECOND SOURCE: Check user manager (in-memory cache)
        user_trips = user_manager.get_user_trips(username) if hasattr(user_manager, 'get_user_trips') else []

        if user_trips:
            logger.info(f"Retrieved {len(user_trips)} trips from user manager for user '{username}'")
            return jsonify(user_trips)

        # THIRD SOURCE: Try to get trips from sync service
        synced_user = sync_service.get_user_data(username)
        if synced_user and 'stats' in synced_user and 'trips' in synced_user['stats']:
            trips_data = synced_user['stats']['trips']
            # Check if trips is a JSON string that needs parsing
            if isinstance(trips_data, str):
                try:
                    trips_data = json.loads(trips_data)
                except json.JSONDecodeError:
                    logger.warning(f"Failed to decode trips JSON for user '{username}'")
                    trips_data = []

            if trips_data:
                # Clean up any trips with invalid dates
                cleaned_trips = []
                for trip in trips_data:
                    if isinstance(trip, dict) and 'date' in trip:
                        try:
                            # Validate the date
                            trip_date = trip['date']
                            if trip_date:
                                # Try to parse the date
                                parsed_date = datetime.fromisoformat(trip_date.replace('Z', '+00:00'))
                                # If date is too old (before 2000), skip it
                                if parsed_date.year >= 2000:
                                    cleaned_trips.append(trip)
                                else:
                                    logger.warning(f"Skipping trip with old date: {trip_date}")
                            else:
                                logger.warning(f"Skipping trip with empty date")
                        except (ValueError, AttributeError) as e:
                            logger.warning(f"Skipping trip with invalid date '{trip.get('date')}': {e}")
                    else:
                        # Keep trips without date field for now
                        cleaned_trips.append(trip)

                logger.info(f"Retrieved {len(cleaned_trips)} valid trips from sync service for user '{username}' (filtered from {len(trips_data)})")
                return jsonify(cleaned_trips)

        # If no trips found anywhere, return empty array
        return jsonify([])

    except Exception as e:
        logger.error(f"Error getting user trips: {e}\n{traceback.format_exc()}")
        return jsonify([]), 500

@app.route('/api/environmental-impact/<username>')
def get_environmental_impact(username):
    """
    API endpoint to get environmental impact data for a user.
    Requires authentication.
    """
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    # Only allow users to access their own data unless they're an admin
    current_user = session['username']
    user_data = user_manager.users.get(current_user, {})
    is_admin = user_data.get('is_admin', False)

    if current_user != username and not is_admin:
        return jsonify({'error': 'Access denied'}), 403

    try:
        # Get user stats from sync service first
        synced_user = sync_service.get_user_data(username)
        if synced_user and 'stats' in synced_user:
            stats = synced_user['stats']
        else:
            # Fallback to user manager
            if username in user_manager.users:
                stats = user_manager.users[username].get('stats', {})
            else:
                stats = {}

        total_distance = stats.get('total_distance', 0)
        total_co2_saved = stats.get('total_co2_saved', 0)
        total_trips = stats.get('total_trips', 0)

        # Calculate environmental impact metrics
        trees_equivalent = total_co2_saved / 21 if total_co2_saved > 0 else 0  # 1 tree absorbs ~21kg CO2 per year
        car_trips_avoided = total_distance / 10 if total_distance > 0 else 0  # ~10km per car trip
        gasoline_saved = total_distance * 0.08 if total_distance > 0 else 0  # ~0.08 liters per km

        # Calculate monthly breakdown for the last 6 months
        from datetime import datetime, timedelta
        today = datetime.now()
        monthly_data = []

        trips = stats.get('trips', [])

        for i in range(6):
            month_start = today.replace(day=1) - timedelta(days=i*30)
            month_end = month_start + timedelta(days=30)
            month_name = month_start.strftime('%B %Y')

            # Filter trips for this month
            month_trips = []
            for trip in trips:
                try:
                    trip_date = datetime.fromisoformat(trip.get('date', '').replace('Z', '+00:00'))
                    if month_start <= trip_date <= month_end:
                        month_trips.append(trip)
                except:
                    continue

            month_distance = sum(trip.get('distance', 0) for trip in month_trips)
            month_co2 = sum(trip.get('co2_saved', 0) for trip in month_trips)

            monthly_data.append({
                'month': month_name,
                'distance': month_distance,
                'co2_saved': month_co2,
                'trips': len(month_trips)
            })

        # Reverse to get chronological order
        monthly_data.reverse()

        return jsonify({
            'total_co2_saved': total_co2_saved,
            'trees_equivalent': round(trees_equivalent, 2),
            'car_trips_avoided': round(car_trips_avoided, 1),
            'gasoline_saved': round(gasoline_saved, 2),
            'total_distance': total_distance,
            'total_trips': total_trips,
            'monthly_breakdown': monthly_data
        })
    except Exception as e:
        logger.error(f"Error getting environmental impact: {e}")
        return jsonify({'error': 'Failed to load environmental impact data'}), 500

@app.route('/api/trips/log', methods=['POST'])
def log_trip():
    """
    API endpoint to log a new cycling trip.
    Requires authentication.
    """
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    username = session['username']
    trip_data = request.json

    if not trip_data:
        return jsonify({'error': 'No trip data provided'}), 400

    # Ensure required fields are present
    required_fields = ['date', 'distance', 'duration']
    missing_fields = [field for field in required_fields if field not in trip_data]

    if missing_fields:
        return jsonify({'error': f'Missing required fields: {", ".join(missing_fields)}'}), 400

    try:
        # Ensure we have a proper timestamp for the trip
        current_time = datetime.now()

        # If no date provided or date is invalid, use current time
        if 'date' not in trip_data or not trip_data['date']:
            trip_data['date'] = current_time.isoformat()
        else:
            # Validate the provided date
            try:
                # Try to parse the provided date
                provided_date = datetime.fromisoformat(trip_data['date'].replace('Z', '+00:00'))
                # If parsing succeeds, keep the provided date but ensure it's in ISO format
                trip_data['date'] = provided_date.isoformat()
            except (ValueError, AttributeError):
                # If parsing fails, use current time
                logger.warning(f"Invalid date provided: {trip_data.get('date')}, using current time")
                trip_data['date'] = current_time.isoformat()

        # Add metadata
        if 'name' not in trip_data or not trip_data['name']:
            # Use the validated date for the trip name
            date_for_name = trip_data['date'].split('T')[0]  # Extract date part
            trip_data['name'] = f"Trip on {date_for_name}"

        trip_data['created_at'] = current_time.isoformat()
        trip_data['username'] = username

        # Get user data from user manager
        if username not in user_manager.users:
            return jsonify({'error': 'User not found'}), 404

        user_data = user_manager.users[username]

        # Initialize stats if they don't exist
        if 'stats' not in user_data:
            user_data['stats'] = {
                'total_trips': 0,
                'total_distance': 0.0,
                'total_co2_saved': 0.0,
                'total_calories': 0,
                'trips': []
            }

        # Initialize trips array if it doesn't exist
        if 'trips' not in user_data['stats']:
            user_data['stats']['trips'] = []

        # Add trip ID
        trip_data['id'] = f"trip_{len(user_data['stats']['trips']) + 1}"

        # Calculate additional trip metrics if not provided
        distance = float(trip_data.get('distance', 0))
        duration = float(trip_data.get('duration', 0))

        if 'co2_saved' not in trip_data:
            trip_data['co2_saved'] = distance * 0.12  # Rough estimate: 0.12 kg CO2 per km

        if 'calories' not in trip_data:
            trip_data['calories'] = int(distance * 50)  # Rough estimate: 50 calories per km

        # Add the trip to the user's trips
        user_data['stats']['trips'].append(trip_data)

        # Update user stats totals
        user_data['stats']['total_trips'] = user_data['stats'].get('total_trips', 0) + 1
        user_data['stats']['total_distance'] = user_data['stats'].get('total_distance', 0.0) + distance
        user_data['stats']['total_co2_saved'] = user_data['stats'].get('total_co2_saved', 0.0) + float(trip_data.get('co2_saved', 0))
        user_data['stats']['total_calories'] = user_data['stats'].get('total_calories', 0) + int(trip_data.get('calories', 0))

        # Save the updated user data
        user_manager.save_users()

        # Also save to database
        try:
            conn = database_manager.create_connection()
            if conn:
                # Get user ID from username
                cursor = conn.cursor()
                cursor.execute("SELECT id FROM users WHERE username = ?", (username,))
                user_row = cursor.fetchone()

                if user_row:
                    user_id = user_row[0]
                    # Add trip to database
                    database_manager.add_trip(conn, (user_id, trip_data['date'], distance, duration,
                                                   float(trip_data.get('co2_saved', 0)),
                                                   int(trip_data.get('calories', 0))))
                    logger.info(f"Trip logged to database for user {username}")

                conn.close()
        except Exception as db_error:
            logger.warning(f"Could not save trip to database: {db_error}")

        # Sync the data
        sync_service.queue_sync_task('sync_user_trips', username=username, data={'trips': user_data['stats']['trips']})

        return jsonify({'success': True, 'trip_id': trip_data['id']})
    except Exception as e:
        logger.error(f"Error logging trip: {e}\n{traceback.format_exc()}")
        return jsonify({'error': f'Failed to log trip: {str(e)}'}), 500

@app.route('/api/routes/<username>')
def get_user_routes(username):
    """
    API endpoint to get user routes.
    Requires authentication or API key.
    """
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    # Only allow users to access their own routes unless they're an admin
    current_user = session['username']
    user_data = user_manager.users.get(current_user, {})
    is_admin = user_data.get('is_admin', False)

    if current_user != username and not is_admin:
        return jsonify({'error': 'Access denied'}), 403

    try:
        routes = route_manager.get_user_routes(username)
        return jsonify(routes)
    except Exception as e:
        logger.error(f"Error getting user routes: {e}")
        return jsonify([])  # Return empty array instead of error

@app.route('/api/routes/analysis/<route_id>')
def get_route_analysis(route_id):
    """
    API endpoint to get detailed analysis for a specific route.
    """
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    username = session['username']

    try:
        # Get the route
        route = route_manager.get_route(username, route_id)

        if not route:
            return jsonify({'error': 'Route not found'}), 404

        # Calculate route analysis
        distance = route.get('distance', 0)
        elevation_gain = route.get('elevation_gain', 0)

        # Calculate difficulty based on distance and elevation
        difficulty = 'easy'
        if distance > 20 or elevation_gain > 300:
            difficulty = 'hard'
        elif distance > 10 or elevation_gain > 150:
            difficulty = 'medium'

        # Calculate estimated time based on average speed
        avg_speed = 15  # km/h
        estimated_time_minutes = (distance / avg_speed) * 60

        # Calculate calories burned (rough estimate)
        calories_per_km = 50
        calories = distance * calories_per_km

        # Calculate CO2 savings
        co2_per_km = 0.12  # kg
        co2_saved = distance * co2_per_km

        # Weather conditions (would be fetched from a weather API in a real implementation)
        weather = {
            'temperature': 22,
            'conditions': 'Sunny',
            'wind_speed': 10,
            'precipitation_chance': 0
        }

        # Traffic conditions (would be fetched from a traffic API in a real implementation)
        traffic = {
            'congestion_level': 'low',
            'busy_segments': []
        }

        return jsonify({
            'route_id': route_id,
            'distance': distance,
            'elevation_gain': elevation_gain,
            'difficulty': difficulty,
            'estimated_time_minutes': estimated_time_minutes,
            'calories': calories,
            'co2_saved': co2_saved,
            'weather': weather,
            'traffic': traffic,
            'safety_score': 85,  # Would be calculated based on various factors
            'scenic_score': 75   # Would be calculated based on various factors
        })
    except Exception as e:
        logger.error(f"Error analyzing route: {e}")
        return jsonify({'error': 'Server error'}), 500

@app.route('/api/routes/safety/<route_id>')
def get_route_safety(route_id):
    """
    API endpoint to get safety assessment for a specific route.
    """
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    username = session['username']

    try:
        # Get the route
        route = route_manager.get_route(username, route_id)

        if not route:
            return jsonify({'error': 'Route not found'}), 404

        # In a real implementation, this would use real safety data
        # For now, we'll generate some sample data

        # Overall safety score (0-100)
        safety_score = 85

        # Safety factors
        safety_factors = [
            {
                'name': 'Bike Lanes',
                'score': 90,
                'description': '90% of the route has dedicated bike lanes'
            },
            {
                'name': 'Traffic Volume',
                'score': 70,
                'description': 'Moderate traffic in some segments'
            },
            {
                'name': 'Road Conditions',
                'score': 85,
                'description': 'Good road surface with some minor issues'
            },
            {
                'name': 'Intersection Safety',
                'score': 80,
                'description': '8 intersections, most with bike-friendly signals'
            },
            {
                'name': 'Lighting',
                'score': 75,
                'description': 'Well-lit during daytime, some dark spots at night'
            }
        ]

        # Safety concerns
        safety_concerns = [
            {
                'description': 'Heavy traffic during rush hours (7-9 AM, 5-7 PM)',
                'severity': 'medium'
            },
            {
                'description': 'Construction zone at km 5.2 - reduced visibility',
                'severity': 'high'
            }
        ]

        # Recommendations
        recommendations = [
            'Avoid rush hour times for safer cycling',
            'Use lights and reflective gear in low-light conditions',
            'Take extra caution in the construction zone',
            'Consider alternative route during peak traffic times'
        ]

        return jsonify({
            'route_id': route_id,
            'safety_score': safety_score,
            'safety_factors': safety_factors,
            'safety_concerns': safety_concerns,
            'recommendations': recommendations
        })
    except Exception as e:
        logger.error(f"Error getting route safety: {e}")
        return jsonify({'error': 'Server error'}), 500

@app.route('/api/routes/compare', methods=['POST'])
def compare_routes():
    """
    API endpoint to compare multiple routes.
    """
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    username = session['username']
    data = request.json

    if not data or 'route_ids' not in data:
        return jsonify({'error': 'No route IDs provided'}), 400

    route_ids = data['route_ids']

    if len(route_ids) < 2:
        return jsonify({'error': 'At least two routes required for comparison'}), 400

    try:
        compared_routes = []

        for route_id in route_ids:
            route = route_manager.get_route(username, route_id)
            if route:
                distance = route.get('distance', 0)
                elevation_gain = route.get('elevation_gain', 0)

                # Calculate difficulty
                difficulty = 'easy'
                if distance > 20 or elevation_gain > 300:
                    difficulty = 'hard'
                elif distance > 10 or elevation_gain > 150:
                    difficulty = 'medium'

                # Calculate estimated time
                avg_speed = 15  # km/h
                estimated_time_minutes = (distance / avg_speed) * 60

                # Calculate CO2 savings
                co2_saved = distance * 0.12

                compared_routes.append({
                    'route_id': route_id,
                    'name': route.get('name', 'Unnamed Route'),
                    'distance': distance,
                    'elevation_gain': elevation_gain,
                    'difficulty': difficulty,
                    'estimated_time_minutes': estimated_time_minutes,
                    'co2_saved': co2_saved
                })

        # Determine recommendation (shortest route for now)
        if compared_routes:
            recommended_route = min(compared_routes, key=lambda r: r['distance'])
            recommendation = recommended_route['route_id']
        else:
            recommendation = None

        return jsonify({
            'routes': compared_routes,
            'recommendation': recommendation
        })
    except Exception as e:
        logger.error(f"Error comparing routes: {e}")
        return jsonify({'error': 'Server error'}), 500

@app.route('/upload', methods=['POST'])
def upload_file():
    """
    Handle file uploads (photos/videos).
    """
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400

    file_type = request.form.get('type', 'photo')
    if file_type not in ['photo', 'video']:
        return jsonify({'error': 'Invalid file type'}), 400

    filename = secure_filename(file.filename)
    upload_dir = os.path.join(app.config['UPLOAD_FOLDER'], file_type + 's')
    file_path = os.path.join(upload_dir, filename)

    file.save(file_path)

    # Save file metadata to user's profile
    username = session['username']
    file_url = f"/uploads/{file_type}s/{filename}"

    # This would ideally be handled by a dedicated media manager
    user_data = user_manager.users.get(username, {})
    if 'media' not in user_data:
        user_data['media'] = []

    user_data['media'].append({
        'type': file_type,
        'url': file_url,
        'filename': filename,
        'uploaded_at': datetime.now().isoformat()
    })

    user_manager.users[username] = user_data
    user_manager.save_users()

    return jsonify({'success': True, 'url': file_url})

# --- Real-time tracking endpoints ---
@app.route('/api/tracking/start', methods=['POST'])
def start_tracking():
    """Start real-time tracking for a user."""
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    username = session['username']
    # Logic to start tracking session
    # This would be implemented with WebSockets for real-time updates

    return jsonify({'success': True, 'tracking_id': 'temp-id'})

@app.route('/api/tracking/update', methods=['POST'])
def update_tracking():
    """Update position during real-time tracking."""
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    data = request.json
    # Process and store location update

    return jsonify({'success': True})

@app.route('/api/tracking/end', methods=['POST'])
def end_tracking():
    """End real-time tracking session."""
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    data = request.json
    # Process end of tracking, calculate stats, etc.

    return jsonify({'success': True})

# --- Notification endpoints ---
@app.route('/api/notifications/register', methods=['POST'])
def register_push_notification():
    """Register a device for push notifications."""
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    data = request.json
    # Store device token for push notifications

    return jsonify({'success': True})

@app.route('/api/notifications/settings', methods=['GET', 'POST'])
def notification_settings():
    """Get or update notification settings."""
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    username = session['username']

    if request.method == 'POST':
        data = request.json
        # Update notification preferences
        user_manager.update_user_preferences(username, {
            'notifications': data
        })
        return jsonify({'success': True})
    else:
        # Get notification preferences
        user_data = user_manager.users.get(username, {})
        notification_prefs = user_data.get('preferences', {}).get('notifications', {})
        return jsonify(notification_prefs)

@app.route('/static/<path:path>')
def serve_static(path):
    """Serve static files."""
    return send_from_directory('static', path)

@app.route('/uploads/<path:path>')
def serve_uploads(path):
    """Serve uploaded files."""
    return send_from_directory(app.config['UPLOAD_FOLDER'], path)

def start_web_server(debug=False, host='0.0.0.0', port=5050):
    """Start the web server."""
    # Ensure required packages are installed
    required_packages = [
        'flask', 'flask-cors', 'werkzeug', 'gunicorn',
        'eventlet', 'flask-socketio'
    ]
    success, failed = ensure_packages(required_packages)

    if not success:
        logger.error(f"Failed to install required packages: {failed}")
        return False

    try:
        app.run(debug=debug, host=host, port=port)
        return True
    except Exception as e:
        logger.error(f"Failed to start web server: {e}")

# --- Feature Routes ---
@app.route('/route-planner')
def route_planner():
    """Render the route planner page."""
    if 'username' not in session:
        return redirect(url_for('login'))

    username = session['username']
    user_data = sync_service.get_user_data(username)

    # Get user's saved routes
    saved_routes = route_manager.get_user_routes(username)

    return render_template('route_planner.html',
                          username=username,
                          user=user_data,
                          saved_routes=saved_routes)

@app.route('/api/routes/save', methods=['POST'])
def save_route():
    """Save a new route."""
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    username = session['username']
    route_data = request.json

    if not route_data:
        return jsonify({'error': 'No route data provided'}), 400

    # Ensure required fields are present
    required_fields = ['name', 'start_point', 'end_point', 'distance', 'coordinates']
    missing_fields = [field for field in required_fields if field not in route_data]

    if missing_fields:
        return jsonify({'error': f'Missing required fields: {", ".join(missing_fields)}'}), 400

    # Add metadata
    route_data['created_at'] = datetime.now().isoformat()
    route_data['username'] = username

    # Save the route
    route_id = route_manager.add_route(username, route_data)

    # Update user stats
    user_data = user_manager.get_user(username)
    if 'total_routes' not in user_data:
        user_data['total_routes'] = 0
    user_data['total_routes'] += 1
    user_manager.update_user(username, user_data)

    return jsonify({'success': True, 'route_id': route_id})

@app.route('/community')
def community():
    """Render the community page."""
    if 'username' not in session:
        return redirect(url_for('login'))

    username = session['username']
    user_data = sync_service.get_user_data(username)

    # Get latest posts from the forum
    forum_posts = database_manager.get_forum_posts(limit=10)

    # Get top contributors
    contributors = database_manager.get_top_contributors(limit=5)

    return render_template('community.html',
                          username=username,
                          user=user_data,
                          forum_posts=forum_posts,
                          contributors=contributors)

@app.route('/community/forum')
def forum():
    """Render the forum main page."""
    if 'username' not in session:
        return redirect(url_for('login'))

    return render_template('forum/index.html', username=session['username'])

@app.route('/api/forum/posts', methods=['GET'])
def get_forum_posts():
    """API endpoint to get forum posts."""
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    category = request.args.get('category', 'all')
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))

    posts = database_manager.get_forum_posts(category=category, page=page, limit=limit)
    total = database_manager.count_forum_posts(category=category)

    return jsonify({
        'posts': posts,
        'total': total,
        'page': page,
        'pages': (total + limit - 1) // limit  # Ceiling division
    })

@app.route('/api/forum/posts', methods=['POST'])
def create_forum_post():
    """API endpoint to create a forum post."""
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    data = request.json
    if not data or 'title' not in data or 'content' not in data:
        return jsonify({'error': 'Missing required fields'}), 400

    username = session['username']
    post_data = {
        'title': data['title'],
        'content': data['content'],
        'category': data.get('category', 'general'),
        'username': username,
        'created_at': datetime.now().isoformat()
    }

    post_id = database_manager.create_forum_post(post_data)

    return jsonify({'success': True, 'post_id': post_id})

@app.route('/achievements')
def achievements():
    """Render the achievements page."""
    if 'username' not in session:
        return redirect(url_for('login'))

    username = session['username']
    user_data = sync_service.get_user_data(username)

    # Get user achievements (placeholder for now)
    achievements = []
    if user_data and 'achievements' in user_data:
        achievements = user_data['achievements']

    return render_template('achievements.html',
                          username=username,
                          user=user_data,
                          achievements=achievements)

@app.route('/challenges')
def challenges():
    """Render the challenges page."""
    if 'username' not in session:
        return redirect(url_for('login'))

    username = session['username']
    user_data = sync_service.get_user_data(username)

    # Get active and completed challenges for the user
    active_challenges = database_manager.get_user_challenges(username, status='active')
    completed_challenges = database_manager.get_user_challenges(username, status='completed')

    # Get available challenges the user hasn't joined
    available_challenges = database_manager.get_available_challenges(username)

    return render_template('challenges.html',
                          username=username,
                          user=user_data,
                          active_challenges=active_challenges,
                          completed_challenges=completed_challenges,
                          available_challenges=available_challenges)

@app.route('/api/challenges/join', methods=['POST'])
def join_challenge():
    """API endpoint to join a challenge."""
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    data = request.json
    if not data or 'challenge_id' not in data:
        return jsonify({'error': 'Missing challenge ID'}), 400

    username = session['username']
    challenge_id = data['challenge_id']

    # Join the challenge
    success = database_manager.join_challenge(username, challenge_id)

    if success:
        return jsonify({'success': True})
    else:
        return jsonify({'error': 'Failed to join challenge'}), 500

@app.route('/api/challenges/update', methods=['POST'])
def update_challenge_progress():
    """API endpoint to update challenge progress."""
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    data = request.json
    if not data or 'challenge_id' not in data or 'progress' not in data:
        return jsonify({'error': 'Missing required fields'}), 400

    username = session['username']
    challenge_id = data['challenge_id']
    progress = data['progress']

    # Update challenge progress
    success = database_manager.update_challenge_progress(username, challenge_id, progress)

    if success:
        return jsonify({'success': True})
    else:
        return jsonify({'error': 'Failed to update challenge progress'}), 500

@app.route('/api/sync/refresh/<username>', methods=['POST'])
def refresh_user_data(username):
    """
    Force a refresh of user data from all data sources.
    """
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    # Only allow users to refresh their own data unless they're an admin
    current_user = session['username']
    user_data = user_manager.users.get(current_user, {})
    is_admin = user_data.get('is_admin', False)

    if current_user != username and not is_admin:
        return jsonify({'error': 'Access denied'}), 403

    try:
        # Force refresh from all sources
        updated_data = sync_service.refresh_user_data(username)

        if updated_data:
            # Check if any part of the data is stored as JSON string and parse it
            processed_data = process_json_strings(updated_data)

            return jsonify({
                'success': True,
                'user': processed_data,
                'message': 'User data refreshed successfully',
                'timestamp': datetime.now().isoformat()
            })
        else:
            # Try database fallback
            db_data = sync_service._get_user_data_from_database(username)
            if db_data:
                processed_data = process_json_strings(db_data)
                return jsonify({
                    'success': True,
                    'user': processed_data,
                    'message': 'User data retrieved from database fallback',
                    'timestamp': datetime.now().isoformat()
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'User not found in any data source'
                }), 404
    except Exception as e:
        logger.error(f"Error refreshing user data: {e}\n{traceback.format_exc()}")

        # Try database fallback on exception
        try:
            db_data = sync_service._get_user_data_from_database(username)
            if db_data:
                processed_data = process_json_strings(db_data)
                return jsonify({
                    'success': True,
                    'user': processed_data,
                    'message': 'Using database as fallback due to sync error',
                    'timestamp': datetime.now().isoformat()
                })
        except Exception as db_e:
            logger.error(f"Database fallback also failed: {db_e}\n{traceback.format_exc()}")

        return jsonify({
            'success': False,
            'message': 'Failed to synchronize data. Please try again. Use the database as a fallback.'
        }), 500

@app.route('/api/sync/stats/<username>', methods=['POST'])
def sync_user_stats(username):
    """
    Update and synchronize user statistics.
    """
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    # Only allow users to update their own stats unless they're an admin
    current_user = session['username']
    user_data = user_manager.users.get(current_user, {})
    is_admin = user_data.get('is_admin', False)

    if current_user != username and not is_admin:
        return jsonify({'error': 'Access denied'}), 403

    try:
        # Get stats from request body
        stats_data = request.json
        if not stats_data:
            return jsonify({'error': 'No data provided'}), 400

        # Update user data with synchronization
        success = sync_service.update_user_data(username, {'stats': stats_data}, source='web')

        if success:
            return jsonify({
                'success': True,
                'message': 'User stats synchronized successfully',
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to synchronize user stats'
            }), 500
    except Exception as e:
        logger.error(f"Error synchronizing user stats: {e}")
        return jsonify({
            'success': False,
            'message': f'Error synchronizing stats: {str(e)}'
        }), 500

@app.route('/api/sync/preferences/<username>', methods=['POST'])
def sync_user_preferences(username):
    """
    Update and synchronize user preferences.
    """
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    # Only allow users to update their own preferences unless they're an admin
    current_user = session['username']
    user_data = user_manager.users.get(current_user, {})
    is_admin = user_data.get('is_admin', False)

    if current_user != username and not is_admin:
        return jsonify({'error': 'Access denied'}), 403

    try:
        # Get preferences from request body
        preferences_data = request.json
        if not preferences_data:
            return jsonify({'error': 'No data provided'}), 400

        # Update user data with synchronization
        success = sync_service.update_user_data(username, {'preferences': preferences_data}, source='web')

        if success:
            return jsonify({
                'success': True,
                'message': 'User preferences synchronized successfully',
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to synchronize user preferences'
            }), 500
    except Exception as e:
        logger.error(f"Error synchronizing user preferences: {e}")
        return jsonify({
            'success': False,
            'message': f'Error synchronizing preferences: {str(e)}'
        }), 500

@app.route('/api/sync/trips/<username>', methods=['POST'])
def sync_user_trips(username):
    """
    Update and synchronize user trips.
    """
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    # Only allow users to update their own trips unless they're an admin
    current_user = session['username']
    user_data = user_manager.users.get(current_user, {})
    is_admin = user_data.get('is_admin', False)

    if current_user != username and not is_admin:
        return jsonify({'error': 'Access denied'}), 403

    try:
        # Get trips from request body
        trips_data = request.json
        if not trips_data or 'trips' not in trips_data:
            return jsonify({'error': 'No trips data provided'}), 400

        # Update user data with synchronization
        success = sync_service.update_user_data(username, {'trips': trips_data['trips']}, source='web')

        if success:
            return jsonify({
                'success': True,
                'message': 'User trips synchronized successfully',
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to synchronize user trips'
            }), 500
    except Exception as e:
        logger.error(f"Error synchronizing user trips: {e}")
        return jsonify({
            'success': False,
            'message': f'Error synchronizing trips: {str(e)}'
        }), 500

# Helper function to recursively process any JSON strings in data
def process_json_strings(data):
    """Recursively parse any JSON strings in the data structure"""
    if isinstance(data, dict):
        result = {}
        for key, value in data.items():
            if isinstance(value, str) and (value.startswith('{') or value.startswith('[')):
                try:
                    result[key] = json.loads(value)
                except json.JSONDecodeError:
                    result[key] = value
            elif isinstance(value, (dict, list)):
                result[key] = process_json_strings(value)
            else:
                result[key] = value
        return result
    elif isinstance(data, list):
        result = []
        for item in data:
            if isinstance(item, str) and (item.startswith('{') or item.startswith('[')):
                try:
                    result.append(json.loads(item))
                except json.JSONDecodeError:
                    result.append(item)
            elif isinstance(item, (dict, list)):
                result.append(process_json_strings(item))
            else:
                result.append(item)
        return result
    else:
        return data

@app.route('/api/sync/status', methods=['GET'])
def sync_status():
    """
    Get the current synchronization status.
    """
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    username = session['username']

    try:
        # Check if Google Sheets is available
        sheets_available = False
        try:
            sheets_available = sheets_manager.is_available() if sheets_manager else False
        except Exception as sheets_e:
            logger.warning(f"Error checking sheets availability: {sheets_e}")

        # Get database availability
        db_available = True
        try:
            with database_manager.get_connection() as conn:
                if not conn:
                    db_available = False
        except Exception as db_e:
            logger.warning(f"Error checking database availability: {db_e}")
            db_available = False

        # Get last sync timestamps
        last_sync = sync_service.last_sync_timestamps.get('full_sync', 0)
        # Only convert to datetime if we have a valid timestamp (not 0)
        if last_sync and last_sync > 0:
            try:
                last_sync_time = datetime.fromtimestamp(last_sync).isoformat()
            except (ValueError, OSError) as e:
                logger.warning(f"Invalid timestamp {last_sync}: {e}")
                last_sync_time = 'Never'
        else:
            last_sync_time = 'Never'

        return jsonify({
            'success': True,
            'sync_enabled': True,
            'sheets_available': sheets_available,
            'database_available': db_available,
            'last_sync': last_sync_time,
            'username': username,
            'fallback_enabled': True
        })
    except Exception as e:
        logger.error(f"Error getting sync status: {e}")
        return jsonify({
            'success': False,
            'message': f'Error getting sync status: {str(e)}'
        }), 500

@app.route('/api/weather/<location>')
def get_weather(location):
    """
    API endpoint to get weather data for a location.
    """
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    try:
        # In a real implementation, this would call a weather API
        # For now, we'll return mock data
        weather_data = {
            'location': location,
            'temperature': 22,
            'conditions': 'Sunny',
            'humidity': 65,
            'wind_speed': 10,
            'precipitation_chance': 0,
            'uv_index': 6,
            'visibility': 10,
            'forecast': [
                {'day': 'Today', 'high': 24, 'low': 18, 'conditions': 'Sunny'},
                {'day': 'Tomorrow', 'high': 26, 'low': 20, 'conditions': 'Partly Cloudy'},
                {'day': 'Day 3', 'high': 23, 'low': 17, 'conditions': 'Cloudy'},
                {'day': 'Day 4', 'high': 21, 'low': 15, 'conditions': 'Light Rain'},
                {'day': 'Day 5', 'high': 25, 'low': 19, 'conditions': 'Sunny'}
            ]
        }
        return jsonify(weather_data)
    except Exception as e:
        logger.error(f"Error getting weather data: {e}")
        return jsonify({'error': 'Server error'}), 500

@app.route('/api/sync/refresh', methods=['POST'])
def refresh_sync():
    """
    API endpoint to force refresh synchronization.
    """
    if 'username' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    username = session['username']

    try:
        if sync_service:
            # Force refresh user data
            refreshed_data = sync_service.refresh_user_data(username)

            if refreshed_data:
                return jsonify({
                    'success': True,
                    'message': 'Data refreshed successfully',
                    'user_data': {
                        'total_trips': refreshed_data.get('stats', {}).get('total_trips', 0),
                        'total_distance': refreshed_data.get('stats', {}).get('total_distance', 0.0),
                        'total_co2_saved': refreshed_data.get('stats', {}).get('total_co2_saved', 0.0),
                        'total_calories': refreshed_data.get('stats', {}).get('total_calories', 0)
                    }
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'No data found for user'
                }), 404
        else:
            return jsonify({
                'success': False,
                'message': 'Sync service not available'
            }), 503
    except Exception as e:
        logger.error(f"Error refreshing sync: {e}")
        return jsonify({
            'success': False,
            'message': f'Error refreshing data: {str(e)}'
        }), 500

@app.route('/auth/guest')
def guest_login():
    """Handle guest login."""
    session['username'] = 'guest'
    return redirect(url_for('index'))

@app.route('/signup', methods=['GET', 'POST'])
def signup():
    """Handle user registration."""
    error = None
    success = None

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        email = request.form.get('email')
        name = request.form.get('name', username)

        # Validate input
        if not username or not password or not email:
            error = 'All fields are required.'
        elif password != confirm_password:
            error = 'Passwords do not match.'
        elif len(password) < 8:
            error = 'Password must be at least 8 characters long.'
        elif username in user_manager.users:
            error = 'Username already exists. Please choose another one.'
        else:
            # Attempt to register the user
            try:
                # Create user manually since we're in web context
                import hashlib
                import base64
                import os

                # Generate salt and hash password
                salt_bytes = os.urandom(32)
                salt = base64.b64encode(salt_bytes).decode('utf-8')
                password_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt_bytes, 100000)
                password_hash = base64.b64encode(password_hash).decode('utf-8')

                # Create user data
                user_data = {
                    'username': username,
                    'name': name,
                    'email': email,
                    'password_hash': password_hash,
                    'salt': salt,
                    'is_admin': False,
                    'is_guest': False,
                    'email_verified': False,
                    'registration_date': datetime.now().isoformat(),
                    'stats': {
                        'total_trips': 0,
                        'total_distance': 0.0,
                        'total_co2_saved': 0.0,
                        'total_calories': 0,
                        'trips': []
                    },
                    'preferences': {
                        'require_email_verification': True
                    }
                }

                # Add user to user manager
                user_manager.users[username] = user_data

                # Save users
                if user_manager.save_users():
                    session['username'] = username

                    # Synchronize the new user across platforms
                    if sync_service:
                        sync_service.queue_sync_task('sync_new_user', data=user_data)

                    flash('Account created successfully! Welcome to EcoCycle.', 'success')
                    return redirect(url_for('index'))
                else:
                    error = 'Failed to save account. Please try again later.'
            except Exception as e:
                logger.error(f"Error creating user account: {e}")
                error = 'Failed to create account. Please try again later.'

    return render_template('signup.html', error=error, success=success)

if __name__ == '__main__':
    # Set up logging
    logging.basicConfig(level=logging.INFO)

    # Start the web server
    app.run(debug=True, host='0.0.0.0', port=5050)
