"""
EcoCycle - Data Visualization Module (Modular Implementation)
This package provides data visualization capabilities for cycling data and statistics.
"""

try:
    from .base import DataVisualization
    __all__ = ['DataVisualization']
except ImportError as e:
    # <PERSON><PERSON> missing optional dependencies gracefully
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"Data visualization features not available: {e}")

    class DataVisualization:
        """Placeholder class when dependencies are missing."""
        def __init__(self, *args, **kwargs):
            raise ImportError("Data visualization features require additional dependencies. Please install with: pip install ecocycle[visualization]")

    __all__ = ['DataVisualization']
