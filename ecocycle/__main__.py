#!/usr/bin/env python3
"""
EcoCycle - Main Entry Point
Entry point for running EcoCycle as a module: python -m ecocycle
"""

import sys
import os

# Add the parent directory to the path so we can import from ecocycle
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    """Main entry point for the EcoCycle package."""
    try:
        # Import the main application
        from ecocycle.cli.main import main as cli_main
        
        # Run the CLI
        cli_main()
        
    except ImportError as e:
        print(f"Error importing EcoCycle modules: {e}")
        print("Please ensure EcoCycle is properly installed.")
        sys.exit(1)
    except Exception as e:
        print(f"Error running EcoCycle: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
