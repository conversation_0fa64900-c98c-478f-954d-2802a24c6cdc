"""
EcoCycle - Notifications Package
Provides a modular notification system for the EcoCycle application.
"""

# Re-export the main components for cleaner imports
from ecocycle.services.notifications.notification_system import NotificationSystem, run_notification_manager
from ecocycle.services.notifications.manager import NotificationManager
from ecocycle.services.notifications.ui import NotificationUI
from ecocycle.services.notifications.senders import EmailSender, SmsSender, AppNotifier
from ecocycle.services.notifications.storage import NotificationStorage
from ecocycle.services.notifications.templates import TemplateManager
from ecocycle.services.notifications.generators import ContentGenerator

# Provide backwards compatibility
__all__ = [
    'NotificationSystem',
    'run_notification_manager',
    'NotificationManager',
    'NotificationUI',
    'EmailSender',
    'SmsSender',
    'AppNotifier',
    'NotificationStorage',
    'TemplateManager',
    'ContentGenerator',
]