{% extends "base.html" %}

{% block title %}EcoCycle - Community{% endblock %}

{% block head %}
<style>
    .post-card {
        transition: transform 0.2s;
        margin-bottom: 1.5rem;
    }
    .post-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .profile-card {
        padding: 1.5rem;
        text-align: center;
        border-radius: 8px;
    }
    .profile-img {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        object-fit: cover;
        margin: 0 auto 1rem;
        border: 3px solid #28a745;
    }
    .stats-container {
        display: flex;
        justify-content: space-between;
        margin-top: 1rem;
    }
    .stat-item {
        text-align: center;
        flex: 1;
    }
    .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #28a745;
    }
    .stat-label {
        font-size: 0.8rem;
        color: #6c757d;
    }
    .contributor-card {
        display: flex;
        align-items: center;
        padding: 0.8rem;
        margin-bottom: 0.5rem;
        border-radius: 8px;
        background-color: #f8f9fa;
        transition: transform 0.2s;
    }
    .contributor-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 10px rgba(0,0,0,0.05);
    }
    .contributor-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 1rem;
        object-fit: cover;
    }
    .event-card {
        border-left: 4px solid #28a745;
        margin-bottom: 1rem;
        transition: transform 0.2s;
    }
    .event-card:hover {
        transform: translateX(5px);
    }
    .nav-tabs .nav-link.active {
        color: #28a745;
        border-bottom: 2px solid #28a745;
        font-weight: 500;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1><i class="fas fa-users me-2"></i>Community</h1>
            <p class="text-muted">Connect with other cyclists, share experiences, and participate in community events.</p>
        </div>
        <div class="col-md-4 text-end">
            <button class="btn btn-outline-success me-2" type="button" data-bs-toggle="modal" data-bs-target="#createPostModal">
                <i class="fas fa-plus-circle me-1"></i> New Post
            </button>
            <button class="btn btn-success" type="button" data-bs-toggle="modal" data-bs-target="#createEventModal">
                <i class="fas fa-calendar-plus me-1"></i> Create Event
            </button>
        </div>
    </div>

    <div class="row">
        <!-- Left Column: User Profile and Top Contributors -->
        <div class="col-md-3 mb-4">
            <!-- User Profile Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-body profile-card">
                    <img src="{{ url_for('static', filename='img/default-avatar.png') }}" alt="User Avatar" class="profile-img">
                    <h5>{{ username }}</h5>
                    <p class="text-muted mb-2">EcoCyclist since {{ user.joined_date|default('2025') }}</p>
                    
                    {% if user.badge %}
                    <span class="badge bg-success mb-3">{{ user.badge }}</span>
                    {% endif %}
                    
                    <div class="stats-container">
                        <div class="stat-item">
                            <div class="stat-value">{{ user.total_routes|default(0) }}</div>
                            <div class="stat-label">Routes</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">{{ user.total_posts|default(0) }}</div>
                            <div class="stat-label">Posts</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">{{ user.eco_points|default(0) }}</div>
                            <div class="stat-label">Points</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Top Contributors Card -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Top Contributors</h5>
                </div>
                <div class="card-body">
                    {% if contributors %}
                        {% for contributor in contributors %}
                        <div class="contributor-card">
                            <img src="{{ url_for('static', filename='img/default-avatar.png') }}" alt="Avatar" class="contributor-avatar">
                            <div>
                                <h6 class="mb-0">{{ contributor.username }}</h6>
                                <small class="text-muted">{{ contributor.eco_points }} points</small>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-2x text-muted mb-2"></i>
                            <p class="text-muted">No contributors data available.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Middle Column: Forum Posts -->
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <ul class="nav nav-tabs card-header-tabs">
                        <li class="nav-item">
                            <a class="nav-link active" id="latest-tab" data-bs-toggle="tab" href="#latest">Latest</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="popular-tab" data-bs-toggle="tab" href="#popular">Popular</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="routes-tab" data-bs-toggle="tab" href="#routes">Routes</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="tips-tab" data-bs-toggle="tab" href="#tips">Tips</a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="latest">
                            {% if forum_posts %}
                                {% for post in forum_posts %}
                                <div class="post-card card mb-3">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center mb-2">
                                            <img src="{{ url_for('static', filename='img/default-avatar.png') }}" alt="Avatar" width="40" height="40" class="rounded-circle me-2">
                                            <div>
                                                <h6 class="mb-0">{{ post.username }}</h6>
                                                <small class="text-muted">{{ post.created_at }}</small>
                                            </div>
                                            {% if post.category %}
                                            <span class="badge bg-light text-dark ms-auto">{{ post.category }}</span>
                                            {% endif %}
                                        </div>
                                        <h5 class="card-title">{{ post.title }}</h5>
                                        <p class="card-text">{{ post.content|truncate(150) }}</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="btn-group">
                                                <a href="/community/forum/post/{{ post.id }}" class="btn btn-sm btn-outline-primary">Read More</a>
                                                <button type="button" class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-comment me-1"></i> {{ post.comment_count|default(0) }}
                                                </button>
                                            </div>
                                            <div>
                                                <button type="button" class="btn btn-sm btn-outline-success me-1">
                                                    <i class="fas fa-thumbs-up"></i> {{ post.likes|default(0) }}
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-share-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <div class="text-center py-5">
                                    <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No forum posts yet. Be the first to post!</p>
                                    <button class="btn btn-success" type="button" data-bs-toggle="modal" data-bs-target="#createPostModal">
                                        <i class="fas fa-plus-circle me-1"></i> Create Post
                                    </button>
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="tab-pane fade" id="popular">
                            <div class="text-center py-5">
                                <i class="fas fa-fire fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Popular posts will appear here.</p>
                            </div>
                        </div>
                        
                        <div class="tab-pane fade" id="routes">
                            <div class="text-center py-5">
                                <i class="fas fa-route fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Shared routes will appear here.</p>
                            </div>
                        </div>
                        
                        <div class="tab-pane fade" id="tips">
                            <div class="text-center py-5">
                                <i class="fas fa-lightbulb fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Cycling tips will appear here.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-white">
                    <nav aria-label="Forum pagination">
                        <ul class="pagination justify-content-center mb-0">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item">
                                <a class="page-link" href="#">Next</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
        
        <!-- Right Column: Upcoming Events and Quick Links -->
        <div class="col-md-3 mb-4">
            <!-- Upcoming Events Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Upcoming Events</h5>
                </div>
                <div class="card-body">
                    <div class="event-card p-3">
                        <div class="d-flex align-items-center mb-2">
                            <div class="text-center me-3">
                                <div class="fw-bold">MAY</div>
                                <div class="h4 mb-0">15</div>
                            </div>
                            <div>
                                <h6 class="mb-1">Weekly Group Ride</h6>
                                <small class="text-muted">10:00 AM • Marina Green</small>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-outline-success w-100">Join Event</button>
                    </div>
                    
                    <div class="event-card p-3">
                        <div class="d-flex align-items-center mb-2">
                            <div class="text-center me-3">
                                <div class="fw-bold">MAY</div>
                                <div class="h4 mb-0">23</div>
                            </div>
                            <div>
                                <h6 class="mb-1">Bike Maintenance Workshop</h6>
                                <small class="text-muted">2:00 PM • EcoCycle Hub</small>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-outline-success w-100">Join Event</button>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="/community/events" class="btn btn-link text-success">View All Events</a>
                    </div>
                </div>
            </div>
            
            <!-- Quick Links Card -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-link me-2"></i>Quick Links</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="/community/forum" class="list-group-item list-group-item-action">
                            <i class="fas fa-comments me-2"></i> Forum
                        </a>
                        <a href="/community/events" class="list-group-item list-group-item-action">
                            <i class="fas fa-calendar-alt me-2"></i> Events Calendar
                        </a>
                        <a href="/community/groups" class="list-group-item list-group-item-action">
                            <i class="fas fa-user-friends me-2"></i> Cycling Groups
                        </a>
                        <a href="/community/routes" class="list-group-item list-group-item-action">
                            <i class="fas fa-route me-2"></i> Shared Routes
                        </a>
                        <a href="/community/leaderboard" class="list-group-item list-group-item-action">
                            <i class="fas fa-trophy me-2"></i> Leaderboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Post Modal -->
<div class="modal fade" id="createPostModal" tabindex="-1" aria-labelledby="createPostModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createPostModalLabel"><i class="fas fa-plus-circle me-2"></i>Create New Post</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="post-form">
                    <div class="mb-3">
                        <label for="post-title" class="form-label">Title</label>
                        <input type="text" class="form-control" id="post-title" placeholder="Enter a title" required>
                    </div>
                    <div class="mb-3">
                        <label for="post-category" class="form-label">Category</label>
                        <select class="form-select" id="post-category" required>
                            <option value="general">General Discussion</option>
                            <option value="routes">Routes & Trails</option>
                            <option value="tips">Tips & Advice</option>
                            <option value="gear">Gear & Equipment</option>
                            <option value="events">Events</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="post-content" class="form-label">Content</label>
                        <textarea class="form-control" id="post-content" rows="5" placeholder="Share your thoughts..." required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="post-tags" class="form-label">Tags (optional)</label>
                        <input type="text" class="form-control" id="post-tags" placeholder="Separate tags with commas">
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="notify-replies">
                            <label class="form-check-label" for="notify-replies">
                                Notify me of replies
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="submit-post-btn">Publish Post</button>
            </div>
        </div>
    </div>
</div>

<!-- Create Event Modal -->
<div class="modal fade" id="createEventModal" tabindex="-1" aria-labelledby="createEventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createEventModalLabel"><i class="fas fa-calendar-plus me-2"></i>Create New Event</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="event-form">
                    <div class="mb-3">
                        <label for="event-title" class="form-label">Event Title</label>
                        <input type="text" class="form-control" id="event-title" placeholder="Enter a title" required>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="event-date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="event-date" required>
                        </div>
                        <div class="col-md-6">
                            <label for="event-time" class="form-label">Time</label>
                            <input type="time" class="form-control" id="event-time" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="event-location" class="form-label">Location</label>
                        <input type="text" class="form-control" id="event-location" placeholder="Enter a location" required>
                    </div>
                    <div class="mb-3">
                        <label for="event-description" class="form-label">Description</label>
                        <textarea class="form-control" id="event-description" rows="4" placeholder="Describe your event..." required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="event-type" class="form-label">Event Type</label>
                        <select class="form-select" id="event-type" required>
                            <option value="group-ride">Group Ride</option>
                            <option value="workshop">Workshop</option>
                            <option value="race">Race</option>
                            <option value="social">Social Gathering</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="event-max-participants" class="form-label">Maximum Participants (optional)</label>
                        <input type="number" class="form-control" id="event-max-participants" min="1">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="submit-event-btn">Create Event</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Post submission handling
        const submitPostBtn = document.getElementById('submit-post-btn');
        if (submitPostBtn) {
            submitPostBtn.addEventListener('click', function() {
                const title = document.getElementById('post-title').value;
                const category = document.getElementById('post-category').value;
                const content = document.getElementById('post-content').value;
                const tags = document.getElementById('post-tags').value;
                
                if (!title || !content) {
                    alert('Please fill in all required fields');
                    return;
                }
                
                // Create post object
                const postData = {
                    title: title,
                    category: category,
                    content: content,
                    tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
                    notify_replies: document.getElementById('notify-replies').checked
                };
                
                // Send post data to server
                fetch('/api/forum/posts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(postData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Close modal and reload page
                        const modal = bootstrap.Modal.getInstance(document.getElementById('createPostModal'));
                        modal.hide();
                        
                        // Show success message
                        alert('Post created successfully!');
                        
                        // Reload the page to show the new post
                        location.reload();
                    } else {
                        alert('Error creating post: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to create post. Please try again.');
                });
            });
        }
        
        // Event submission handling
        const submitEventBtn = document.getElementById('submit-event-btn');
        if (submitEventBtn) {
            submitEventBtn.addEventListener('click', function() {
                const title = document.getElementById('event-title').value;
                const date = document.getElementById('event-date').value;
                const time = document.getElementById('event-time').value;
                const location = document.getElementById('event-location').value;
                const description = document.getElementById('event-description').value;
                const eventType = document.getElementById('event-type').value;
                const maxParticipants = document.getElementById('event-max-participants').value;
                
                if (!title || !date || !time || !location || !description) {
                    alert('Please fill in all required fields');
                    return;
                }
                
                // Create event object
                const eventData = {
                    title: title,
                    date: date,
                    time: time,
                    location: location,
                    description: description,
                    event_type: eventType,
                    max_participants: maxParticipants ? parseInt(maxParticipants) : null
                };
                
                // Send event data to server
                fetch('/api/community/events', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(eventData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Close modal and reload page
                        const modal = bootstrap.Modal.getInstance(document.getElementById('createEventModal'));
                        modal.hide();
                        
                        // Show success message
                        alert('Event created successfully!');
                        
                        // Reload the page to show the new event
                        location.reload();
                    } else {
                        alert('Error creating event: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to create event. Please try again.');
                });
            });
        }
    });
</script>
{% endblock %}
