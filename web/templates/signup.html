{% extends "base.html" %}

{% block title %}Sign Up - EcoCycle{% endblock %}

{% block head %}
<style>
    .signup-container {
        max-width: 500px;
        margin: 3rem auto;
    }
    .card {
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    .card-header {
        background-color: #28a745;
        color: white;
        text-align: center;
        padding: 1.5rem;
    }
    .signup-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    .social-signup-button {
        width: 100%;
        margin-bottom: 10px;
        text-align: left;
    }
    .social-signup-button i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }
    .divider {
        display: flex;
        align-items: center;
        margin: 1rem 0;
    }
    .divider::before, .divider::after {
        content: "";
        flex: 1;
        border-bottom: 1px solid #ddd;
    }
    .divider-text {
        padding: 0 1rem;
        color: #6c757d;
    }
    .password-requirements {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="signup-container">
    <div class="card">
        <div class="card-header">
            <div class="signup-icon">
                <i class="fas fa-user-plus"></i>
            </div>
            <h3>Join EcoCycle</h3>
            <p class="mb-0">Create an account to track your eco-friendly activities</p>
        </div>
        <div class="card-body p-4">
            {% if success %}
            <div class="alert alert-success" role="alert">
                {{ success }}
                <div class="mt-2">
                    <a href="{{ url_for('login') }}" class="btn btn-success btn-sm">Sign In Now</a>
                </div>
            </div>
            {% endif %}
            
            {% if error %}
            <div class="alert alert-danger" role="alert">
                {{ error }}
            </div>
            {% endif %}
            
            <!-- Social Signup Buttons -->
            <div class="social-signup mb-3">
                <a href="{{ url_for('google_auth') }}" class="btn btn-outline-danger social-signup-button">
                    <i class="fab fa-google"></i> Sign up with Google
                </a>
                <!-- More social login options can be added here -->
            </div>
            
            <div class="divider">
                <span class="divider-text">or</span>
            </div>
            
            <!-- Traditional Signup Form -->
            <form method="post" action="{{ url_for('signup') }}" id="signupForm">
                <div class="mb-3">
                    <label for="username" class="form-label">Username</label>
                    <input type="text" class="form-control" id="username" name="username" required>
                </div>
                <div class="mb-3">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" class="form-control" id="email" name="email" required>
                </div>
                <div class="mb-3">
                    <label for="name" class="form-label">Full Name</label>
                    <input type="text" class="form-control" id="name" name="name">
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" class="form-control" id="password" name="password" required
                           pattern=".{8,}" title="Password must be at least 8 characters">
                    <div class="password-requirements">
                        Password must be at least 8 characters long
                    </div>
                </div>
                <div class="mb-3">
                    <label for="confirm_password" class="form-label">Confirm Password</label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                    <label class="form-check-label" for="terms">
                        I agree to the <a href="#" class="text-decoration-none">Terms of Service</a> and <a href="#" class="text-decoration-none">Privacy Policy</a>
                    </label>
                </div>
                <button type="submit" class="btn btn-success w-100">Create Account</button>
            </form>
        </div>
        <div class="card-footer bg-light p-3 text-center">
            <p class="mb-0">Already have an account? <a href="{{ url_for('login') }}" class="text-decoration-none">Sign in</a></p>
        </div>
    </div>
</div>

<script>
document.getElementById('signupForm').addEventListener('submit', function(event) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (password !== confirmPassword) {
        event.preventDefault();
        alert('Passwords do not match!');
    }
});
</script>
{% endblock %}
