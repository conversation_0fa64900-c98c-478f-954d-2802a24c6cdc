{% extends "base.html" %}

{% block title %}{{ topic.title }} - EcoCycle Forum{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/forum.css') }}">
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="forum-container">
    <div class="breadcrumb">
        <a href="{{ url_for('forum.index') }}">Forum Home</a> &raquo;
        <a href="{{ url_for('forum.category', category_id=topic.category_id) }}">{{ category.name }}</a> &raquo;
        {{ topic.title }}
    </div>

    <div class="topic-header">
        <h1>{{ topic.title }}</h1>
        <div class="topic-meta">
            <span class="topic-views"><i class="fas fa-eye"></i> {{ topic.views }} views</span>
            <span class="topic-replies"><i class="fas fa-comments"></i> {{ pagination.total_count }} replies</span>
            <span class="topic-created"><i class="fas fa-calendar"></i> {{ topic.created_at|datetime }}</span>
        </div>
    </div>

    <!-- Original post -->
    <div class="post original-post" id="post-original">
        <div class="post-sidebar">
            <div class="user-avatar">
                <img src="{{ topic.author.profile_image or url_for('static', filename='img/default-avatar.png') }}" alt="{{ topic.author.username }}">
            </div>
            <div class="user-info">
                <div class="username">{{ topic.author.username }}</div>
                <div class="user-role">{{ topic.author.role|capitalize }}</div>
                <div class="post-count">Posts: {{ topic.author.post_count }}</div>
                <div class="join-date">Member since: {{ topic.author.joined_at|date }}</div>
            </div>
        </div>
        <div class="post-content">
            <div class="post-header">
                <span class="post-date" title="{{ topic.created_at }}">{{ topic.created_at|timeago }}</span>
                <div class="post-actions">
                    {% if current_user.is_authenticated and (current_user.id == topic.author_id or current_user.is_admin) %}
                    <button class="btn btn-sm btn-outline-secondary edit-topic-btn" data-topic-id="{{ topic.id }}">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    {% endif %}
                    {% if current_user.is_authenticated %}
                    <button class="btn btn-sm btn-outline-primary quote-btn" data-content="{{ topic.content|escape }}" data-author="{{ topic.author.username }}">
                        <i class="fas fa-quote-right"></i> Quote
                    </button>
                    {% endif %}
                </div>
            </div>
            <div class="post-body">
                <div class="post-text">{{ topic.content|safe }}</div>
                {% if topic.edited %}
                <div class="post-edited">
                    <small><em>Last edited: {{ topic.updated_at|timeago }}</em></small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Replies -->
    {% for post in posts %}
    <div class="post" id="post-{{ post.id }}">
        <div class="post-sidebar">
            <div class="user-avatar">
                <img src="{{ post.profile_image or url_for('static', filename='img/default-avatar.png') }}" alt="{{ post.username }}">
            </div>
            <div class="user-info">
                <div class="username">{{ post.username }}</div>
                <div class="user-role">{{ post.role|capitalize if post.role else 'Member' }}</div>
                <div class="post-count">Posts: {{ post.post_count|default(0) }}</div>
                <div class="join-date">Member since: {{ post.joined_at|date if post.joined_at else 'Unknown' }}</div>
            </div>
        </div>
        <div class="post-content">
            <div class="post-header">
                <span class="post-date" title="{{ post.created_at }}">{{ post.created_at|timeago }}</span>
                <div class="post-actions">
                    {% if current_user.is_authenticated %}
                    <button class="btn btn-sm btn-outline-success reaction-btn 
                                 {{ 'active' if post.user_has_liked else '' }}" 
                            data-post-id="{{ post.id }}" 
                            data-reaction="like">
                        <i class="fas fa-thumbs-up"></i>
                        <span class="reaction-count">{{ post.like_count }}</span>
                    </button>
                    {% endif %}
                    
                    {% if current_user.is_authenticated and (current_user.id == post.author_id or current_user.is_admin) %}
                    <button class="btn btn-sm btn-outline-secondary edit-post-btn" data-post-id="{{ post.id }}">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="btn btn-sm btn-outline-danger delete-post-btn" data-post-id="{{ post.id }}">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                    {% endif %}
                    
                    {% if current_user.is_authenticated %}
                    <button class="btn btn-sm btn-outline-primary quote-btn" data-content="{{ post.content|escape }}" data-author="{{ post.username }}">
                        <i class="fas fa-quote-right"></i> Quote
                    </button>
                    {% endif %}
                </div>
            </div>
            <div class="post-body">
                <div class="post-text">{{ post.content|safe }}</div>
                {% if post.edited %}
                <div class="post-edited">
                    <small><em>Last edited: {{ post.updated_at|timeago }}</em></small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    {% endfor %}

    <!-- Pagination -->
    {% if pagination.total_pages > 1 %}
    <div class="pagination-container">
        <nav aria-label="Posts pagination">
            <ul class="pagination">
                {% if pagination.page > 1 %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('forum.topic', topic_id=topic.id, page=pagination.page-1) }}">Previous</a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">Previous</span>
                </li>
                {% endif %}
                
                {% for p in range(1, pagination.total_pages + 1) %}
                <li class="page-item {% if p == pagination.page %}active{% endif %}">
                    <a class="page-link" href="{{ url_for('forum.topic', topic_id=topic.id, page=p) }}">{{ p }}</a>
                </li>
                {% endfor %}
                
                {% if pagination.page < pagination.total_pages %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('forum.topic', topic_id=topic.id, page=pagination.page+1) }}">Next</a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">Next</span>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}

    <!-- Reply form -->
    {% if current_user.is_authenticated %}
    <div class="reply-container">
        <h3>Post a Reply</h3>
        <form id="reply-form" data-topic-id="{{ topic.id }}">
            <div class="form-group">
                <div id="editor-container"></div>
                <textarea id="reply-content" name="content" style="display:none" required></textarea>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i> Post Reply
                </button>
            </div>
        </form>
    </div>
    {% else %}
    <div class="login-prompt">
        <p>You need to <a href="{{ url_for('auth.login', next=request.url) }}">sign in</a> to post a reply.</p>
    </div>
    {% endif %}

    <!-- Edit post modal -->
    <div class="modal fade" id="edit-post-modal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Post</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="edit-post-form">
                        <input type="hidden" id="edit-post-id">
                        <div class="form-group">
                            <div id="edit-editor-container"></div>
                            <textarea id="edit-content" name="content" style="display:none" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="save-edit-btn">Save Changes</button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
<script src="{{ url_for('static', filename='js/forum.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Quill editor for reply
        const quill = new Quill('#editor-container', {
            theme: 'snow',
            placeholder: 'Write your reply here...',
            modules: {
                toolbar: [
                    [{ 'header': [1, 2, 3, false] }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'color': [] }, { 'background': [] }],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    ['blockquote', 'code-block'],
                    ['link', 'image'],
                    ['clean']
                ]
            }
        });
        
        // Initialize Quill editor for editing
        const editQuill = new Quill('#edit-editor-container', {
            theme: 'snow',
            modules: {
                toolbar: [
                    [{ 'header': [1, 2, 3, false] }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'color': [] }, { 'background': [] }],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    ['blockquote', 'code-block'],
                    ['link', 'image'],
                    ['clean']
                ]
            }
        });
        
        // Handle reply form submission
        const replyForm = document.getElementById('reply-form');
        if (replyForm) {
            replyForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const content = quill.root.innerHTML;
                if (!content || content === '<p><br></p>') {
                    alert('Please enter a reply');
                    return;
                }
                
                document.getElementById('reply-content').value = content;
                const topicId = this.getAttribute('data-topic-id');
                
                fetch('/api/forum/post', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        topic_id: topicId,
                        content: content
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload the page to show the new post
                        window.location.reload();
                    } else {
                        alert('Error: ' + (data.error || 'Failed to post reply'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while posting your reply');
                });
            });
        }
        
        // Handle quote buttons
        document.querySelectorAll('.quote-btn').forEach(button => {
            button.addEventListener('click', function() {
                const content = this.getAttribute('data-content');
                const author = this.getAttribute('data-author');
                
                const quoteText = `<blockquote><p><strong>${author} wrote:</strong></p>${content}</blockquote><p><br></p>`;
                quill.clipboard.dangerouslyPasteHTML(0, quoteText);
                
                // Scroll to reply form
                document.querySelector('.reply-container').scrollIntoView({ behavior: 'smooth' });
                
                // Focus on editor
                quill.focus();
            });
        });
        
        // Handle reaction buttons
        document.querySelectorAll('.reaction-btn').forEach(button => {
            button.addEventListener('click', function() {
                const postId = this.getAttribute('data-post-id');
                const reactionType = this.getAttribute('data-reaction');
                const countElement = this.querySelector('.reaction-count');
                
                fetch('/api/forum/reaction', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        post_id: postId,
                        reaction_type: reactionType
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.action === 'added') {
                            this.classList.add('active');
                            countElement.textContent = parseInt(countElement.textContent) + 1;
                        } else {
                            this.classList.remove('active');
                            countElement.textContent = parseInt(countElement.textContent) - 1;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });
        });
        
        // Handle edit post buttons
        document.querySelectorAll('.edit-post-btn').forEach(button => {
            button.addEventListener('click', function() {
                const postId = this.getAttribute('data-post-id');
                document.getElementById('edit-post-id').value = postId;
                
                // Get current content
                const postElement = document.querySelector(`#post-${postId} .post-text`);
                const currentContent = postElement.innerHTML;
                
                // Set content in editor
                editQuill.clipboard.dangerouslyPasteHTML(0, currentContent);
                
                // Show modal
                $('#edit-post-modal').modal('show');
            });
        });
        
        // Handle save edit button
        document.getElementById('save-edit-btn').addEventListener('click', function() {
            const postId = document.getElementById('edit-post-id').value;
            const content = editQuill.root.innerHTML;
            
            if (!content || content === '<p><br></p>') {
                alert('Post cannot be empty');
                return;
            }
            
            fetch(`/api/forum/post/${postId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content: content
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the post content in the DOM
                    const postElement = document.querySelector(`#post-${postId} .post-text`);
                    postElement.innerHTML = content;
                    
                    // Add edited indicator if not already there
                    const postBody = document.querySelector(`#post-${postId} .post-body`);
                    if (!postBody.querySelector('.post-edited')) {
                        const editedDiv = document.createElement('div');
                        editedDiv.className = 'post-edited';
                        editedDiv.innerHTML = '<small><em>Last edited: just now</em></small>';
                        postBody.appendChild(editedDiv);
                    }
                    
                    // Close modal
                    $('#edit-post-modal').modal('hide');
                } else {
                    alert('Error: ' + (data.error || 'Failed to update post'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating your post');
            });
        });
        
        // Handle delete post buttons
        document.querySelectorAll('.delete-post-btn').forEach(button => {
            button.addEventListener('click', function() {
                if (confirm('Are you sure you want to delete this post? This action cannot be undone.')) {
                    const postId = this.getAttribute('data-post-id');
                    
                    fetch(`/api/forum/post/${postId}`, {
                        method: 'DELETE'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Remove the post from the DOM
                            document.querySelector(`#post-${postId}`).remove();
                        } else {
                            alert('Error: ' + (data.error || 'Failed to delete post'));
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while deleting your post');
                    });
                }
            });
        });
    });
</script>
{% endblock %}
