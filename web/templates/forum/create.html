{% extends "base.html" %}

{% block title %}Create New Topic - EcoCycle Forum{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/forum.css') }}">
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="forum-container">
    <div class="breadcrumb">
        <a href="{{ url_for('forum.index') }}">Forum Home</a> &raquo;
        {% if category %}
        <a href="{{ url_for('forum.category', category_id=category.id) }}">{{ category.name }}</a> &raquo;
        {% endif %}
        Create New Topic
    </div>

    <div class="create-topic-container">
        <h1>Create New Topic</h1>
        
        <form id="create-topic-form">
            <div class="form-group">
                <label for="title">Topic Title</label>
                <input type="text" class="form-control" id="title" name="title" required 
                       minlength="5" maxlength="100" placeholder="Enter a descriptive title">
                <small class="form-text text-muted">
                    A clear title helps others understand what your topic is about
                </small>
            </div>
            
            {% if not category %}
            <div class="form-group">
                <label for="category">Category</label>
                <select class="form-control" id="category" name="category_id" required>
                    <option value="">Select a category</option>
                    {% for cat in categories %}
                    <option value="{{ cat.id }}">{{ cat.name }}</option>
                    {% endfor %}
                </select>
            </div>
            {% else %}
            <input type="hidden" id="category" name="category_id" value="{{ category.id }}">
            {% endif %}
            
            <div class="form-group">
                <label for="editor-container">Content</label>
                <div id="editor-container"></div>
                <textarea id="content" name="content" style="display:none" required></textarea>
                <small class="form-text text-muted">
                    Share your thoughts, questions, or information in detail
                </small>
            </div>
            
            <div class="form-group">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="notify" name="notify" checked>
                    <label class="custom-control-label" for="notify">
                        Notify me of replies
                    </label>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                    Cancel
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i> Create Topic
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Quill editor
        const quill = new Quill('#editor-container', {
            theme: 'snow',
            placeholder: 'Write your topic content here...',
            modules: {
                toolbar: [
                    [{ 'header': [1, 2, 3, false] }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'color': [] }, { 'background': [] }],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    ['blockquote', 'code-block'],
                    ['link', 'image'],
                    ['clean']
                ]
            }
        });
        
        // Handle @mentions
        const Mention = Quill.import('formats/mention');
        // Configure mentions if needed
        
        // Handle form submission
        document.getElementById('create-topic-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Validate form
            const title = document.getElementById('title').value.trim();
            const categoryId = document.getElementById('category').value;
            const content = quill.root.innerHTML;
            
            if (!title) {
                alert('Please enter a title');
                return;
            }
            
            if (!categoryId) {
                alert('Please select a category');
                return;
            }
            
            if (!content || content === '<p><br></p>') {
                alert('Please enter some content');
                return;
            }
            
            // Set content in hidden textarea for form submission
            document.getElementById('content').value = content;
            
            // Submit form via AJAX
            fetch('/api/forum/topic', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    title: title,
                    category_id: categoryId,
                    content: content
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Redirect to the new topic
                    window.location.href = `/forum/topic/${data.topic_id}`;
                } else {
                    alert('Error: ' + (data.error || 'Failed to create topic'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while creating your topic');
            });
        });
        
        // Enable image uploads
        const fileInput = document.createElement('input');
        fileInput.setAttribute('type', 'file');
        fileInput.setAttribute('accept', 'image/*');
        fileInput.style.display = 'none';
        document.body.appendChild(fileInput);
        
        // Custom image handler
        const imageHandler = () => {
            fileInput.click();
            
            fileInput.onchange = () => {
                const file = fileInput.files[0];
                if (file) {
                    // Create a FormData object
                    const formData = new FormData();
                    formData.append('file', file);
                    
                    // Show a loading indicator
                    const range = quill.getSelection();
                    quill.insertText(range.index, 'Uploading image... ', 'bold');
                    
                    // Upload the image
                    fetch('/api/forum/upload', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(result => {
                        // Remove the loading text
                        quill.deleteText(range.index, 'Uploading image... '.length);
                        
                        // Insert the uploaded image
                        quill.insertEmbed(range.index, 'image', result.url);
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        // Remove the loading text
                        quill.deleteText(range.index, 'Uploading image... '.length);
                        quill.insertText(range.index, 'Failed to upload image. ', 'bold', 'red');
                    });
                }
            };
        };
        
        // Override default image upload handler
        const toolbar = quill.getModule('toolbar');
        toolbar.addHandler('image', imageHandler);
    });
</script>
{% endblock %}
