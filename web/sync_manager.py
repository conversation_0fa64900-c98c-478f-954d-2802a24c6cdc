"""
EcoCycle - Synchronization Manager
Handles synchronization between web and mobile platforms
"""
import os
import json
import time
import logging
import threading
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union

# Configure logger
logger = logging.getLogger(__name__)

class SyncManager:
    """Manages cross-platform data synchronization between web and mobile applications."""
    
    def __init__(self, data_dir: str, sync_interval: int = 300):
        """
        Initialize the sync manager.
        
        Args:
            data_dir: Directory for storing sync data
            sync_interval: Interval in seconds between auto-sync (default: 5 minutes)
        """
        self.data_dir = data_dir
        self.sync_interval = sync_interval
        self.sync_db_path = os.path.join(data_dir, 'sync.db')
        self.sync_lock = threading.RLock()
        self.last_sync_time = {}
        self.sync_threads = {}
        
        # Create sync directory if it doesn't exist
        os.makedirs(data_dir, exist_ok=True)
        
        # Initialize database
        self._init_db()
    
    def _init_db(self):
        """Initialize the sync database."""
        try:
            with sqlite3.connect(self.sync_db_path) as conn:
                cursor = conn.cursor()
                
                # Create sync_records table
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS sync_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    entity_type TEXT NOT NULL,
                    entity_id TEXT NOT NULL,
                    data TEXT NOT NULL,
                    timestamp INTEGER NOT NULL,
                    device_id TEXT NOT NULL,
                    sync_status TEXT NOT NULL,
                    last_modified INTEGER NOT NULL,
                    UNIQUE(user_id, entity_type, entity_id, device_id)
                )
                ''')
                
                # Create sync_conflicts table
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS sync_conflicts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    entity_type TEXT NOT NULL,
                    entity_id TEXT NOT NULL,
                    device_id_1 TEXT NOT NULL,
                    device_id_2 TEXT NOT NULL,
                    data_1 TEXT NOT NULL,
                    data_2 TEXT NOT NULL,
                    timestamp INTEGER NOT NULL,
                    resolution_status TEXT NOT NULL,
                    resolved_data TEXT,
                    resolved_timestamp INTEGER
                )
                ''')
                
                # Create sync_devices table
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS sync_devices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_id TEXT UNIQUE NOT NULL,
                    user_id TEXT NOT NULL,
                    device_type TEXT NOT NULL,
                    device_name TEXT,
                    last_sync INTEGER,
                    last_seen INTEGER,
                    sync_enabled INTEGER DEFAULT 1
                )
                ''')
                
                conn.commit()
                logger.info("Sync database initialized")
        except Exception as e:
            logger.error(f"Error initializing sync database: {e}")
            raise
    
    def register_device(self, user_id: str, device_id: str, device_type: str, device_name: str = None) -> bool:
        """
        Register a device for synchronization.
        
        Args:
            user_id: User identifier
            device_id: Unique device identifier
            device_type: Type of device (web, mobile, desktop)
            device_name: Human-readable device name
            
        Returns:
            Success status
        """
        try:
            with sqlite3.connect(self.sync_db_path) as conn:
                cursor = conn.cursor()
                current_time = int(time.time())
                
                cursor.execute('''
                INSERT OR REPLACE INTO sync_devices 
                (device_id, user_id, device_type, device_name, last_seen)
                VALUES (?, ?, ?, ?, ?)
                ''', (device_id, user_id, device_type, device_name, current_time))
                
                conn.commit()
                logger.info(f"Device '{device_id}' registered for user '{user_id}'")
                return True
        except Exception as e:
            logger.error(f"Error registering device: {e}")
            return False
    
    def upload_changes(self, user_id: str, device_id: str, changes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Upload changes from a device to the synchronization service.
        
        Args:
            user_id: User identifier
            device_id: Device identifier
            changes: List of changed entities with their data
            
        Returns:
            Result with success status and conflict information
        """
        result = {
            "success": True,
            "synced_entities": 0,
            "conflicts": []
        }
        
        with self.sync_lock:
            try:
                with sqlite3.connect(self.sync_db_path) as conn:
                    conn.row_factory = sqlite3.Row
                    cursor = conn.cursor()
                    current_time = int(time.time())
                    
                    # Update device's last seen time
                    cursor.execute('''
                    UPDATE sync_devices SET last_seen = ? WHERE device_id = ?
                    ''', (current_time, device_id))
                    
                    for change in changes:
                        entity_type = change.get("entity_type")
                        entity_id = change.get("entity_id")
                        data = json.dumps(change.get("data", {}))
                        last_modified = change.get("last_modified", current_time)
                        
                        # Check for conflicts
                        cursor.execute('''
                        SELECT * FROM sync_records 
                        WHERE user_id = ? AND entity_type = ? AND entity_id = ? AND device_id != ?
                        ORDER BY last_modified DESC LIMIT 1
                        ''', (user_id, entity_type, entity_id, device_id))
                        
                        existing_record = cursor.fetchone()
                        
                        if existing_record and existing_record['last_modified'] > last_modified:
                            # Conflict detected - newer version exists from another device
                            result["conflicts"].append({
                                "entity_type": entity_type,
                                "entity_id": entity_id,
                                "server_timestamp": existing_record['last_modified'],
                                "client_timestamp": last_modified
                            })
                            
                            # Record the conflict for later resolution
                            cursor.execute('''
                            INSERT INTO sync_conflicts
                            (user_id, entity_type, entity_id, device_id_1, device_id_2, 
                             data_1, data_2, timestamp, resolution_status)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                user_id, entity_type, entity_id, existing_record['device_id'], device_id,
                                existing_record['data'], data, current_time, 'UNRESOLVED'
                            ))
                            
                            continue
                        
                        # Store or update the record
                        cursor.execute('''
                        INSERT OR REPLACE INTO sync_records
                        (user_id, entity_type, entity_id, data, timestamp, device_id, sync_status, last_modified)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            user_id, entity_type, entity_id, data, current_time, device_id, 'SYNCED', last_modified
                        ))
                        
                        result["synced_entities"] += 1
                    
                    # Update last sync time for the device
                    cursor.execute('''
                    UPDATE sync_devices SET last_sync = ? WHERE device_id = ?
                    ''', (current_time, device_id))
                    
                    conn.commit()
                    
                    # Store last sync time in memory
                    self.last_sync_time[device_id] = current_time
                    
                    logger.info(f"Synced {result['synced_entities']} entities for user '{user_id}' from device '{device_id}'")
                    
                    if result["conflicts"]:
                        logger.warning(f"Detected {len(result['conflicts'])} conflicts during sync")
            except Exception as e:
                logger.error(f"Error during sync upload: {e}")
                result["success"] = False
                result["error"] = str(e)
        
        return result
    
    def download_changes(self, user_id: str, device_id: str, last_sync: int = 0) -> Dict[str, Any]:
        """
        Download changes from the synchronization service to a device.
        
        Args:
            user_id: User identifier
            device_id: Device identifier
            last_sync: Timestamp of last synchronization
            
        Returns:
            Result with success status and changed entities
        """
        result = {
            "success": True,
            "changes": [],
            "last_sync": int(time.time())
        }
        
        with self.sync_lock:
            try:
                with sqlite3.connect(self.sync_db_path) as conn:
                    conn.row_factory = sqlite3.Row
                    cursor = conn.cursor()
                    current_time = int(time.time())
                    
                    # Update device's last seen time
                    cursor.execute('''
                    UPDATE sync_devices SET last_seen = ? WHERE device_id = ?
                    ''', (current_time, device_id))
                    
                    # Get changes since last sync from other devices
                    cursor.execute('''
                    SELECT * FROM sync_records 
                    WHERE user_id = ? AND device_id != ? AND (last_modified > ? OR timestamp > ?)
                    ORDER BY entity_type, entity_id, last_modified
                    ''', (user_id, device_id, last_sync, last_sync))
                    
                    records = cursor.fetchall()
                    
                    # Group by entity to get only the latest version of each
                    latest_entities = {}
                    for record in records:
                        key = f"{record['entity_type']}:{record['entity_id']}"
                        
                        # Only store if it's newer or we don't have it yet
                        if key not in latest_entities or record['last_modified'] > latest_entities[key]['last_modified']:
                            latest_entities[key] = dict(record)
                    
                    # Convert to list of changes
                    for entity in latest_entities.values():
                        try:
                            entity_data = json.loads(entity['data'])
                            
                            result["changes"].append({
                                "entity_type": entity['entity_type'],
                                "entity_id": entity['entity_id'],
                                "data": entity_data,
                                "last_modified": entity['last_modified'],
                                "source_device": entity['device_id']
                            })
                        except json.JSONDecodeError:
                            logger.error(f"Error decoding JSON data for entity {entity['entity_type']}:{entity['entity_id']}")
                    
                    # Update last sync time for the device
                    cursor.execute('''
                    UPDATE sync_devices SET last_sync = ? WHERE device_id = ?
                    ''', (current_time, device_id))
                    
                    conn.commit()
                    
                    # Store last sync time in memory
                    self.last_sync_time[device_id] = current_time
                    result["last_sync"] = current_time
                    
                    logger.info(f"Downloaded {len(result['changes'])} changes for user '{user_id}' to device '{device_id}'")
            except Exception as e:
                logger.error(f"Error during sync download: {e}")
                result["success"] = False
                result["error"] = str(e)
        
        return result
    
    def resolve_conflict(self, conflict_id: int, resolution_data: Dict[str, Any], resolver_id: str) -> bool:
        """
        Resolve a synchronization conflict.
        
        Args:
            conflict_id: ID of the conflict
            resolution_data: Data to use for resolution
            resolver_id: ID of the user or device resolving the conflict
            
        Returns:
            Success status
        """
        try:
            with sqlite3.connect(self.sync_db_path) as conn:
                cursor = conn.cursor()
                current_time = int(time.time())
                
                # Get conflict details
                cursor.execute('SELECT * FROM sync_conflicts WHERE id = ?', (conflict_id,))
                conflict = cursor.fetchone()
                
                if not conflict:
                    logger.warning(f"Conflict {conflict_id} not found for resolution")
                    return False
                
                # Update conflict as resolved
                cursor.execute('''
                UPDATE sync_conflicts 
                SET resolution_status = ?, resolved_data = ?, resolved_timestamp = ?
                WHERE id = ?
                ''', (
                    'RESOLVED', json.dumps(resolution_data), current_time, conflict_id
                ))
                
                # Update sync record with resolved data
                cursor.execute('''
                INSERT OR REPLACE INTO sync_records
                (user_id, entity_type, entity_id, data, timestamp, device_id, sync_status, last_modified)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    conflict[1], conflict[2], conflict[3], json.dumps(resolution_data),
                    current_time, resolver_id, 'RESOLVED', current_time
                ))
                
                conn.commit()
                logger.info(f"Resolved conflict {conflict_id} for {conflict[2]}:{conflict[3]}")
                return True
        except Exception as e:
            logger.error(f"Error resolving conflict: {e}")
            return False
    
    def get_device_sync_status(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get synchronization status for all user devices.
        
        Args:
            user_id: User identifier
            
        Returns:
            List of device sync statuses
        """
        try:
            with sqlite3.connect(self.sync_db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                SELECT * FROM sync_devices WHERE user_id = ?
                ''', (user_id,))
                
                devices = cursor.fetchall()
                result = []
                
                for device in devices:
                    result.append({
                        "device_id": device['device_id'],
                        "device_type": device['device_type'],
                        "device_name": device['device_name'],
                        "last_sync": device['last_sync'],
                        "last_seen": device['last_seen'],
                        "sync_enabled": bool(device['sync_enabled']),
                        "status": "online" if device['last_seen'] > int(time.time()) - 300 else "offline"
                    })
                
                return result
        except Exception as e:
            logger.error(f"Error getting device sync status: {e}")
            return []
    
    def get_pending_conflicts(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get pending synchronization conflicts for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            List of pending conflicts
        """
        try:
            with sqlite3.connect(self.sync_db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                SELECT * FROM sync_conflicts 
                WHERE user_id = ? AND resolution_status = 'UNRESOLVED'
                ORDER BY timestamp DESC
                ''', (user_id,))
                
                conflicts = cursor.fetchall()
                result = []
                
                for conflict in conflicts:
                    try:
                        data_1 = json.loads(conflict['data_1'])
                        data_2 = json.loads(conflict['data_2'])
                        
                        # Get device names
                        cursor.execute('SELECT device_name, device_type FROM sync_devices WHERE device_id = ?', 
                                      (conflict['device_id_1'],))
                        device_1 = cursor.fetchone()
                        
                        cursor.execute('SELECT device_name, device_type FROM sync_devices WHERE device_id = ?', 
                                      (conflict['device_id_2'],))
                        device_2 = cursor.fetchone()
                        
                        result.append({
                            "conflict_id": conflict['id'],
                            "entity_type": conflict['entity_type'],
                            "entity_id": conflict['entity_id'],
                            "timestamp": conflict['timestamp'],
                            "device_1": {
                                "id": conflict['device_id_1'],
                                "name": device_1['device_name'] if device_1 else "Unknown Device",
                                "type": device_1['device_type'] if device_1 else "unknown"
                            },
                            "device_2": {
                                "id": conflict['device_id_2'],
                                "name": device_2['device_name'] if device_2 else "Unknown Device",
                                "type": device_2['device_type'] if device_2 else "unknown"
                            },
                            "data_1": data_1,
                            "data_2": data_2
                        })
                    except json.JSONDecodeError:
                        logger.error(f"Error decoding JSON data for conflict {conflict['id']}")
                
                return result
        except Exception as e:
            logger.error(f"Error getting pending conflicts: {e}")
            return []
    
    def start_auto_sync(self, user_id: str, device_id: str) -> bool:
        """
        Start automatic synchronization for a device.
        
        Args:
            user_id: User identifier
            device_id: Device identifier
            
        Returns:
            Success status
        """
        if f"{user_id}:{device_id}" in self.sync_threads:
            if self.sync_threads[f"{user_id}:{device_id}"].is_alive():
                logger.warning(f"Auto-sync already running for user '{user_id}' on device '{device_id}'")
                return False
        
        # Create a thread for automatic synchronization
        sync_thread = threading.Thread(
            target=self._auto_sync_worker,
            args=(user_id, device_id),
            daemon=True
        )
        
        self.sync_threads[f"{user_id}:{device_id}"] = sync_thread
        sync_thread.start()
        
        logger.info(f"Started auto-sync for user '{user_id}' on device '{device_id}'")
        return True
    
    def stop_auto_sync(self, user_id: str, device_id: str) -> bool:
        """
        Stop automatic synchronization for a device.
        
        Args:
            user_id: User identifier
            device_id: Device identifier
            
        Returns:
            Success status
        """
        thread_key = f"{user_id}:{device_id}"
        
        if thread_key in self.sync_threads:
            # We can't forcibly stop the thread, but we can mark it for stoppage
            # The thread will check this status and exit gracefully
            self.sync_threads[thread_key] = None
            logger.info(f"Marked auto-sync for stoppage for user '{user_id}' on device '{device_id}'")
            return True
        
        logger.warning(f"No auto-sync running for user '{user_id}' on device '{device_id}'")
        return False
    
    def _auto_sync_worker(self, user_id: str, device_id: str):
        """
        Worker function for automatic synchronization.
        
        Args:
            user_id: User identifier
            device_id: Device identifier
        """
        thread_key = f"{user_id}:{device_id}"
        
        while thread_key in self.sync_threads and self.sync_threads[thread_key]:
            try:
                # Perform synchronization
                logger.debug(f"Auto-sync cycle for user '{user_id}' on device '{device_id}'")
                
                # Download changes
                last_sync = self.last_sync_time.get(device_id, 0)
                download_result = self.download_changes(user_id, device_id, last_sync)
                
                if download_result["success"] and download_result["changes"]:
                    logger.info(f"Auto-sync downloaded {len(download_result['changes'])} changes")
                
                # Sleep for the specified interval
                time.sleep(self.sync_interval)
            except Exception as e:
                logger.error(f"Error in auto-sync worker: {e}")
                time.sleep(60)  # Sleep for a minute on error
        
        logger.info(f"Auto-sync worker stopped for user '{user_id}' on device '{device_id}'")
