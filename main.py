#!/usr/bin/env python3
"""
EcoCycle - Legacy Entry Point
This file is maintained for backward compatibility.
For new installations, use: python -m ecocycle
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """Legacy main entry point."""
    try:
        # Import from new structure
        from ecocycle import main as ecocycle_main
        return ecocycle_main()
    except ImportError as e:
        print(f"Error importing EcoCycle: {e}")
        print("Please ensure EcoCycle is properly installed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
