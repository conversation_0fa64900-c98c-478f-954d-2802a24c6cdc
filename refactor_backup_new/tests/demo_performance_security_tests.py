#!/usr/bin/env python3
"""
EcoCycle - Performance and Security Testing Demo
Demonstrates how to run the new performance and security testing features.
"""

import os
import sys

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

def demo_performance_tests():
    """Demonstrate performance testing capabilities."""
    print("="*60)
    print("PERFORMANCE TESTING DEMO")
    print("="*60)

    try:
        from tests.test_performance_comprehensive import TestPerformanceComprehensive

        print("✓ Performance testing module loaded successfully")

        # Create test instance
        test_instance = TestPerformanceComprehensive()
        test_instance.setUp()

        print("\n1. Load Testing with Multiple Users")
        print("-" * 40)
        try:
            test_instance.test_load_testing_multiple_users()
            print("✓ Load testing completed successfully")

            # Display results
            load_results = test_instance.performance_metrics.get('load_test_results', {})
            if load_results and isinstance(load_results, dict):
                print(f"   - Concurrent users: {load_results.get('concurrent_users', 'N/A')}")
                print(f"   - Success rate: {load_results.get('success_rate', 0):.2%}")
                print(f"   - Average session time: {load_results.get('avg_session_time', 0):.2f}s")
        except Exception as e:
            print(f"✗ Load testing failed: {e}")

        print("\n2. Memory Usage Monitoring")
        print("-" * 40)
        try:
            test_instance.test_memory_usage_monitoring()
            print("✓ Memory monitoring completed successfully")

            # Display results
            memory_results = test_instance.performance_metrics.get('memory_usage_results', {})
            if memory_results and isinstance(memory_results, dict):
                print(f"   - Peak memory: {memory_results.get('peak_memory_mb', 0):.2f} MB")
                print(f"   - Memory growth: {memory_results.get('memory_growth_mb', 0):.2f} MB")
        except Exception as e:
            print(f"✗ Memory monitoring failed: {e}")

        print("\n3. API Response Time Testing")
        print("-" * 40)
        try:
            test_instance.test_api_response_time_testing()
            print("✓ API response time testing completed successfully")

            # Display results
            api_results = test_instance.performance_metrics.get('api_response_times', {})
            if api_results and isinstance(api_results, dict):
                for endpoint, metrics in api_results.items():
                    print(f"   - {endpoint}: {metrics.get('avg_response_time', 0):.3f}s avg")
        except Exception as e:
            print(f"✗ API response time testing failed: {e}")

        test_instance.tearDown()

    except ImportError as e:
        print(f"✗ Performance testing module not available: {e}")
        print("  Install required packages: pip install psutil memory-profiler")

def demo_security_tests():
    """Demonstrate security testing capabilities."""
    print("\n" + "="*60)
    print("SECURITY TESTING DEMO")
    print("="*60)

    try:
        from tests.test_security_comprehensive import TestSecurityComprehensive

        print("✓ Security testing module loaded successfully")

        # Create test instance
        test_instance = TestSecurityComprehensive()
        test_instance.setUp()

        print("\n1. Authentication Penetration Testing")
        print("-" * 40)
        try:
            test_instance.test_authentication_penetration_testing()
            print("✓ Authentication penetration testing completed")

            # Display results
            auth_results = test_instance.security_results.get('authentication_tests', [])
            for result in auth_results:
                status = "✓" if result.get('security_passed', False) else "✗"
                print(f"   {status} {result.get('test_name', 'Unknown test')}")
        except Exception as e:
            print(f"✗ Authentication penetration testing failed: {e}")

        print("\n2. Input Validation Testing")
        print("-" * 40)
        try:
            test_instance.test_input_validation_testing()
            print("✓ Input validation testing completed")

            # Display results
            input_results = test_instance.security_results.get('input_validation_tests', [])
            for result in input_results:
                status = "✓" if result.get('security_passed', False) else "✗"
                print(f"   {status} {result.get('test_name', 'Unknown test')}")
        except Exception as e:
            print(f"✗ Input validation testing failed: {e}")

        print("\n3. Session Management Testing")
        print("-" * 40)
        try:
            test_instance.test_session_management_testing()
            print("✓ Session management testing completed")

            # Display results
            session_results = test_instance.security_results.get('session_management_tests', [])
            for result in session_results:
                status = "✓" if result.get('security_passed', False) else "✗"
                print(f"   {status} {result.get('test_name', 'Unknown test')}")
        except Exception as e:
            print(f"✗ Session management testing failed: {e}")

        test_instance.tearDown()

    except ImportError as e:
        print(f"✗ Security testing module not available: {e}")
        print("  Install required packages: pip install faker")

def demo_load_testing():
    """Demonstrate load testing capabilities."""
    print("\n" + "="*60)
    print("LOAD TESTING DEMO")
    print("="*60)

    try:
        from tests.test_load_testing import LoadTestRunner

        print("✓ Load testing module loaded successfully")

        # Create load test runner
        runner = LoadTestRunner()

        print("\n1. Basic Load Test")
        print("-" * 40)
        print("Running load test with 5 users for 30 seconds...")

        results = runner.run_load_test(
            host="http://localhost:5050",
            users=5,
            spawn_rate=1,
            run_time=30
        )

        if "error" not in results:
            print("✓ Load test completed successfully")
            print(f"   - Total requests: {results.get('total_requests', 0)}")
            print(f"   - Average response time: {results.get('average_response_time', 0):.2f}ms")
            print(f"   - Requests per second: {results.get('requests_per_second', 0):.2f}")
            print(f"   - Failure rate: {results.get('failure_rate', 0):.2%}")
        else:
            print(f"✗ Load test failed: {results['error']}")

        print("\n2. Generating Load Test Report")
        print("-" * 40)

        report_file = os.path.join(PROJECT_ROOT, 'tests', 'test_logs', 'demo_load_test_report.json')
        report = runner.generate_load_test_report(results, report_file)

        print(f"✓ Load test report generated: {report_file}")

        # Display performance analysis
        analysis = report.get('performance_analysis', {})
        if analysis:
            print(f"   - Performance grade: {analysis.get('performance_grade', 'Unknown')}")

            strengths = analysis.get('strengths', [])
            if strengths:
                print("   - Strengths:")
                for strength in strengths:
                    print(f"     • {strength}")

            bottlenecks = analysis.get('bottlenecks', [])
            if bottlenecks:
                print("   - Bottlenecks:")
                for bottleneck in bottlenecks:
                    print(f"     • {bottleneck}")

    except ImportError as e:
        print(f"✗ Load testing not available: {e}")
        print("  Install Locust: pip install locust")
        print("  Note: Load testing requires the web application to be running on localhost:5050")

def demo_comprehensive_runner():
    """Demonstrate the comprehensive test runner."""
    print("\n" + "="*60)
    print("COMPREHENSIVE TEST RUNNER DEMO")
    print("="*60)

    try:
        from tests.run_performance_security_tests import PerformanceSecurityTestRunner

        print("✓ Comprehensive test runner loaded successfully")

        # Create test runner
        runner = PerformanceSecurityTestRunner()

        print("\nRunning comprehensive performance and security tests...")
        print("(This may take a few minutes)")

        # Run tests with shorter duration for demo
        runner.run_all_tests(include_load_tests=False, load_test_duration=30)

        # Print summary
        runner.print_summary()

        # Save results
        output_file = runner.save_results()
        if output_file:
            print(f"\n✓ Detailed results saved to: {output_file}")

    except ImportError as e:
        print(f"✗ Comprehensive test runner not available: {e}")

def main():
    """Main demo function."""
    print("EcoCycle Performance and Security Testing Demo")
    print("=" * 60)
    print("This demo showcases the new testing capabilities:")
    print("• Performance Testing (Load, Memory, API Response Times)")
    print("• Security Testing (Authentication, Input Validation, Sessions)")
    print("• Load Testing (using Locust)")
    print("• Comprehensive Test Runner")
    print()

    # Check if we should run all demos or specific ones
    if len(sys.argv) > 1:
        demo_type = sys.argv[1].lower()
        if demo_type == "performance":
            demo_performance_tests()
        elif demo_type == "security":
            demo_security_tests()
        elif demo_type == "load":
            demo_load_testing()
        elif demo_type == "comprehensive":
            demo_comprehensive_runner()
        else:
            print(f"Unknown demo type: {demo_type}")
            print("Available options: performance, security, load, comprehensive")
    else:
        # Run all demos
        demo_performance_tests()
        demo_security_tests()
        demo_load_testing()
        demo_comprehensive_runner()

    print("\n" + "="*60)
    print("DEMO COMPLETED")
    print("="*60)
    print("To run specific demos:")
    print("  python tests/demo_performance_security_tests.py performance")
    print("  python tests/demo_performance_security_tests.py security")
    print("  python tests/demo_performance_security_tests.py load")
    print("  python tests/demo_performance_security_tests.py comprehensive")
    print()
    print("To run the full test suite:")
    print("  python tests/run_performance_security_tests.py")
    print("  python tests/run_comprehensive_tests.py")

if __name__ == "__main__":
    main()
