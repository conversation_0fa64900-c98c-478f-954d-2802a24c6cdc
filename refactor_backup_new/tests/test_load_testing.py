#!/usr/bin/env python3
"""
EcoCycle - Load Testing Module
Uses Locust for comprehensive load testing of the web application.
"""

import os
import sys
import time
import json
import random
from datetime import datetime

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

try:
    from locust import HttpUser, task, between, events
    from locust.env import Environment
    from locust.stats import stats_printer, stats_history
    from locust.log import setup_logging
    LOCUST_AVAILABLE = True
except ImportError:
    print("Warning: Locust is not installed. Load testing will be limited.")
    LOCUST_AVAILABLE = False
    
    # Create mock classes for when Locust is not available
    class HttpUser:
        wait_time = None
        def __init__(self):
            pass
    
    def task(func):
        return func
    
    def between(min_wait, max_wait):
        return lambda: random.uniform(min_wait, max_wait)

class EcoCycleLoadTestUser(HttpUser):
    """Load test user for EcoCycle web application."""
    
    wait_time = between(1, 3)  # Wait 1-3 seconds between requests
    
    def on_start(self):
        """Called when a user starts."""
        self.username = f"load_test_user_{random.randint(1000, 9999)}"
        self.password = "LoadTestPassword123!"
        self.user_data = {}
        
        # Attempt to login or register
        self.login_or_register()
    
    def login_or_register(self):
        """Login or register a test user."""
        try:
            # Try to login first
            login_response = self.client.post("/login", data={
                "username": self.username,
                "password": self.password
            }, catch_response=True)
            
            if login_response.status_code == 200:
                # Login successful
                self.user_data = {"logged_in": True}
            else:
                # Try to register
                register_response = self.client.post("/register", data={
                    "username": self.username,
                    "password": self.password,
                    "email": f"{self.username}@loadtest.com",
                    "name": f"Load Test User {self.username}"
                }, catch_response=True)
                
                if register_response.status_code == 200:
                    self.user_data = {"logged_in": True}
                else:
                    self.user_data = {"logged_in": False}
                    
        except Exception as e:
            print(f"Login/Register error: {e}")
            self.user_data = {"logged_in": False}
    
    @task(3)
    def view_dashboard(self):
        """View the main dashboard."""
        with self.client.get("/", catch_response=True) as response:
            if response.status_code != 200:
                response.failure(f"Dashboard failed with status {response.status_code}")
    
    @task(2)
    def view_statistics(self):
        """View user statistics."""
        with self.client.get("/api/statistics", catch_response=True) as response:
            if response.status_code not in [200, 401]:  # 401 is acceptable if not logged in
                response.failure(f"Statistics failed with status {response.status_code}")
    
    @task(2)
    def log_trip(self):
        """Log a cycling trip."""
        trip_data = {
            "date": datetime.now().isoformat(),
            "distance": round(random.uniform(1.0, 50.0), 2),
            "duration": round(random.uniform(5.0, 180.0), 2),
            "co2_saved": round(random.uniform(0.1, 10.0), 2),
            "calories": random.randint(50, 1000)
        }
        
        with self.client.post("/api/trips", 
                            json=trip_data,
                            headers={"Content-Type": "application/json"},
                            catch_response=True) as response:
            if response.status_code not in [200, 201, 401]:
                response.failure(f"Trip logging failed with status {response.status_code}")
    
    @task(1)
    def view_routes(self):
        """View saved routes."""
        with self.client.get("/api/routes", catch_response=True) as response:
            if response.status_code not in [200, 401]:
                response.failure(f"Routes view failed with status {response.status_code}")
    
    @task(1)
    def create_route(self):
        """Create a new route."""
        route_data = {
            "name": f"Load Test Route {random.randint(1, 1000)}",
            "start_location": f"Start Location {random.randint(1, 100)}",
            "end_location": f"End Location {random.randint(1, 100)}",
            "distance": round(random.uniform(1.0, 100.0), 2),
            "difficulty": random.choice(["easy", "medium", "hard"]),
            "description": "Load test route description"
        }
        
        with self.client.post("/api/routes",
                            json=route_data,
                            headers={"Content-Type": "application/json"},
                            catch_response=True) as response:
            if response.status_code not in [200, 201, 401]:
                response.failure(f"Route creation failed with status {response.status_code}")
    
    @task(1)
    def view_community(self):
        """View community page."""
        with self.client.get("/community", catch_response=True) as response:
            if response.status_code not in [200, 401]:
                response.failure(f"Community view failed with status {response.status_code}")
    
    @task(1)
    def upload_file(self):
        """Test file upload functionality."""
        # Create a small test file
        test_file_content = b"Load test file content " + str(random.randint(1, 1000)).encode()
        
        files = {
            'file': ('loadtest.txt', test_file_content, 'text/plain')
        }
        
        with self.client.post("/api/upload",
                            files=files,
                            catch_response=True) as response:
            if response.status_code not in [200, 201, 400, 401, 413]:  # Various acceptable responses
                response.failure(f"File upload failed with status {response.status_code}")

class LoadTestRunner:
    """Runner for load tests."""
    
    def __init__(self):
        self.results = {}
        
    def run_load_test(self, host="http://localhost:5050", users=10, spawn_rate=2, run_time=60):
        """Run a load test with specified parameters."""
        if not LOCUST_AVAILABLE:
            print("Locust is not available. Skipping load test.")
            return {"error": "Locust not available"}
        
        # Setup logging
        setup_logging("INFO", None)
        
        # Create environment
        env = Environment(user_classes=[EcoCycleLoadTestUser])
        env.create_local_runner()
        
        # Start test
        env.runner.start(users, spawn_rate=spawn_rate)
        
        # Run for specified time
        start_time = time.time()
        while time.time() - start_time < run_time:
            time.sleep(1)
        
        # Stop test
        env.runner.quit()
        
        # Collect results
        stats = env.runner.stats
        
        results = {
            "total_requests": stats.total.num_requests,
            "total_failures": stats.total.num_failures,
            "average_response_time": stats.total.avg_response_time,
            "min_response_time": stats.total.min_response_time,
            "max_response_time": stats.total.max_response_time,
            "requests_per_second": stats.total.current_rps,
            "failure_rate": stats.total.fail_ratio,
            "test_duration": run_time,
            "concurrent_users": users,
            "spawn_rate": spawn_rate
        }
        
        # Add detailed endpoint statistics
        endpoint_stats = {}
        for name, entry in stats.entries.items():
            if name != "Aggregated":
                endpoint_stats[name] = {
                    "requests": entry.num_requests,
                    "failures": entry.num_failures,
                    "avg_response_time": entry.avg_response_time,
                    "min_response_time": entry.min_response_time,
                    "max_response_time": entry.max_response_time
                }
        
        results["endpoint_stats"] = endpoint_stats
        
        return results
    
    def run_stress_test(self, host="http://localhost:5050", max_users=100, spawn_rate=5, run_time=300):
        """Run a stress test with gradually increasing load."""
        if not LOCUST_AVAILABLE:
            print("Locust is not available. Skipping stress test.")
            return {"error": "Locust not available"}
        
        stress_results = []
        
        # Test with increasing user loads
        user_loads = [10, 25, 50, 75, max_users]
        
        for users in user_loads:
            print(f"Running stress test with {users} users...")
            
            result = self.run_load_test(
                host=host,
                users=users,
                spawn_rate=spawn_rate,
                run_time=run_time // len(user_loads)  # Divide time among tests
            )
            
            result["user_load"] = users
            stress_results.append(result)
            
            # Brief pause between tests
            time.sleep(5)
        
        return {
            "stress_test_results": stress_results,
            "max_users_tested": max_users,
            "total_test_time": run_time
        }
    
    def generate_load_test_report(self, results, output_file="load_test_report.json"):
        """Generate a comprehensive load test report."""
        report = {
            "test_timestamp": datetime.now().isoformat(),
            "test_results": results,
            "performance_analysis": self._analyze_performance(results),
            "recommendations": self._generate_recommendations(results)
        }
        
        # Save report to file
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        return report
    
    def _analyze_performance(self, results):
        """Analyze performance metrics."""
        if "error" in results:
            return {"error": "No results to analyze"}
        
        analysis = {
            "performance_grade": "Unknown",
            "bottlenecks": [],
            "strengths": []
        }
        
        # Analyze response times
        avg_response_time = results.get("average_response_time", 0)
        if avg_response_time < 200:
            analysis["strengths"].append("Excellent response times")
            performance_score = 90
        elif avg_response_time < 500:
            analysis["strengths"].append("Good response times")
            performance_score = 75
        elif avg_response_time < 1000:
            analysis["bottlenecks"].append("Moderate response times")
            performance_score = 60
        else:
            analysis["bottlenecks"].append("Slow response times")
            performance_score = 40
        
        # Analyze failure rate
        failure_rate = results.get("failure_rate", 0)
        if failure_rate < 0.01:
            analysis["strengths"].append("Very low failure rate")
            performance_score += 10
        elif failure_rate < 0.05:
            analysis["strengths"].append("Low failure rate")
            performance_score += 5
        elif failure_rate < 0.1:
            analysis["bottlenecks"].append("Moderate failure rate")
            performance_score -= 10
        else:
            analysis["bottlenecks"].append("High failure rate")
            performance_score -= 20
        
        # Assign grade
        if performance_score >= 85:
            analysis["performance_grade"] = "A"
        elif performance_score >= 75:
            analysis["performance_grade"] = "B"
        elif performance_score >= 65:
            analysis["performance_grade"] = "C"
        elif performance_score >= 50:
            analysis["performance_grade"] = "D"
        else:
            analysis["performance_grade"] = "F"
        
        return analysis
    
    def _generate_recommendations(self, results):
        """Generate performance improvement recommendations."""
        if "error" in results:
            return ["Fix Locust installation to enable load testing"]
        
        recommendations = []
        
        avg_response_time = results.get("average_response_time", 0)
        failure_rate = results.get("failure_rate", 0)
        
        if avg_response_time > 1000:
            recommendations.append("Optimize database queries and add caching")
            recommendations.append("Consider implementing CDN for static assets")
        
        if failure_rate > 0.05:
            recommendations.append("Investigate and fix application errors")
            recommendations.append("Implement better error handling and retry mechanisms")
        
        if results.get("requests_per_second", 0) < 10:
            recommendations.append("Scale application horizontally with load balancers")
            recommendations.append("Optimize application code for better throughput")
        
        if not recommendations:
            recommendations.append("Performance is good - consider monitoring for regression")
        
        return recommendations

if __name__ == "__main__":
    # Example usage
    runner = LoadTestRunner()
    
    print("Starting load test...")
    results = runner.run_load_test(users=5, run_time=30)
    
    print("Generating report...")
    report = runner.generate_load_test_report(results)
    
    print(f"Load test completed. Results: {json.dumps(results, indent=2)}")
