#!/usr/bin/env python3
"""
Final Test Suite Runner for EcoCycle Backend
Runs all available tests and generates a comprehensive report.
"""

import os
import sys
import unittest
import time
import json
from datetime import datetime
from io import StringIO

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

class FinalTestRunner:
    """Final comprehensive test runner for EcoCycle."""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.results = {}
        self.test_modules = [
            'tests.test_working_functionality',
            'tests.test_basic_functionality',
            'tests.test_comprehensive_report'
        ]
        
    def run_test_module(self, module_name):
        """Run a specific test module and capture results."""
        try:
            # Capture test output
            test_output = StringIO()
            
            # Load and run the test module
            loader = unittest.TestLoader()
            suite = loader.loadTestsFromName(module_name)
            
            # Run tests with custom result collector
            runner = unittest.TextTestRunner(
                stream=test_output,
                verbosity=2,
                buffer=True
            )
            
            result = runner.run(suite)
            
            # Parse results
            return {
                'module': module_name,
                'tests_run': result.testsRun,
                'failures': len(result.failures),
                'errors': len(result.errors),
                'skipped': len(result.skipped) if hasattr(result, 'skipped') else 0,
                'success_rate': ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100) if result.testsRun > 0 else 0,
                'failure_details': [{'test': str(test), 'error': error} for test, error in result.failures],
                'error_details': [{'test': str(test), 'error': error} for test, error in result.errors],
                'output': test_output.getvalue()
            }
            
        except Exception as e:
            return {
                'module': module_name,
                'tests_run': 0,
                'failures': 0,
                'errors': 1,
                'skipped': 0,
                'success_rate': 0,
                'failure_details': [],
                'error_details': [{'test': module_name, 'error': str(e)}],
                'output': f"Failed to run module: {e}"
            }
    
    def run_all_tests(self):
        """Run all test modules."""
        print("🚀 Starting EcoCycle Final Test Suite...")
        print("=" * 60)
        
        self.start_time = time.time()
        
        for module_name in self.test_modules:
            print(f"\n📋 Running {module_name}...")
            result = self.run_test_module(module_name)
            self.results[module_name] = result
            
            # Print immediate feedback
            if result['tests_run'] > 0:
                passed = result['tests_run'] - result['failures'] - result['errors']
                print(f"   ✅ {passed}/{result['tests_run']} tests passed ({result['success_rate']:.1f}%)")
                if result['failures'] > 0:
                    print(f"   ❌ {result['failures']} failures")
                if result['errors'] > 0:
                    print(f"   🚨 {result['errors']} errors")
                if result['skipped'] > 0:
                    print(f"   ⏭️  {result['skipped']} skipped")
            else:
                print(f"   ⚠️  No tests found or module failed to load")
        
        self.end_time = time.time()
        
        return self.generate_final_report()
    
    def generate_final_report(self):
        """Generate the final comprehensive report."""
        total_time = self.end_time - self.start_time
        
        # Aggregate results
        total_tests = sum(r['tests_run'] for r in self.results.values())
        total_failures = sum(r['failures'] for r in self.results.values())
        total_errors = sum(r['errors'] for r in self.results.values())
        total_skipped = sum(r['skipped'] for r in self.results.values())
        total_passed = total_tests - total_failures - total_errors
        
        overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'execution_time': round(total_time, 2),
            'summary': {
                'total_tests': total_tests,
                'passed': total_passed,
                'failures': total_failures,
                'errors': total_errors,
                'skipped': total_skipped,
                'success_rate': round(overall_success_rate, 1)
            },
            'module_results': self.results,
            'recommendations': self.generate_recommendations()
        }
        
        return report
    
    def generate_recommendations(self):
        """Generate recommendations based on test results."""
        recommendations = []
        
        # Analyze results for recommendations
        total_tests = sum(r['tests_run'] for r in self.results.values())
        total_failures = sum(r['failures'] for r in self.results.values())
        total_errors = sum(r['errors'] for r in self.results.values())
        
        if total_failures > 0:
            recommendations.append({
                'priority': 'high',
                'category': 'test_failures',
                'title': f'Fix {total_failures} Test Failures',
                'description': 'Address failing tests to ensure application reliability'
            })
        
        if total_errors > 0:
            recommendations.append({
                'priority': 'high',
                'category': 'test_errors',
                'title': f'Resolve {total_errors} Test Errors',
                'description': 'Fix test setup and configuration issues'
            })
        
        # Module-specific recommendations
        for module_name, result in self.results.items():
            if result['success_rate'] < 80 and result['tests_run'] > 0:
                recommendations.append({
                    'priority': 'medium',
                    'category': 'module_improvement',
                    'title': f'Improve {module_name} Test Coverage',
                    'description': f'Success rate is {result["success_rate"]:.1f}%, needs improvement'
                })
        
        # General recommendations
        if total_tests < 50:
            recommendations.append({
                'priority': 'medium',
                'category': 'test_coverage',
                'title': 'Expand Test Coverage',
                'description': f'Only {total_tests} tests found, consider adding more comprehensive tests'
            })
        
        return recommendations
    
    def print_final_summary(self, report):
        """Print a comprehensive final summary."""
        print("\n" + "=" * 80)
        print("🎯 ECOCYCLE FINAL TEST SUITE RESULTS")
        print("=" * 80)
        
        summary = report['summary']
        
        # Overall results
        print(f"📊 OVERALL RESULTS:")
        print(f"   Total tests: {summary['total_tests']}")
        print(f"   Passed: {summary['passed']} ({summary['success_rate']:.1f}%)")
        print(f"   Failed: {summary['failures']}")
        print(f"   Errors: {summary['errors']}")
        print(f"   Skipped: {summary['skipped']}")
        print(f"   Execution Time: {report['execution_time']:.2f} seconds")
        
        # Success indicator
        if summary['success_rate'] >= 80:
            print(f"\n🎉 EXCELLENT! Success rate: {summary['success_rate']:.1f}%")
        elif summary['success_rate'] >= 60:
            print(f"\n✅ GOOD! Success rate: {summary['success_rate']:.1f}%")
        elif summary['success_rate'] >= 40:
            print(f"\n⚠️  NEEDS WORK! Success rate: {summary['success_rate']:.1f}%")
        else:
            print(f"\n❌ CRITICAL ISSUES! Success rate: {summary['success_rate']:.1f}%")
        
        # Module breakdown
        print(f"\n📋 MODULE BREAKDOWN:")
        for module_name, result in report['module_results'].items():
            module_short = module_name.split('.')[-1]
            if result['tests_run'] > 0:
                passed = result['tests_run'] - result['failures'] - result['errors']
                status = "✅" if result['success_rate'] >= 80 else "⚠️" if result['success_rate'] >= 50 else "❌"
                print(f"   {status} {module_short}: {passed}/{result['tests_run']} ({result['success_rate']:.1f}%)")
            else:
                print(f"   ⚪ {module_short}: No tests run")
        
        # Key issues
        all_failures = []
        all_errors = []
        for result in report['module_results'].values():
            all_failures.extend(result['failure_details'])
            all_errors.extend(result['error_details'])
        
        if all_failures or all_errors:
            print(f"\n🚨 KEY ISSUES TO ADDRESS:")
            
            # Show top failures
            if all_failures:
                print(f"   Failures ({len(all_failures)}):")
                for i, failure in enumerate(all_failures[:3], 1):
                    test_name = failure['test'].split('.')[-1] if '.' in failure['test'] else failure['test']
                    print(f"     {i}. {test_name}")
                if len(all_failures) > 3:
                    print(f"     ... and {len(all_failures) - 3} more")
            
            # Show top errors
            if all_errors:
                print(f"   Errors ({len(all_errors)}):")
                for i, error in enumerate(all_errors[:3], 1):
                    test_name = error['test'].split('.')[-1] if '.' in error['test'] else error['test']
                    print(f"     {i}. {test_name}")
                if len(all_errors) > 3:
                    print(f"     ... and {len(all_errors) - 3} more")
        
        # Recommendations
        recommendations = report['recommendations']
        if recommendations:
            print(f"\n💡 TOP RECOMMENDATIONS:")
            high_priority = [r for r in recommendations if r['priority'] == 'high']
            medium_priority = [r for r in recommendations if r['priority'] == 'medium']
            
            for i, rec in enumerate(high_priority[:3], 1):
                print(f"   🔥 {i}. {rec['title']}")
            
            for i, rec in enumerate(medium_priority[:2], len(high_priority) + 1):
                print(f"   📋 {i}. {rec['title']}")
        
        # Application health assessment
        print(f"\n🏥 APPLICATION HEALTH ASSESSMENT:")
        if summary['success_rate'] >= 80:
            print("   🟢 HEALTHY - Application is ready for production")
        elif summary['success_rate'] >= 60:
            print("   🟡 STABLE - Application is functional with minor issues")
        elif summary['success_rate'] >= 40:
            print("   🟠 UNSTABLE - Application needs significant fixes")
        else:
            print("   🔴 CRITICAL - Application requires immediate attention")
        
        print("\n" + "=" * 80)
        
        return summary['success_rate']
    
    def save_report(self, report):
        """Save the detailed report to a file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(PROJECT_ROOT, 'tests', 'test_logs', f'final_test_report_{timestamp}.json')
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"📄 Detailed report saved to: {report_file}")
        return report_file

def main():
    """Main function to run the final test suite."""
    runner = FinalTestRunner()
    
    try:
        # Run all tests
        report = runner.run_all_tests()
        
        # Print summary
        success_rate = runner.print_final_summary(report)
        
        # Save detailed report
        runner.save_report(report)
        
        # Determine exit code
        if success_rate >= 80:
            print("\n🎉 All tests passed successfully!")
            return 0
        elif success_rate >= 60:
            print("\n✅ tests mostly successful with minor issues.")
            return 0
        else:
            print("\n⚠️  Significant test failures detected.")
            return 1
            
    except KeyboardInterrupt:
        print("\n\n⏹️  Test run interrupted by user.")
        return 1
    except Exception as e:
        print(f"\n❌ Test run failed with error: {e}")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
