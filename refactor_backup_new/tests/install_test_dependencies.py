#!/usr/bin/env python3
"""
EcoCycle - Test Dependencies Installer
Installs required packages for performance and security testing.
"""

import os
import sys
import subprocess
import importlib

def check_package(package_name):
    """Check if a package is installed."""
    try:
        importlib.import_module(package_name)
        return True
    except ImportError:
        return False

def install_package(package_name):
    """Install a package using pip."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """Install test dependencies."""
    print("EcoCycle Test Dependencies Installer")
    print("=" * 40)
    
    # Core testing dependencies
    core_packages = [
        ("psutil", "psutil"),
        ("memory_profiler", "memory-profiler"),
        ("pytest", "pytest"),
        ("pytest_benchmark", "pytest-benchmark"),
        ("pytest_xdist", "pytest-xdist"),
        ("pytest_mock", "pytest-mock")
    ]
    
    # Optional testing dependencies
    optional_packages = [
        ("locust", "locust"),
        ("faker", "faker")
    ]
    
    print("Installing core testing dependencies...")
    core_success = 0
    for module_name, package_name in core_packages:
        if check_package(module_name):
            print(f"✓ {package_name} already installed")
            core_success += 1
        else:
            print(f"Installing {package_name}...")
            if install_package(package_name):
                print(f"✓ {package_name} installed successfully")
                core_success += 1
            else:
                print(f"✗ Failed to install {package_name}")
    
    print(f"\nCore packages: {core_success}/{len(core_packages)} installed")
    
    print("\nInstalling optional testing dependencies...")
    optional_success = 0
    for module_name, package_name in optional_packages:
        if check_package(module_name):
            print(f"✓ {package_name} already installed")
            optional_success += 1
        else:
            print(f"Installing {package_name}...")
            if install_package(package_name):
                print(f"✓ {package_name} installed successfully")
                optional_success += 1
            else:
                print(f"✗ Failed to install {package_name}")
    
    print(f"\nOptional packages: {optional_success}/{len(optional_packages)} installed")
    
    # Summary
    print("\n" + "=" * 40)
    print("INSTALLATION SUMMARY")
    print("=" * 40)
    
    if core_success == len(core_packages):
        print("✓ All core testing dependencies installed")
        print("  Performance and security testing available")
    else:
        print("✗ Some core dependencies missing")
        print("  Some tests may not work properly")
    
    if optional_success == len(optional_packages):
        print("✓ All optional dependencies installed")
        print("  Load testing and advanced features available")
    else:
        print("⚠ Some optional dependencies missing")
        print("  Load testing may not be available")
    
    print("\nNext steps:")
    print("1. Run demo: python tests/demo_performance_security_tests.py")
    print("2. Run tests: python tests/run_performance_security_tests.py")
    print("3. Check README: tests/README_PERFORMANCE_SECURITY.md")

if __name__ == "__main__":
    main()
