"""
EcoCycle - Shared Argument Parser Module
Centralized argument parsing for both main.py and cli.py to eliminate duplication.
"""
import argparse
from typing import Optional


class EcoCycleArgumentParser:
    """Centralized argument parser for EcoCycle application."""
    
    def __init__(self, include_advanced_commands: bool = False):
        """
        Initialize the argument parser.
        
        Args:
            include_advanced_commands: Whether to include advanced commands like verify-email, etc.
        """
        self.include_advanced_commands = include_advanced_commands
        self.parser = self._create_parser()
    
    def _create_parser(self) -> argparse.ArgumentParser:
        """Create and configure the argument parser."""
        parser = argparse.ArgumentParser(description='EcoCycle - Cycle into a greener tomorrow')
        
        # Add subparsers for commands
        subparsers = parser.add_subparsers(dest='command', help='command')
        
        # 'run' command (default)
        subparsers.add_parser('run', help='Run the main application')
        
        # 'log' command
        self._add_log_parser(subparsers)
        
        # 'stats' command
        self._add_stats_parser(subparsers)
        
        # 'weather' command
        self._add_weather_parser(subparsers)
        
        # 'routes' command (CLI only)
        if not self.include_advanced_commands:
            self._add_routes_parser(subparsers)
        
        # 'config' command
        self._add_config_parser(subparsers)
        
        # 'export' command
        self._add_export_parser(subparsers)
        
        # 'update' command
        self._add_update_parser(subparsers)
        
        # 'doctor' command
        self._add_doctor_parser(subparsers)
        
        # 'version' command
        subparsers.add_parser('version', help='Display version information')
        
        # Advanced commands (main.py only)
        if self.include_advanced_commands:
            self._add_advanced_commands(subparsers)
        
        # CLI-specific commands
        if not self.include_advanced_commands:
            self._add_cli_specific_commands(subparsers)
        
        return parser
    
    def _add_log_parser(self, subparsers):
        """Add log command parser."""
        log_parser = subparsers.add_parser('log', help='Log a cycling trip')
        log_parser.add_argument('--date', help='Trip date (YYYY-MM-DD, defaults to today)')
        log_parser.add_argument('--distance', type=float, help='Trip distance in kilometers')
        log_parser.add_argument('--duration', type=float, help='Trip duration in minutes')
        log_parser.add_argument('-y', '--yes', action='store_true', help='Automatically save without confirmation')
    
    def _add_stats_parser(self, subparsers):
        """Add stats command parser."""
        stats_parser = subparsers.add_parser('stats', help='View cycling statistics')
        
        if self.include_advanced_commands:
            # Main.py version with user selection for admins
            stats_parser.add_argument('--user', help='Username to view stats for (admin only)')
        else:
            # CLI version with summary and visualization options
            stats_parser.add_argument('--summary', action='store_true', help='Show only summary statistics')
            stats_parser.add_argument('--visualize', '-v', action='store_true', help='Open data visualization')
    
    def _add_weather_parser(self, subparsers):
        """Add weather command parser."""
        weather_parser = subparsers.add_parser('weather', help='Check weather for cycling')
        weather_parser.add_argument('--location', help='Location to check weather for')
    
    def _add_routes_parser(self, subparsers):
        """Add routes command parser (CLI only)."""
        routes_parser = subparsers.add_parser('routes', help='Manage cycling routes')
        routes_parser.add_argument('action', choices=['plan', 'list', 'impact'], help='Action to perform')
    
    def _add_config_parser(self, subparsers):
        """Add config command parser."""
        config_parser = subparsers.add_parser('config', help='Configure application settings')
        config_parser.add_argument('--set', nargs=2, metavar=('KEY', 'VALUE'), help='Set a configuration value')
        config_parser.add_argument('--get', metavar='KEY', help='Get a configuration value')
        config_parser.add_argument('--list', action='store_true', help='List all configuration values')
    
    def _add_export_parser(self, subparsers):
        """Add export command parser."""
        export_parser = subparsers.add_parser('export', help='Export cycling data')
        export_parser.add_argument('--format', choices=['csv', 'json', 'pdf'], default='csv', help='Export format')
        export_parser.add_argument('--output', help='Output file path')
    
    def _add_update_parser(self, subparsers):
        """Add update command parser."""
        update_parser = subparsers.add_parser('update', help='Update the application')
        if self.include_advanced_commands:
            update_parser.add_argument('--check', action='store_true', help='Check for updates')
    
    def _add_doctor_parser(self, subparsers):
        """Add doctor command parser."""
        doctor_parser = subparsers.add_parser('doctor', help='Run system diagnostics')
        doctor_parser.add_argument('--fix', action='store_true', help='Attempt to fix issues automatically')
    
    def _add_advanced_commands(self, subparsers):
        """Add advanced commands for main.py."""
        # 'verify-email' command
        verify_email_parser = subparsers.add_parser('verify-email', help='Verify email address')
        verify_email_parser.add_argument('--code', required=True, help='6-digit verification code')
        
        # 'reset-password' command
        reset_password_parser = subparsers.add_parser('reset-password', help='Reset password using verification code')
        reset_password_parser.add_argument('--username', required=True, help='Username or email address')
        reset_password_parser.add_argument('--code', required=True, help='6-digit verification code')
        reset_password_parser.add_argument('--new-password', required=True, help='New password')
        
        # 'recover-account' command
        recover_account_parser = subparsers.add_parser('recover-account', help='Recover account using verification code')
        recover_account_parser.add_argument('--email', required=True, help='Email address')
        recover_account_parser.add_argument('--code', required=True, help='6-digit verification code')
        
        # 'request-verification' command
        request_verification_parser = subparsers.add_parser('request-verification', help='Request a new verification code')
        request_verification_parser.add_argument('--type', required=True, choices=['email', 'password', 'recovery'], help='Type of verification code to request')
        request_verification_parser.add_argument('--username', required=True, help='Username or email address')
        
        # 'help' command
        help_parser = subparsers.add_parser('help', help='Show help information')
        help_parser.add_argument('topic', nargs='?', help='Help topic')
    
    def _add_cli_specific_commands(self, subparsers):
        """Add CLI-specific commands."""
        # 'social' command
        subparsers.add_parser('social', help='Manage social features')
        
        # 'notifications' command
        subparsers.add_parser('notifications', help='Manage notifications')
    
    def parse_args(self, args=None):
        """Parse command line arguments."""
        return self.parser.parse_args(args)
    
    def print_help(self):
        """Print help message."""
        self.parser.print_help()


def create_main_parser() -> EcoCycleArgumentParser:
    """Create argument parser for main.py with advanced commands."""
    return EcoCycleArgumentParser(include_advanced_commands=True)


def create_cli_parser() -> EcoCycleArgumentParser:
    """Create argument parser for cli.py with CLI-specific commands."""
    return EcoCycleArgumentParser(include_advanced_commands=False)
