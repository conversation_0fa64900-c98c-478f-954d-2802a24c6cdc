api:
  google_maps_key: AIzaSyCQgETF8ugicnaI3GMN4typji_ndMHAa7k
  openweathermap_key: ********************************
  strava_client_id: ''
  strava_client_secret: ''
app:
  auto_update: true
  check_dependencies: true
  debug_mode: false
  language: en
  log_backup_count: 5
  max_log_size: 10485760
  name: EcoCycle
  session_timeout: 3600
  theme: light
  version: 3.0.0
cache:
  enabled: true
  max_size: 104857600
  routes_cache_file: /Users/<USER>/PycharmProjects/eco/data/cache/routes_cache.json
  ttl: 3600
  weather_cache_file: /Users/<USER>/PycharmProjects/eco/data/cache/weather_cache.json
developer:
  audit_logging: true
  bypass_restrictions: true
  debug_level: DEBUG
  enabled: false
  password_hash: 71355207e45f936ef50e5d88fb9fa8892ae5159ceff5cb5fe96c36d0f448001c:4b6b53c6e394fde7ecdfca94dcc4be52e653f6976c2bd38600ccd1a4f83b5226
  session_timeout: 1800
  username: dev_admin
email:
  from_email: <EMAIL>
  smtp_password: qryehirutvplofdk
  smtp_port: 587
  smtp_server: smtp.gmail.com
  smtp_username: <EMAIL>
  verification_code_length: 6
  verification_code_ttl: 3600
  verification_required: true
environmental:
  calories_per_km: 50
  co2_per_km: 0.192
  fuel_efficiency: 0.08
  fuel_price: 1.5
  points_per_km: 10
  tree_co2_per_year: 25
features:
  achievements: true
  analytics: true
  challenges: true
  leaderboard: true
  notifications: true
  route_planning: true
  social_sharing: true
  weather_integration: true
logging:
  console_logging: true
  debug_log_file: /Users/<USER>/PycharmProjects/eco/Logs/debug.log
  error_log_file: /Users/<USER>/PycharmProjects/eco/Logs/error.log
  file_logging: true
  level: INFO
  log_file: /Users/<USER>/PycharmProjects/eco/Logs/ecocycle.log
  log_format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
notifications:
  achievement_notifications: true
  challenge_notifications: true
  email_notifications: true
  enabled: true
  leaderboard_notifications: true
  push_notifications: false
  weather_alerts: true
paths:
  backup_dir: /Users/<USER>/PycharmProjects/eco/database_backups
  cache_dir: /Users/<USER>/PycharmProjects/eco/data/cache
  data_dir: /Users/<USER>/PycharmProjects/eco/data
  database_file: /Users/<USER>/PycharmProjects/eco/ecocycle.db
  debug_dir: /Users/<USER>/PycharmProjects/eco/data/debug
  google_auth_file: /Users/<USER>/PycharmProjects/eco/client_secrets.json
  log_dir: /Users/<USER>/PycharmProjects/eco/Logs
  project_root: /Users/<USER>/PycharmProjects/eco
  session_file: /Users/<USER>/PycharmProjects/eco/data/user/session.json
  user_data_dir: /Users/<USER>/PycharmProjects/eco/data/user
  users_file: /Users/<USER>/PycharmProjects/eco/data/user/users.json
security:
  jwt_expiration: 86400
  jwt_secret_key: default-jwt-secret
  password_min_length: 8
  password_require_lowercase: true
  password_require_number: true
  password_require_special: true
  password_require_uppercase: true
  session_secret_key: N\|I%UD3%@Ec03y373@
ui:
  accent_color: '#4CAF50'
  animations_enabled: true
  compact_mode: false
  font_size: medium
  show_welcome_screen: true
  sidebar_visible: true
  theme: light
