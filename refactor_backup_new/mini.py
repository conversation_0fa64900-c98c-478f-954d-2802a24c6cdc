#!/usr/bin/env python3
"""
EcoCycle - Minimal Entry Point
This minimal script just imports and calls the main application.
"""
import sys
import core.dependency.dependency_manager  # Import dependency_manager

def main():
    """
    Simple entry point that runs the EcoCycle application.
    Will pass any command line arguments to the main app.
    """
    try:
        # Ensure core dependencies are installed
        dependency_manager.ensure_feature('core')

        # Try to import the main module
        import main
        # Run the main function
        sys.exit(main.main())
    except ImportError as e:
        print(f"Error importing main module: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error running EcoCycle: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
