/* EcoCycle Web Dashboard Styles - Modern UI Inspired by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> */

:root {
    --primary-color: #22c55e;
    --primary-dark: #16a34a;
    --primary-light: #86efac;
    --secondary-color: #0ea5e9;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --success-color: #22c55e;
    --warning-color: #eab308;
    --danger-color: #ef4444;
    --info-color: #0ea5e9;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --border-radius-sm: 0.25rem;
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: var(--gray-50);
    color: var(--gray-800);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Navbar customization */
.navbar-brand {
    font-weight: 700;
    letter-spacing: 0.5px;
}

.navbar-dark.bg-success {
    background-color: var(--primary-color) !important;
    box-shadow: var(--box-shadow);
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

.nav-link:hover {
    color: var(--primary-light) !important;
}

/* Container to push footer to bottom */
.container {
    flex: 1 0 auto;
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
}

footer {
    flex-shrink: 0;
    margin-top: auto;
    background-color: var(--dark-color);
    color: var(--light-color);
    padding: 2rem 0;
}

footer a.text-light {
    transition: color 0.2s ease;
}

footer a.text-light:hover {
    color: var(--primary-light) !important;
    text-decoration: none;
}

/* Card styling */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--box-shadow-md);
    transform: translateY(-4px);
}

.card-header {
    border-bottom: none;
    background-color: var(--gray-100);
    font-weight: 600;
    padding: 1rem 1.25rem;
}

.card-body {
    padding: 1.25rem;
}

.card-footer {
    background-color: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    padding: 1rem 1.25rem;
}

/* Button styling */
.btn {
    font-weight: 500;
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    box-shadow: var(--box-shadow-sm);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    box-shadow: var(--box-shadow-sm);
}

.btn-outline-success {
    color: var(--success-color);
    border-color: var(--success-color);
}

.btn-outline-success:hover {
    background-color: var(--success-color);
    color: white;
    box-shadow: var(--box-shadow-sm);
}

.btn-sm {
    font-size: 0.875rem;
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius-sm);
}

/* Animated elements */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Dashboard specific styles */
.stat-card {
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-md);
}

.stat-card .card-body {
    padding: 1.5rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1.2;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--gray-600);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
}

.display-4 {
    font-weight: 700 !important;
    line-height: 1.1;
}

/* Activity feed */
.activity-item {
    position: relative;
    padding: 1.25rem;
    border-left: 4px solid var(--primary-color);
    margin-bottom: 1rem;
    background-color: white;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    box-shadow: var(--box-shadow-sm);
    transition: all 0.2s ease;
}

.activity-item:hover {
    box-shadow: var(--box-shadow);
    transform: translateX(3px);
}

.activity-timestamp {
    font-size: 0.75rem;
    color: var(--gray-500);
    font-weight: 500;
}

.eco-badge {
    background-color: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 2rem;
    font-size: 0.75rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

/* Route planner */
.route-map-container {
    height: 500px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow-md);
    position: relative;
}

.route-details {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-top: -80px;
    position: relative;
    z-index: 10;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--gray-200);
}

.route-controls {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 100;
    background-color: white;
    border-radius: var(--border-radius);
    padding: 0.75rem;
    box-shadow: var(--box-shadow);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.route-type-selector {
    display: flex;
    background-color: var(--gray-100);
    border-radius: var(--border-radius);
    padding: 0.25rem;
    margin-bottom: 1rem;
}

.route-type-option {
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.route-type-option.active {
    background-color: white;
    color: var(--primary-color);
    box-shadow: var(--box-shadow-sm);
}

.route-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-top: 1rem;
}

.route-stat-item {
    background-color: var(--gray-50);
    border-radius: var(--border-radius);
    padding: 1rem;
    text-align: center;
    transition: all 0.2s ease;
}

.route-stat-item:hover {
    background-color: white;
    box-shadow: var(--box-shadow-sm);
}

.route-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.route-stat-label {
    font-size: 0.75rem;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Media upload section */
.media-preview {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 10px;
}

.upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 25px;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background-color: #f1f8f2;
}

.upload-icon {
    font-size: 2rem;
    color: #adb5bd;
    margin-bottom: 10px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .stat-card {
        margin-bottom: 15px;
    }

    .chart-container {
        height: 250px;
    }

    .route-map-container {
        height: 300px;
    }
}

/* Notifications */
.notification-badge {
    position: relative;
    top: -10px;
    right: -5px;
    padding: 3px 6px;
    border-radius: 50%;
    background-color: var(--danger-color);
    color: white;
    font-size: 0.75rem;
}

.notification-item {
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
}

.notification-item:hover {
    background-color: #f5f5f5;
}

.notification-item.unread {
    background-color: #e8f4fc;
}

/* Offline indicator */
.offline-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 10px 15px;
    background-color: var(--danger-color);
    color: white;
    border-radius: 4px;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

/* Loading spinner */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Community forum styles */
.forum-topic {
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 8px;
    background-color: white;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    transition: transform 0.2s;
}

.forum-topic:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.topic-author {
    font-size: 0.85rem;
    color: #6c757d;
}

.topic-stats {
    font-size: 0.85rem;
    color: #6c757d;
}

/* Social login buttons */
.btn-social {
    position: relative;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-left: 44px;
    margin-bottom: 10px;
}

.btn-social > :first-child {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 32px;
    line-height: 34px;
    font-size: 1.6em;
    text-align: center;
    border-right: 1px solid rgba(0,0,0,0.2);
}

.btn-google {
    color: #fff;
    background-color: #dd4b39;
    border-color: rgba(0,0,0,0.2);
}

.btn-facebook {
    color: #fff;
    background-color: #3b5998;
    border-color: rgba(0,0,0,0.2);
}

/* Mobile widget */
.mobile-widget {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 10px rgba(0,0,0,0.3);
    z-index: 1000;
    transition: all 0.3s ease;
}

.mobile-widget:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 15px rgba(0,0,0,0.4);
}

.widget-icon {
    font-size: 1.5rem;
}

/* Augmented reality container */
.ar-container {
    position: relative;
    height: 400px;
    background-color: #000;
    color: white;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 20px;
}

.ar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 20px;
    background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, transparent 100%);
}

.ar-direction {
    font-size: 1.5rem;
    margin-bottom: 10px;
    font-weight: bold;
}

.ar-distance {
    font-size: 1.2rem;
    margin-bottom: 20px;
}

/* Animation for real-time tracking */
.pulse {
    border-radius: 50%;
    height: 14px;
    width: 14px;
    background: var(--success-color);
    display: inline-block;
    position: relative;
}

.pulse:after {
    content: "";
    display: block;
    position: absolute;
    border-radius: 50%;
    height: 30px;
    width: 30px;
    top: -8px;
    left: -8px;
    background-color: var(--success-color);
    opacity: 0;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(0.5);
        opacity: 0;
    }
    50% {
        opacity: 0.4;
    }
    100% {
        transform: scale(1.2);
        opacity: 0;
    }
}
