"""
EcoCycle - Location Services Module
Handles location-based services and recommendations for cycling routes
"""
import os
import json
import logging
import time
import math
import random
from typing import Dict, List, Optional, Any, Tuple, Union
import requests
from concurrent.futures import ThreadPoolExecutor
from dotenv import load_dotenv

# Add parent directory to path
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import EcoCycle modules
import config.config
import core.dependency.dependency_manager
from web.supabase_client import SupabaseClient

# Configure logger
logger = logging.getLogger(__name__)

# Ensure required packages are installed
required_packages = ['requests', 'python-dotenv', 'numpy', 'sklearn']
dependency_manager.ensure_packages(required_packages)

# Load environment variables
load_dotenv()

# Constants
OPENWEATHER_API_KEY = os.environ.get('OPENWEATHER_API_KEY', '')
MAPBOX_ACCESS_TOKEN = os.environ.get('MAPBOX_ACCESS_TOKEN', '')
MAX_POINTS_OF_INTEREST = 20

class LocationServices:
    """Provides location-based services and recommendations for cyclists."""
    
    def __init__(self):
        """Initialize location services with database and API connections."""
        self.db = SupabaseClient()
        self.last_weather_update = {}
        self.weather_cache = {}
        self.poi_cache = {}
        self.route_cache = {}
        
        # Create executor for parallel API requests
        self.executor = ThreadPoolExecutor(max_workers=5)
    
    def get_weather_forecast(self, lat: float, lng: float) -> Dict[str, Any]:
        """
        Get weather forecast for a location.
        
        Args:
            lat: Latitude
            lng: Longitude
            
        Returns:
            dict: Weather forecast data
        """
        cache_key = f"{lat:.3f},{lng:.3f}"
        current_time = time.time()
        
        # Check cache first (valid for 30 minutes)
        if cache_key in self.weather_cache and current_time - self.last_weather_update.get(cache_key, 0) < 1800:
            return self.weather_cache[cache_key]
        
        if not OPENWEATHER_API_KEY:
            logger.warning("OpenWeather API key not configured")
            return self._generate_mock_weather(lat, lng)
        
        try:
            # Get 5-day weather forecast with 3-hour intervals
            url = f"https://api.openweathermap.org/data/2.5/forecast"
            params = {
                "lat": lat,
                "lon": lng,
                "appid": OPENWEATHER_API_KEY,
                "units": "metric"
            }
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            # Process and format the data
            processed_data = {
                "current": data["list"][0],
                "hourly": data["list"][:8],  # Next 24 hours
                "daily": self._process_daily_forecast(data["list"]),
                "location": data["city"],
                "cycling_conditions": self._evaluate_cycling_conditions(data["list"])
            }
            
            # Update cache
            self.weather_cache[cache_key] = processed_data
            self.last_weather_update[cache_key] = current_time
            
            return processed_data
        except Exception as e:
            logger.error(f"Error fetching weather forecast: {e}")
            return self._generate_mock_weather(lat, lng)
    
    def _process_daily_forecast(self, hourly_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process hourly forecast into daily summaries."""
        daily = {}
        
        for item in hourly_data:
            date = item["dt_txt"].split(" ")[0]
            
            if date not in daily:
                daily[date] = {
                    "date": date,
                    "temp_min": item["main"]["temp_min"],
                    "temp_max": item["main"]["temp_max"],
                    "humidity": item["main"]["humidity"],
                    "weather": item["weather"][0],
                    "wind_speed": item["wind"]["speed"],
                    "precipitation": item.get("rain", {}).get("3h", 0) + item.get("snow", {}).get("3h", 0),
                    "readings": [item]
                }
            else:
                daily[date]["temp_min"] = min(daily[date]["temp_min"], item["main"]["temp_min"])
                daily[date]["temp_max"] = max(daily[date]["temp_max"], item["main"]["temp_max"])
                daily[date]["humidity"] = (daily[date]["humidity"] + item["main"]["humidity"]) / 2
                daily[date]["wind_speed"] = (daily[date]["wind_speed"] + item["wind"]["speed"]) / 2
                daily[date]["precipitation"] += item.get("rain", {}).get("3h", 0) + item.get("snow", {}).get("3h", 0)
                daily[date]["readings"].append(item)
        
        # Return as sorted list
        return sorted(daily.values(), key=lambda x: x["date"])
    
    def _evaluate_cycling_conditions(self, hourly_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Evaluate cycling conditions based on weather data."""
        now = time.time()
        next_12h = [h for h in hourly_data if h["dt"] <= now + 43200]  # Next 12 hours
        
        if not next_12h:
            return {"rating": "unknown", "message": "No forecast data available"}
        
        # Calculate cycling conditions rating (0-100)
        rating = 100
        
        # Check for precipitation
        precipitation = sum(h.get("rain", {}).get("3h", 0) + h.get("snow", {}).get("3h", 0) for h in next_12h)
        if precipitation > 5:
            rating -= 40  # Heavy rain/snow
        elif precipitation > 1:
            rating -= 20  # Light rain/snow
        
        # Check for temperature
        avg_temp = sum(h["main"]["temp"] for h in next_12h) / len(next_12h)
        if avg_temp < 5:
            rating -= 20  # Cold
        elif avg_temp < 10:
            rating -= 10  # Cool
        elif avg_temp > 30:
            rating -= 15  # Very hot
        
        # Check for wind
        avg_wind = sum(h["wind"]["speed"] for h in next_12h) / len(next_12h)
        if avg_wind > 10:
            rating -= 25  # Very windy
        elif avg_wind > 7:
            rating -= 15  # Moderately windy
        
        # Generate message
        message = "Perfect cycling conditions!"
        
        if rating < 40:
            message = "Poor cycling conditions. Consider postponing your ride."
        elif rating < 60:
            message = "Challenging conditions. Prepare accordingly if you decide to ride."
        elif rating < 80:
            message = "Good cycling conditions with some limitations."
        
        # Return conditions
        return {
            "rating": rating,
            "message": message,
            "precipitation": precipitation,
            "avg_temp": avg_temp,
            "avg_wind": avg_wind
        }
    
    def _generate_mock_weather(self, lat: float, lng: float) -> Dict[str, Any]:
        """Generate mock weather data when API is unavailable."""
        current_time = int(time.time())
        hourly = []
        
        for i in range(24):
            hour_time = current_time + i * 3600
            temp = 20 + random.uniform(-5, 5)
            
            hourly.append({
                "dt": hour_time,
                "dt_txt": time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime(hour_time)),
                "main": {
                    "temp": temp,
                    "temp_min": temp - 1,
                    "temp_max": temp + 1,
                    "humidity": random.randint(40, 80)
                },
                "weather": [
                    {
                        "id": 800,
                        "main": "Clear",
                        "description": "clear sky",
                        "icon": "01d"
                    }
                ],
                "wind": {
                    "speed": random.uniform(1, 5),
                    "deg": random.randint(0, 359)
                }
            })
        
        return {
            "current": hourly[0],
            "hourly": hourly[:8],
            "daily": self._process_daily_forecast(hourly),
            "location": {
                "name": "Unknown",
                "country": "Unknown",
                "coord": {"lat": lat, "lon": lng}
            },
            "cycling_conditions": {
                "rating": 80,
                "message": "Good cycling conditions (simulated data)",
                "precipitation": 0,
                "avg_temp": 20,
                "avg_wind": 3
            }
        }
    
    def find_points_of_interest(self, lat: float, lng: float, radius: int = 5000, poi_type: str = None) -> List[Dict[str, Any]]:
        """
        Find cycling-related points of interest near a location.
        
        Args:
            lat: Latitude
            lng: Longitude
            radius: Search radius in meters
            poi_type: Type of POI to search for
            
        Returns:
            list: List of POIs
        """
        cache_key = f"{lat:.3f},{lng:.3f},{radius},{poi_type}"
        
        # Check cache first (valid for 24 hours)
        if cache_key in self.poi_cache and time.time() - self.poi_cache[cache_key]["timestamp"] < 86400:
            return self.poi_cache[cache_key]["data"]
        
        # Try to fetch from Supabase first
        results = []
        
        # If the database is available, use spatial search
        if self.db.is_available():
            filters = {}
            if poi_type:
                filters["type"] = poi_type
                
            results = self.db.spatial_search(
                "points_of_interest",
                (lat, lng),
                radius
            )
        
        # If no results from database or database unavailable, try Mapbox
        if not results and MAPBOX_ACCESS_TOKEN:
            try:
                poi_types = {
                    "bike_shop": "bicycle_shop",
                    "repair": "bicycle_repair_station",
                    "parking": "bicycle_parking",
                    "water": "drinking_water",
                    "restroom": "restroom",
                    "cafe": "cafe"
                }
                
                mapbox_type = poi_types.get(poi_type, "")
                
                url = f"https://api.mapbox.com/geocoding/v5/mapbox.places/{mapbox_type}.json"
                params = {
                    "proximity": f"{lng},{lat}",
                    "radius": radius,
                    "limit": 10,
                    "access_token": MAPBOX_ACCESS_TOKEN
                }
                
                response = requests.get(url, params=params)
                response.raise_for_status()
                
                data = response.json()
                
                for feature in data.get("features", []):
                    results.append({
                        "id": feature["id"],
                        "name": feature["text"],
                        "type": poi_type or "unknown",
                        "location": {
                            "lat": feature["center"][1],
                            "lng": feature["center"][0]
                        },
                        "address": feature.get("place_name", ""),
                        "distance": self._calculate_distance(
                            lat, lng,
                            feature["center"][1], feature["center"][0]
                        )
                    })
            except Exception as e:
                logger.error(f"Error fetching POIs from Mapbox: {e}")
        
        # If still no results or APIs not available, generate mock data
        if not results:
            results = self._generate_mock_pois(lat, lng, radius, poi_type)
        
        # Sort by distance and limit results
        results = sorted(results, key=lambda x: x.get("distance", 0))[:MAX_POINTS_OF_INTEREST]
        
        # Update cache
        self.poi_cache[cache_key] = {
            "data": results,
            "timestamp": time.time()
        }
        
        return results
    
    def _generate_mock_pois(self, lat: float, lng: float, radius: int, poi_type: str = None) -> List[Dict[str, Any]]:
        """Generate mock POIs when APIs are unavailable."""
        results = []
        poi_types = ["bike_shop", "repair", "parking", "water", "restroom", "cafe"]
        
        # If specific type requested, just generate that type
        types_to_generate = [poi_type] if poi_type else poi_types
        
        for _ in range(min(10, MAX_POINTS_OF_INTEREST)):
            # Random angle and distance within radius
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(100, radius)
            
            # Convert to lat/lng offset (approximate)
            lat_offset = (distance / 111320) * math.cos(angle)
            lng_offset = distance / (111320 * math.cos(math.radians(lat)))
            
            poi_type = random.choice(types_to_generate)
            
            results.append({
                "id": f"mock-{len(results)}",
                "name": f"{poi_type.replace('_', ' ').title()} {len(results) + 1}",
                "type": poi_type,
                "location": {
                    "lat": lat + lat_offset,
                    "lng": lng + lng_offset
                },
                "address": "123 Example St, City",
                "distance": distance,
                "is_mock": True
            })
        
        return results
    
    def _calculate_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """Calculate distance between two points in meters using Haversine formula."""
        R = 6371000  # Earth radius in meters
        
        phi1 = math.radians(lat1)
        phi2 = math.radians(lat2)
        delta_phi = math.radians(lat2 - lat1)
        delta_lambda = math.radians(lng2 - lng1)
        
        a = (math.sin(delta_phi / 2) * math.sin(delta_phi / 2) +
             math.cos(phi1) * math.cos(phi2) *
             math.sin(delta_lambda / 2) * math.sin(delta_lambda / 2))
        
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c
    
    def get_route_recommendations(self, lat: float, lng: float, preferences: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Get personalized route recommendations based on location and preferences.
        
        Args:
            lat: Latitude
            lng: Longitude
            preferences: User preferences for routes
            
        Returns:
            list: Recommended routes
        """
        if not preferences:
            preferences = {}
            
        # Default preferences
        default_prefs = {
            "max_distance": 20,  # km
            "max_elevation": 300,  # meters
            "route_type": "any",  # road, trail, mixed
            "difficulty": "moderate"  # easy, moderate, challenging
        }
        
        # Merge with default preferences
        for key, value in default_prefs.items():
            if key not in preferences:
                preferences[key] = value
        
        # Try to get routes from database
        routes = []
        
        if self.db.is_available():
            # Query routes from Supabase
            try:
                # Use database for spatial search
                db_routes = self.db.spatial_search(
                    "routes",
                    (lat, lng),
                    preferences["max_distance"] * 1000  # Convert to meters
                )
                
                # Apply filters based on preferences
                for route in db_routes:
                    if self._route_matches_preferences(route, preferences):
                        routes.append(route)
            except Exception as e:
                logger.error(f"Error fetching routes from database: {e}")
        
        # If no routes found, generate mock routes
        if not routes:
            routes = self._generate_mock_routes(lat, lng, preferences)
        
        # Sort routes based on preference match and distance
        return self._sort_routes_by_relevance(routes, preferences)
    
    def _route_matches_preferences(self, route: Dict[str, Any], preferences: Dict[str, Any]) -> bool:
        """Check if a route matches user preferences."""
        # Check distance
        if route.get("distance", 0) > preferences["max_distance"]:
            return False
            
        # Check elevation
        if route.get("elevation_gain", 0) > preferences["max_elevation"]:
            return False
            
        # Check route type
        if preferences["route_type"] != "any" and route.get("type") != preferences["route_type"]:
            return False
            
        # Check difficulty
        if route.get("difficulty") != preferences["difficulty"] and preferences["difficulty"] != "any":
            return False
            
        return True
    
    def _generate_mock_routes(self, lat: float, lng: float, preferences: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate mock routes when database is unavailable."""
        routes = []
        route_types = ["road", "trail", "mixed"]
        difficulties = ["easy", "moderate", "challenging"]
        
        for i in range(5):
            # Random distance within preferences
            distance = random.uniform(3, preferences["max_distance"])
            
            # Random route type
            route_type = preferences["route_type"]
            if route_type == "any":
                route_type = random.choice(route_types)
                
            # Random difficulty
            difficulty = preferences["difficulty"]
            if difficulty == "any":
                difficulty = random.choice(difficulties)
                
            # Random elevation gain
            max_elevation = min(preferences["max_elevation"], distance * 50)  # Max 50m gain per km
            elevation_gain = random.uniform(distance * 10, max_elevation)
            
            # Create a mock route
            routes.append({
                "id": f"mock-route-{i}",
                "name": f"Mock Route {i+1}",
                "description": f"A beautiful {distance:.1f} km {route_type} route",
                "distance": distance,
                "elevation_gain": elevation_gain,
                "type": route_type,
                "difficulty": difficulty,
                "start_point": {
                    "lat": lat,
                    "lng": lng
                },
                "end_point": {
                    "lat": lat + random.uniform(-0.02, 0.02),
                    "lng": lng + random.uniform(-0.02, 0.02)
                },
                "rating": random.uniform(3.0, 5.0),
                "created_by": "system",
                "is_mock": True
            })
        
        return routes
    
    def _sort_routes_by_relevance(self, routes: List[Dict[str, Any]], preferences: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Sort routes by relevance to user preferences."""
        def calculate_relevance_score(route):
            score = 100
            
            # Distance score - prefer routes closer to desired distance
            distance_factor = abs(route.get("distance", 0) - preferences["max_distance"]) / preferences["max_distance"]
            score -= distance_factor * 25
            
            # Elevation score - prefer routes with less elevation gain
            elevation_factor = route.get("elevation_gain", 0) / preferences["max_elevation"]
            score -= elevation_factor * 25
            
            # Route type match
            if preferences["route_type"] != "any" and route.get("type") != preferences["route_type"]:
                score -= 15
                
            # Difficulty match
            if preferences["difficulty"] != "any" and route.get("difficulty") != preferences["difficulty"]:
                score -= 15
                
            # Rating boost
            rating = route.get("rating", 3.0)
            score += (rating - 3) * 10  # -10 to +20 point adjustment
            
            return score
        
        # Calculate score for each route
        for route in routes:
            route["relevance_score"] = calculate_relevance_score(route)
            
        # Sort by score (highest first)
        return sorted(routes, key=lambda x: x.get("relevance_score", 0), reverse=True)
    
    def share_location(self, user_id: str, lat: float, lng: float, expiry_time: int = 3600) -> Dict[str, Any]:
        """
        Share a user's location temporarily.
        
        Args:
            user_id: User identifier
            lat: Latitude
            lng: Longitude
            expiry_time: Time in seconds until the share expires
            
        Returns:
            dict: Sharing details with URL
        """
        if not self.db.is_available():
            logger.warning("Database not available for location sharing")
            return {
                "success": False,
                "error": "Database not available"
            }
            
        try:
            # Generate a unique share ID
            share_id = f"loc_{int(time.time())}_{user_id[:8]}"
            
            # Create share record
            share_data = {
                "id": share_id,
                "user_id": user_id,
                "latitude": lat,
                "longitude": lng,
                "created_at": int(time.time()),
                "expires_at": int(time.time()) + expiry_time,
                "active": True
            }
            
            # Insert into database
            result = self.db.insert("location_shares", share_data)
            
            if not result:
                return {
                    "success": False,
                    "error": "Failed to create location share"
                }
                
            # Return share details
            base_url = "https://ecocycle.app/location/share"
            share_url = f"{base_url}/{share_id}"
            
            return {
                "success": True,
                "share_id": share_id,
                "share_url": share_url,
                "expires_at": share_data["expires_at"]
            }
        except Exception as e:
            logger.error(f"Error sharing location: {e}")
            return {
                "success": False,
                "error": str(e)
            }
