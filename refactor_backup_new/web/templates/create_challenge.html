{% extends "base.html" %}

{% block title %}Create Custom Challenge{% endblock %}

{% block head %}
<style>
    /* Form Card Styling */
    .challenge-form-card {
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 2rem;
        box-shadow: 0 8px 20px rgba(0,0,0,0.05);
        border: none;
        transition: all 0.3s ease;
    }
    .challenge-form-card:hover {
        box-shadow: 0 12px 30px rgba(0,0,0,0.1);
    }

    /* Preview Card Styling */
    .challenge-preview {
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 2rem;
        box-shadow: 0 8px 20px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border: none;
        transform-style: preserve-3d;
        perspective: 1000px;
    }
    .challenge-preview:hover {
        transform: translateY(-8px) rotateY(5deg);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }

    /* Challenge Banner */
    .challenge-banner {
        height: 140px;
        position: relative;
        overflow: hidden;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }
    .challenge-banner img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }
    .challenge-preview:hover .challenge-banner img {
        transform: scale(1.05);
    }

    /* Challenge Badge */
    .challenge-badge {
        position: absolute;
        bottom: -30px;
        left: 20px;
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        border: 4px solid #28a745;
        transition: all 0.3s ease;
        z-index: 10;
    }
    .challenge-preview:hover .challenge-badge {
        transform: scale(1.1) rotate(5deg);
    }
    .challenge-badge i {
        font-size: 2.2rem;
        color: #28a745;
        transition: transform 0.3s ease;
    }
    .challenge-preview:hover .challenge-badge i {
        transform: rotate(-5deg);
    }

    /* Challenge Body */
    .challenge-body {
        padding-top: 35px;
        transition: all 0.3s ease;
    }

    /* Form Sections */
    .form-section {
        margin-bottom: 2.5rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid #e9ecef;
        position: relative;
    }
    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    .form-section-title {
        margin-bottom: 1.5rem;
        color: #28a745;
        font-weight: bold;
        display: flex;
        align-items: center;
    }
    .form-section-title i {
        margin-right: 0.75rem;
        background-color: rgba(40, 167, 69, 0.1);
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #28a745;
    }

    /* Progress Indicator */
    .form-progress {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2rem;
        position: relative;
    }
    .form-progress::before {
        content: '';
        position: absolute;
        top: 15px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #e9ecef;
        z-index: 0;
    }
    .progress-step {
        position: relative;
        z-index: 1;
        text-align: center;
        width: 30px;
        height: 30px;
        background-color: #e9ecef;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: #6c757d;
        transition: all 0.3s ease;
    }
    .progress-step.active {
        background-color: #28a745;
        color: white;
        box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2);
    }
    .progress-step.completed {
        background-color: #28a745;
        color: white;
    }
    .progress-step-label {
        position: absolute;
        top: 35px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 0.8rem;
        color: #6c757d;
        white-space: nowrap;
    }

    /* Form Controls */
    .form-control, .form-select {
        border-radius: 8px;
        padding: 0.6rem 1rem;
        border: 1px solid #ced4da;
        transition: all 0.2s ease;
    }
    .form-control:focus, .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25);
    }
    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #495057;
    }
    .form-text {
        color: #6c757d;
        font-size: 0.85rem;
        margin-top: 0.5rem;
    }

    /* Difficulty Selector */
    .difficulty-selector {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }
    .difficulty-option {
        flex: 1;
        text-align: center;
        padding: 1.25rem 1rem;
        border: 2px solid #dee2e6;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    .difficulty-option::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background-color: transparent;
        transition: all 0.3s ease;
    }
    .difficulty-option.difficulty-easy::before {
        background-color: #20c997;
    }
    .difficulty-option.difficulty-medium::before {
        background-color: #ffc107;
    }
    .difficulty-option.difficulty-hard::before {
        background-color: #dc3545;
    }
    .difficulty-option:hover {
        border-color: #28a745;
        background-color: rgba(40, 167, 69, 0.05);
        transform: translateY(-3px);
    }
    .difficulty-option.selected {
        border-color: #28a745;
        background-color: rgba(40, 167, 69, 0.05);
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        transform: translateY(-3px);
    }
    .difficulty-icon {
        font-size: 2.2rem;
        margin-bottom: 0.75rem;
        transition: transform 0.3s ease;
    }
    .difficulty-option:hover .difficulty-icon {
        transform: scale(1.1);
    }
    .difficulty-easy .difficulty-icon {
        color: #20c997;
    }
    .difficulty-medium .difficulty-icon {
        color: #ffc107;
    }
    .difficulty-hard .difficulty-icon {
        color: #dc3545;
    }

    /* Icon Selector */
    .icon-selector {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 1rem;
        margin-bottom: 1.5rem;
    }
    @media (max-width: 768px) {
        .icon-selector {
            grid-template-columns: repeat(4, 1fr);
        }
    }
    .icon-option {
        text-align: center;
        padding: 1rem;
        border: 2px solid #dee2e6;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .icon-option:hover {
        border-color: #28a745;
        background-color: rgba(40, 167, 69, 0.05);
        transform: translateY(-3px);
    }
    .icon-option.selected {
        border-color: #28a745;
        background-color: rgba(40, 167, 69, 0.05);
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        transform: translateY(-3px);
    }
    .icon-option i {
        font-size: 1.5rem;
        color: #28a745;
        transition: transform 0.3s ease;
    }
    .icon-option:hover i {
        transform: scale(1.2);
    }

    /* Preview Section */
    .preview-section {
        position: sticky;
        top: 2rem;
    }
    .preview-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
    }
    .preview-header i {
        margin-right: 0.75rem;
        font-size: 1.5rem;
        color: #28a745;
    }

    /* Reward Badges */
    .reward-badge {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin: 0 auto 0.75rem;
        transition: all 0.3s ease;
        border: 2px solid rgba(40, 167, 69, 0.2);
    }
    .challenge-preview:hover .reward-badge {
        transform: scale(1.05);
    }
    .reward-badge i {
        font-size: 1.8rem;
        color: #28a745;
    }

    /* Submit Button */
    .submit-btn {
        padding: 1rem 2rem;
        font-size: 1.1rem;
        border-radius: 50px;
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.2);
        transition: all 0.3s ease;
    }
    .submit-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    }

    /* Animations */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .fade-in {
        animation: fadeIn 0.5s ease-out forwards;
    }

    /* Form Validation Styling */
    .was-validated .form-control:invalid,
    .form-control.is-invalid {
        border-color: #dc3545;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }

    .was-validated .form-control:valid,
    .form-control.is-valid {
        border-color: #28a745;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }

    .invalid-feedback {
        display: none;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.875em;
        color: #dc3545;
    }

    .was-validated .form-control:invalid ~ .invalid-feedback,
    .form-control.is-invalid ~ .invalid-feedback {
        display: block;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1><i class="fas fa-plus-circle me-2"></i>Create Custom Challenge</h1>
            <p class="text-muted">Design your own personalized sustainability challenge to track your environmental impact and inspire others.</p>
        </div>
        <div class="col-md-4 d-flex justify-content-md-end align-items-center">
            <a href="{{ url_for('challenges') }}" class="btn btn-outline-success">
                <i class="fas fa-arrow-left me-2"></i>Back to Challenges
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card challenge-form-card">
                <div class="card-body p-4">
                    <!-- Form Progress Indicator -->
                    <div class="form-progress mb-4">
                        <div class="progress-step completed" data-step="1">
                            1
                            <span class="progress-step-label">Details</span>
                        </div>
                        <div class="progress-step active" data-step="2">
                            2
                            <span class="progress-step-label">Goals</span>
                        </div>
                        <div class="progress-step" data-step="3">
                            3
                            <span class="progress-step-label">Appearance</span>
                        </div>
                    </div>

                    <form id="create-challenge-form" class="needs-validation" novalidate>
                        <!-- Challenge Details Section -->
                        <div class="form-section">
                            <h4 class="form-section-title">
                                <i class="fas fa-info-circle"></i>Challenge Details
                            </h4>

                            <div class="mb-3">
                                <label for="challenge-title" class="form-label">Challenge Title</label>
                                <input type="text" class="form-control" id="challenge-title"
                                       placeholder="e.g., Zero Waste Week" required>
                                <div class="invalid-feedback">
                                    Please provide a title for your challenge.
                                </div>
                                <div class="form-text">
                                    Choose a clear, engaging title that describes your challenge.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="challenge-description" class="form-label">Challenge Description</label>
                                <textarea class="form-control" id="challenge-description" rows="4"
                                          placeholder="Describe what this challenge is about and how to complete it" required></textarea>
                                <div class="invalid-feedback">
                                    Please provide a description for your challenge.
                                </div>
                                <div class="form-text">
                                    Explain the purpose, benefits, and how to complete the challenge.
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="challenge-category" class="form-label">Category</label>
                                    <select class="form-select" id="challenge-category" required>
                                        <option value="" selected disabled>Select a category</option>
                                        <option value="waste">Waste Reduction</option>
                                        <option value="energy">Energy Conservation</option>
                                        <option value="transport">Sustainable Transport</option>
                                        <option value="food">Sustainable Food</option>
                                        <option value="water">Water Conservation</option>
                                        <option value="community">Community Action</option>
                                        <option value="other">Other</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a category.
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="challenge-visibility" class="form-label">Visibility</label>
                                    <select class="form-select" id="challenge-visibility" required>
                                        <option value="private" selected>Private (Only you)</option>
                                        <option value="friends">Friends Only</option>
                                        <option value="public">Public (Everyone)</option>
                                    </select>
                                    <div class="form-text">
                                        Control who can see and join your challenge.
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="challenge-tags" class="form-label">Tags (Optional)</label>
                                <input type="text" class="form-control" id="challenge-tags"
                                       placeholder="e.g., plastic-free, beginner, home">
                                <div class="form-text">
                                    Add comma-separated tags to help others find your challenge.
                                </div>
                            </div>
                        </div>

                        <!-- Challenge Goals Section -->
                        <div class="form-section">
                            <h4 class="form-section-title">
                                <i class="fas fa-bullseye"></i>Challenge Goals
                            </h4>

                            <div class="row mb-4">
                                <div class="col-md-6 mb-3">
                                    <label for="challenge-target" class="form-label">Target Value</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="challenge-target"
                                               placeholder="e.g., 5" min="1" required>
                                        <select class="form-select" id="challenge-unit" style="max-width: 150px;">
                                            <option value="days">Days</option>
                                            <option value="items">Items</option>
                                            <option value="kg">Kilograms</option>
                                            <option value="trips">Trips</option>
                                            <option value="actions">Actions</option>
                                            <option value="liters">Liters</option>
                                            <option value="hours">Hours</option>
                                        </select>
                                    </div>
                                    <div class="invalid-feedback">
                                        Please specify a target value.
                                    </div>
                                    <div class="form-text">The quantifiable goal for this challenge</div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="challenge-duration" class="form-label">Duration</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="challenge-duration"
                                               placeholder="e.g., 7" min="1" max="90" required>
                                        <span class="input-group-text">days</span>
                                    </div>
                                    <div class="invalid-feedback">
                                        Please specify a duration between 1 and 90 days.
                                    </div>
                                    <div class="form-text">How many days to complete this challenge</div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label class="form-label">Difficulty Level</label>
                                <div class="difficulty-selector">
                                    <div class="difficulty-option difficulty-easy" data-difficulty="easy">
                                        <div class="difficulty-icon">
                                            <i class="fas fa-seedling"></i>
                                        </div>
                                        <div>Easy</div>
                                        <small class="text-muted d-block mt-1">For beginners</small>
                                    </div>
                                    <div class="difficulty-option difficulty-medium selected" data-difficulty="medium">
                                        <div class="difficulty-icon">
                                            <i class="fas fa-leaf"></i>
                                        </div>
                                        <div>Medium</div>
                                        <small class="text-muted d-block mt-1">Some effort needed</small>
                                    </div>
                                    <div class="difficulty-option difficulty-hard" data-difficulty="hard">
                                        <div class="difficulty-icon">
                                            <i class="fas fa-tree"></i>
                                        </div>
                                        <div>Hard</div>
                                        <small class="text-muted d-block mt-1">Significant commitment</small>
                                    </div>
                                </div>
                                <input type="hidden" id="challenge-difficulty" value="medium" required>
                            </div>

                            <div class="mb-4">
                                <label for="challenge-milestones" class="form-label">Milestones (Optional)</label>
                                <div id="milestones-container">
                                    <div class="milestone-item mb-2 d-flex align-items-center">
                                        <div class="flex-grow-1 me-2">
                                            <input type="text" class="form-control" placeholder="e.g., Reduce waste by 25%">
                                        </div>
                                        <div style="width: 100px;" class="me-2">
                                            <input type="number" class="form-control" placeholder="25" min="1" max="100">
                                        </div>
                                        <div>
                                            <span class="input-group-text">%</span>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-outline-success btn-sm mt-2" id="add-milestone-btn">
                                    <i class="fas fa-plus me-1"></i> Add Milestone
                                </button>
                                <div class="form-text">
                                    Break down your challenge into smaller achievements
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="challenge-verification" class="form-label">Verification Method</label>
                                <select class="form-select" id="challenge-verification" required>
                                    <option value="honor">Honor System</option>
                                    <option value="photo">Photo Evidence</option>
                                    <option value="data">Data Tracking</option>
                                    <option value="buddy">Buddy Verification</option>
                                </select>
                                <div class="form-text">
                                    How will participants verify they've completed the challenge?
                                </div>
                            </div>
                        </div>

                        <!-- Challenge Appearance Section -->
                        <div class="form-section">
                            <h4 class="form-section-title">
                                <i class="fas fa-paint-brush"></i>Challenge Appearance
                            </h4>

                            <div class="mb-4">
                                <label class="form-label">Challenge Icon</label>
                                <div class="icon-selector">
                                    <div class="icon-option selected" data-icon="fa-leaf">
                                        <i class="fas fa-leaf"></i>
                                    </div>
                                    <div class="icon-option" data-icon="fa-recycle">
                                        <i class="fas fa-recycle"></i>
                                    </div>
                                    <div class="icon-option" data-icon="fa-bicycle">
                                        <i class="fas fa-bicycle"></i>
                                    </div>
                                    <div class="icon-option" data-icon="fa-lightbulb">
                                        <i class="fas fa-lightbulb"></i>
                                    </div>
                                    <div class="icon-option" data-icon="fa-tint">
                                        <i class="fas fa-tint"></i>
                                    </div>
                                    <div class="icon-option" data-icon="fa-carrot">
                                        <i class="fas fa-carrot"></i>
                                    </div>
                                    <div class="icon-option" data-icon="fa-seedling">
                                        <i class="fas fa-seedling"></i>
                                    </div>
                                    <div class="icon-option" data-icon="fa-solar-panel">
                                        <i class="fas fa-solar-panel"></i>
                                    </div>
                                    <div class="icon-option" data-icon="fa-tree">
                                        <i class="fas fa-tree"></i>
                                    </div>
                                    <div class="icon-option" data-icon="fa-wind">
                                        <i class="fas fa-wind"></i>
                                    </div>
                                    <div class="icon-option" data-icon="fa-mountain">
                                        <i class="fas fa-mountain"></i>
                                    </div>
                                    <div class="icon-option" data-icon="fa-globe-americas">
                                        <i class="fas fa-globe-americas"></i>
                                    </div>
                                </div>
                                <input type="hidden" id="challenge-icon" value="fa-leaf" required>
                            </div>

                            <div class="mb-4">
                                <label for="challenge-color" class="form-label">Theme Color</label>
                                <div class="d-flex gap-2 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="color-theme" id="color-green" value="green" checked>
                                        <label class="form-check-label" for="color-green">
                                            <span class="badge" style="background-color: #28a745; width: 20px; height: 20px; display: inline-block;"></span>
                                            Green
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="color-theme" id="color-blue" value="blue">
                                        <label class="form-check-label" for="color-blue">
                                            <span class="badge" style="background-color: #17a2b8; width: 20px; height: 20px; display: inline-block;"></span>
                                            Blue
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="color-theme" id="color-orange" value="orange">
                                        <label class="form-check-label" for="color-orange">
                                            <span class="badge" style="background-color: #fd7e14; width: 20px; height: 20px; display: inline-block;"></span>
                                            Orange
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="color-theme" id="color-purple" value="purple">
                                        <label class="form-check-label" for="color-purple">
                                            <span class="badge" style="background-color: #6f42c1; width: 20px; height: 20px; display: inline-block;"></span>
                                            Purple
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="challenge-banner" class="form-label">Banner Image (Optional)</label>
                                <div class="input-group mb-3">
                                    <input type="file" class="form-control" id="challenge-banner" accept="image/*">
                                    <button class="btn btn-outline-secondary" type="button" id="remove-banner-btn">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    Upload an image to make your challenge stand out (max 2MB)
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-4 mb-2">
                            <button type="button" class="btn btn-outline-secondary" id="save-draft-btn">
                                <i class="fas fa-save me-2"></i>Save Draft
                            </button>
                            <button type="submit" class="btn btn-success submit-btn">
                                <i class="fas fa-plus-circle me-2"></i>Create Challenge
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="preview-section">
                <div class="preview-header">
                    <i class="fas fa-eye"></i>
                    <h4 class="mb-0">Challenge Preview</h4>
                </div>

                <div class="card challenge-preview">
                    <div class="challenge-banner">
                        <img src="{{ url_for('static', filename='img/challenge-default.jpg') }}"
                             alt="Challenge Banner" id="preview-banner">
                        <div class="challenge-badge">
                            <i class="fas fa-leaf" id="preview-icon"></i>
                        </div>
                    </div>
                    <div class="card-body challenge-body">
                        <h5 class="card-title" id="preview-title">Challenge Title</h5>
                        <p class="card-text text-muted small" id="preview-description">Challenge description will appear here.</p>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span class="badge bg-success" id="preview-category">Category</span>
                            <span class="badge bg-warning text-dark" id="preview-difficulty">Medium</span>
                        </div>

                        <div class="d-flex flex-wrap gap-2 mb-3">
                            <span class="badge bg-light text-dark" id="preview-tag-1">sustainability</span>
                            <span class="badge bg-light text-dark" id="preview-tag-2">eco-friendly</span>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <small class="text-muted">
                                <i class="fas fa-bullseye me-1"></i>
                                <span id="preview-target">0 items</span>
                            </small>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                <span id="preview-duration">7 days</span>
                            </small>
                        </div>

                        <div class="progress mb-3" style="height: 8px;">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 0%"
                                 aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>

                        <h6 class="mb-2">Rewards</h6>
                        <div class="d-flex justify-content-around mb-3">
                            <div class="text-center">
                                <div class="reward-badge">
                                    <i class="fas fa-star"></i>
                                </div>
                                <small class="d-block" id="preview-points">50 points</small>
                            </div>
                            <div class="text-center">
                                <div class="reward-badge">
                                    <i class="fas fa-leaf"></i>
                                </div>
                                <small class="d-block" id="preview-co2">2.5 kg CO2</small>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button class="btn btn-sm btn-outline-success" disabled>
                                <i class="fas fa-users me-1"></i> Join Challenge
                            </button>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-lightbulb text-warning me-2"></i>Tips</h5>
                        <ul class="mb-0 ps-3">
                            <li class="mb-2">Be specific about what participants need to do</li>
                            <li class="mb-2">Set realistic goals that are achievable but challenging</li>
                            <li class="mb-2">Add milestones to help track progress</li>
                            <li>Choose a verification method that makes sense for your challenge</li>
                        </ul>
                    </div>
                </div>

                <div class="alert alert-info mt-4">
                    <i class="fas fa-info-circle me-2"></i>
                    <span id="visibility-message">This challenge will be visible only to you. You can share it with friends after creation.</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form elements
        const titleInput = document.getElementById('challenge-title');
        const descriptionInput = document.getElementById('challenge-description');
        const categorySelect = document.getElementById('challenge-category');
        const visibilitySelect = document.getElementById('challenge-visibility');
        const tagsInput = document.getElementById('challenge-tags');
        const targetInput = document.getElementById('challenge-target');
        const unitSelect = document.getElementById('challenge-unit');
        const durationInput = document.getElementById('challenge-duration');
        const difficultyOptions = document.querySelectorAll('.difficulty-option');
        const difficultyInput = document.getElementById('challenge-difficulty');
        const iconOptions = document.querySelectorAll('.icon-option');
        const iconInput = document.getElementById('challenge-icon');
        const colorOptions = document.querySelectorAll('input[name="color-theme"]');
        const bannerInput = document.getElementById('challenge-banner');
        const removeBannerBtn = document.getElementById('remove-banner-btn');
        const addMilestoneBtn = document.getElementById('add-milestone-btn');
        const milestonesContainer = document.getElementById('milestones-container');
        const saveDraftBtn = document.getElementById('save-draft-btn');
        const form = document.getElementById('create-challenge-form');
        const progressSteps = document.querySelectorAll('.progress-step');

        // Preview elements
        const previewTitle = document.getElementById('preview-title');
        const previewDescription = document.getElementById('preview-description');
        const previewCategory = document.getElementById('preview-category');
        const previewTarget = document.getElementById('preview-target');
        const previewDuration = document.getElementById('preview-duration');
        const previewDifficulty = document.getElementById('preview-difficulty');
        const previewIcon = document.getElementById('preview-icon');
        const previewPoints = document.getElementById('preview-points');
        const previewCO2 = document.getElementById('preview-co2');
        const previewBanner = document.getElementById('preview-banner');
        const previewTag1 = document.getElementById('preview-tag-1');
        const previewTag2 = document.getElementById('preview-tag-2');
        const visibilityMessage = document.getElementById('visibility-message');

        // Form validation
        form.addEventListener('submit', function(e) {
            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();

                // Show validation errors
                form.classList.add('was-validated');

                // Show toast notification
                showToast('Please fill in all required fields', 'danger');

                return false;
            }

            e.preventDefault();

            // Get form data
            const challengeData = getFormData();

            // Here you would normally send this data to the server
            console.log('Challenge data:', challengeData);

            // Show success message
            showToast('Challenge created successfully!', 'success');

            // Simulate redirect after delay
            setTimeout(() => {
                // window.location.href = '/challenges';
                showToast('Redirecting to challenges page...', 'info');
            }, 2000);
        });

        // Function to get all form data
        function getFormData() {
            // Get selected difficulty
            let selectedDifficulty = difficultyInput.value;

            // Get selected icon
            let selectedIcon = iconInput.value;

            // Get selected color theme
            let selectedColor = 'green';
            colorOptions.forEach(option => {
                if (option.checked) {
                    selectedColor = option.value;
                }
            });

            // Get tags
            const tags = tagsInput.value.split(',')
                .map(tag => tag.trim())
                .filter(tag => tag.length > 0);

            // Get milestones
            const milestones = [];
            document.querySelectorAll('.milestone-item').forEach(item => {
                const description = item.querySelector('input[type="text"]').value;
                const percentage = item.querySelector('input[type="number"]').value;

                if (description && percentage) {
                    milestones.push({
                        description,
                        percentage: parseInt(percentage)
                    });
                }
            });

            // Create challenge data object
            return {
                title: titleInput.value,
                description: descriptionInput.value,
                category: categorySelect.value,
                visibility: visibilitySelect.value,
                tags,
                target: parseInt(targetInput.value),
                unit: unitSelect.value,
                duration: parseInt(durationInput.value),
                difficulty: selectedDifficulty,
                milestones,
                verification: document.getElementById('challenge-verification').value,
                icon: selectedIcon,
                color: selectedColor,
                // banner would be handled by FormData in a real implementation
                hasBanner: bannerInput.files.length > 0
            };
        }

        // Update preview on input changes
        titleInput.addEventListener('input', function() {
            previewTitle.textContent = this.value || 'Challenge Title';
            validateInput(this);
        });

        descriptionInput.addEventListener('input', function() {
            previewDescription.textContent = this.value || 'Challenge description will appear here.';
            validateInput(this);
        });

        categorySelect.addEventListener('change', function() {
            const categoryText = this.options[this.selectedIndex].text;
            previewCategory.textContent = categoryText;
            validateInput(this);

            // Update preview category badge color based on category
            updateCategoryColor(this.value);
        });

        visibilitySelect.addEventListener('change', function() {
            const visibility = this.value;

            // Update visibility message in preview
            if (visibility === 'private') {
                visibilityMessage.textContent = 'This challenge will be visible only to you. You can share it with friends after creation.';
            } else if (visibility === 'friends') {
                visibilityMessage.textContent = 'This challenge will be visible to your friends. They can join and track progress.';
            } else {
                visibilityMessage.textContent = 'This challenge will be visible to everyone. Anyone can join and track progress.';
            }
        });

        tagsInput.addEventListener('input', function() {
            const tags = this.value.split(',')
                .map(tag => tag.trim())
                .filter(tag => tag.length > 0);

            // Update preview tags
            if (tags.length > 0) {
                previewTag1.textContent = tags[0];
                previewTag1.style.display = 'inline-flex';
            } else {
                previewTag1.style.display = 'none';
            }

            if (tags.length > 1) {
                previewTag2.textContent = tags[1];
                previewTag2.style.display = 'inline-flex';
            } else {
                previewTag2.style.display = 'none';
            }
        });

        function updateTargetPreview() {
            const target = targetInput.value || '0';
            const unit = unitSelect.value;
            previewTarget.textContent = `${target} ${unit}`;

            validateInput(targetInput);

            // Also update points and CO2 based on target, duration and difficulty
            updateRewards();
        }

        targetInput.addEventListener('input', updateTargetPreview);
        unitSelect.addEventListener('change', updateTargetPreview);

        durationInput.addEventListener('input', function() {
            const duration = this.value || '7';
            previewDuration.textContent = `${duration} days`;

            validateInput(this);

            // Update rewards when duration changes
            updateRewards();
        });

        // Difficulty selector
        difficultyOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Remove selected class from all options
                difficultyOptions.forEach(opt => opt.classList.remove('selected'));

                // Add selected class to clicked option
                this.classList.add('selected');

                // Update hidden input
                const difficulty = this.dataset.difficulty;
                difficultyInput.value = difficulty;

                // Update preview
                previewDifficulty.textContent = difficulty.charAt(0).toUpperCase() + difficulty.slice(1);

                // Update difficulty badge color
                updateDifficultyColor(difficulty);

                // Update rewards when difficulty changes
                updateRewards();

                // Add animation to show selection
                this.classList.add('fade-in');
                setTimeout(() => {
                    this.classList.remove('fade-in');
                }, 500);
            });
        });

        // Icon selector
        iconOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Remove selected class from all options
                iconOptions.forEach(opt => opt.classList.remove('selected'));

                // Add selected class to clicked option
                this.classList.add('selected');

                // Update hidden input
                const icon = this.dataset.icon;
                iconInput.value = icon;

                // Update preview
                previewIcon.className = `fas ${icon}`;

                // Add animation to show selection
                this.classList.add('fade-in');
                setTimeout(() => {
                    this.classList.remove('fade-in');
                }, 500);
            });
        });

        // Color theme selector
        colorOptions.forEach(option => {
            option.addEventListener('change', function() {
                if (this.checked) {
                    const color = this.value;
                    updateColorTheme(color);
                }
            });
        });

        // Banner image upload
        bannerInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const file = this.files[0];

                // Check file size (max 2MB)
                if (file.size > 2 * 1024 * 1024) {
                    showToast('Image size exceeds 2MB limit', 'danger');
                    this.value = '';
                    return;
                }

                const reader = new FileReader();

                reader.onload = function(e) {
                    previewBanner.src = e.target.result;
                };

                reader.readAsDataURL(file);
            }
        });

        // Remove banner button
        removeBannerBtn.addEventListener('click', function() {
            bannerInput.value = '';
            previewBanner.src = "{{ url_for('static', filename='img/challenge-default.jpg') }}";
        });

        // Add milestone button
        addMilestoneBtn.addEventListener('click', function() {
            const milestoneItem = document.createElement('div');
            milestoneItem.className = 'milestone-item mb-2 d-flex align-items-center';
            milestoneItem.innerHTML = `
                <div class="flex-grow-1 me-2">
                    <input type="text" class="form-control" placeholder="e.g., Reduce waste by 25%">
                </div>
                <div style="width: 100px;" class="me-2">
                    <input type="number" class="form-control" placeholder="25" min="1" max="100">
                </div>
                <div>
                    <button type="button" class="btn btn-outline-danger remove-milestone-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            milestonesContainer.appendChild(milestoneItem);

            // Add event listener to remove button
            milestoneItem.querySelector('.remove-milestone-btn').addEventListener('click', function() {
                milestoneItem.remove();
            });

            // Add animation
            milestoneItem.classList.add('fade-in');
        });

        // Save draft button
        saveDraftBtn.addEventListener('click', function() {
            const challengeData = getFormData();
            console.log('Saving draft:', challengeData);

            // Show success message
            showToast('Draft saved successfully!', 'success');
        });

        // Progress steps
        progressSteps.forEach(step => {
            step.addEventListener('click', function() {
                const stepNumber = parseInt(this.dataset.step);

                // Update active step
                progressSteps.forEach(s => {
                    const sNumber = parseInt(s.dataset.step);

                    if (sNumber < stepNumber) {
                        s.classList.remove('active');
                        s.classList.add('completed');
                    } else if (sNumber === stepNumber) {
                        s.classList.add('active');
                        s.classList.remove('completed');
                    } else {
                        s.classList.remove('active');
                        s.classList.remove('completed');
                    }
                });

                // Scroll to corresponding section
                const sections = document.querySelectorAll('.form-section');
                if (sections[stepNumber - 1]) {
                    sections[stepNumber - 1].scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // Calculate and update rewards based on challenge parameters
        function updateRewards() {
            const target = parseInt(targetInput.value) || 0;
            const duration = parseInt(durationInput.value) || 7;

            // Get selected difficulty
            let difficultyMultiplier = 1;
            difficultyOptions.forEach(option => {
                if (option.classList.contains('selected')) {
                    const difficulty = option.dataset.difficulty;
                    if (difficulty === 'easy') difficultyMultiplier = 0.8;
                    if (difficulty === 'medium') difficultyMultiplier = 1;
                    if (difficulty === 'hard') difficultyMultiplier = 1.5;
                }
            });

            // Calculate points and CO2 impact
            const points = Math.round(target * difficultyMultiplier * (duration / 7) * 10);
            const co2 = (target * difficultyMultiplier * (duration / 7) * 0.5).toFixed(1);

            // Update preview
            previewPoints.textContent = `${points} points`;
            previewCO2.textContent = `${co2} kg CO2`;
        }

        // Update category color in preview
        function updateCategoryColor(category) {
            let bgColor = '#28a745'; // Default green

            switch(category) {
                case 'waste':
                    bgColor = '#28a745'; // Green
                    break;
                case 'energy':
                    bgColor = '#ffc107'; // Yellow
                    break;
                case 'transport':
                    bgColor = '#17a2b8'; // Teal
                    break;
                case 'food':
                    bgColor = '#fd7e14'; // Orange
                    break;
                case 'water':
                    bgColor = '#007bff'; // Blue
                    break;
                case 'community':
                    bgColor = '#6f42c1'; // Purple
                    break;
            }

            previewCategory.style.backgroundColor = bgColor;
        }

        // Update difficulty color in preview
        function updateDifficultyColor(difficulty) {
            let bgColor = '#ffc107'; // Default medium (yellow)

            switch(difficulty) {
                case 'easy':
                    bgColor = '#20c997'; // Teal
                    break;
                case 'medium':
                    bgColor = '#ffc107'; // Yellow
                    break;
                case 'hard':
                    bgColor = '#dc3545'; // Red
                    break;
            }

            previewDifficulty.style.backgroundColor = bgColor;
        }

        // Update color theme in preview
        function updateColorTheme(color) {
            let badgeColor = '#28a745'; // Default green

            switch(color) {
                case 'green':
                    badgeColor = '#28a745';
                    break;
                case 'blue':
                    badgeColor = '#17a2b8';
                    break;
                case 'orange':
                    badgeColor = '#fd7e14';
                    break;
                case 'purple':
                    badgeColor = '#6f42c1';
                    break;
            }

            document.querySelector('.challenge-badge').style.borderColor = badgeColor;
            document.querySelector('.progress-bar').style.backgroundColor = badgeColor;
            document.querySelectorAll('.reward-badge i').forEach(icon => {
                icon.style.color = badgeColor;
            });
        }

        // Validate input and show/hide validation feedback
        function validateInput(input) {
            if (input.checkValidity()) {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
            } else {
                input.classList.add('is-invalid');
                input.classList.remove('is-valid');
            }
        }

        // Toast notification function
        function showToast(message, type = 'info') {
            // Create toast container if it doesn't exist
            let toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
                document.body.appendChild(toastContainer);
            }

            // Create toast element
            const toastId = 'toast-' + Date.now();
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type} border-0`;
            toast.id = toastId;
            toast.setAttribute('role', 'alert');
            toast.setAttribute('aria-live', 'assertive');
            toast.setAttribute('aria-atomic', 'true');

            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            `;

            toastContainer.appendChild(toast);

            // Initialize and show the toast
            const bsToast = new bootstrap.Toast(toast, {
                autohide: true,
                delay: 3000
            });
            bsToast.show();

            // Remove toast after it's hidden
            toast.addEventListener('hidden.bs.toast', function() {
                toast.remove();
            });
        }

        // Initialize preview with default values
        updateTargetPreview();
        updateRewards();
        updateCategoryColor('waste');
        updateDifficultyColor('medium');

        // Hide tags initially
        previewTag1.style.display = 'none';
        previewTag2.style.display = 'none';

        // Add animation to preview card
        document.querySelector('.challenge-preview').classList.add('fade-in');
    });
</script>
{% endblock %}
