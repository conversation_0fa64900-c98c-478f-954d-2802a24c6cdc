{% extends "base.html" %}

{% block title %}EcoCycle Community Forum{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/forum.css') }}">
{% endblock %}

{% block content %}
<div class="forum-container">
    <div class="forum-header">
        <h1>EcoCycle Community Forum</h1>
        <p>Connect with fellow cyclists, share tips, and discuss routes</p>
    </div>

    <div class="forum-search">
        <form action="{{ url_for('forum.search') }}" method="GET">
            <div class="input-group">
                <input type="text" name="q" class="form-control" placeholder="Search the forum..." required minlength="3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Search
                </button>
            </div>
        </form>
    </div>

    <div class="categories-container">
        <div class="categories-header">
            <h2>Discussion Categories</h2>
            {% if current_user.is_authenticated %}
            <a href="{{ url_for('forum.create_topic') }}" class="btn btn-success">
                <i class="fas fa-plus"></i> New Topic
            </a>
            {% endif %}
        </div>

        {% for category in categories %}
        <div class="category-card">
            <div class="category-icon">
                <i class="fas fa-{{ category.icon }}"></i>
            </div>
            <div class="category-details">
                <h3><a href="{{ url_for('forum.category', category_id=category.id) }}">{{ category.name }}</a></h3>
                <p>{{ category.description }}</p>
                <div class="category-stats">
                    <span><i class="fas fa-comments"></i> {{ category.topic_count }} Topics</span>
                    <span><i class="fas fa-reply"></i> {{ category.post_count }} Posts</span>
                </div>
            </div>
            {% if category.latest_topic %}
            <div class="latest-activity">
                <div class="latest-topic">
                    <small>Latest: <a href="{{ url_for('forum.topic', topic_id=category.latest_topic.id) }}">{{ category.latest_topic.title }}</a></small>
                    <small>by {{ category.latest_topic.author }} {{ category.latest_topic.created_at|timeago }}</small>
                </div>
            </div>
            {% endif %}
        </div>
        {% endfor %}
    </div>

    <div class="forum-stats">
        <h3>Forum Statistics</h3>
        <div class="stats-container">
            <div class="stat-item">
                <span class="stat-number">{{ stats.total_topics }}</span>
                <span class="stat-label">Topics</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{ stats.total_posts }}</span>
                <span class="stat-label">Posts</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{ stats.total_users }}</span>
                <span class="stat-label">Users</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Newest Member:</span>
                <span class="stat-value">{{ stats.newest_user.username }}</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/forum.js') }}"></script>
{% endblock %}
