"""
EcoCycle - API Endpoints Module
Handles API endpoints for mobile and web extensions.
"""
import os
import json
import time
import logging
import uuid
from typing import Dict, List, Optional, Any, Tuple, Union
from functools import wraps

# Flask imports
try:
    from flask import Blueprint, request, jsonify, current_app, g, session
except ImportError:
    # Handle case where <PERSON>lask is not installed
    print("Warning: Flask is not installed. Web functionality will be limited.")
    Blueprint = None

# Werkzeug imports
try:
    from werkzeug.utils import secure_filename
except ImportError:
    # Handle case where werkzeug is not installed
    print("Warning: Werkzeug is not installed. File upload functionality will be limited.")
    secure_filename = lambda x: x

# Add parent directory to path
import sys
import os
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import EcoCycle modules
import config.config as config
from auth.user_management.user_manager import UserManager
from web.sync_manager import SyncManager

# Configure logger
logger = logging.getLogger(__name__)

# Create Blueprint
api = Blueprint('api', __name__)

# Initialize shared components
user_manager = UserManager()
sync_manager = SyncManager(os.path.join(config.DATA_DIR, 'sync'))

# API authentication decorator
def require_auth(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        # Check if we're in a Flask session (web app)
        if 'username' in session:
            g.user = session['username']
            logger.info(f"User authenticated via session: {g.user}")
            return f(*args, **kwargs)

        # Check if user is already set in the request context
        if hasattr(g, 'user') and g.user:
            logger.info(f"User already authenticated: {g.user}")
            return f(*args, **kwargs)

        # Check for API authentication methods
        auth_header = request.headers.get('Authorization')
        api_key = request.args.get('api_key')

        # If no authentication provided, return error
        if not auth_header and not api_key:
            logger.warning("Authentication required but not provided")
            return jsonify({"error": "Authentication required"}), 401

        # Process token from header or query parameter
        token = None
        if auth_header:
            # Check Bearer token
            parts = auth_header.split()
            if len(parts) == 2 and parts[0].lower() == 'bearer':
                token = parts[1]
        else:
            # Use API key
            token = api_key

        if not token:
            logger.warning("Invalid authentication token format")
            return jsonify({"error": "Invalid authentication token"}), 401

        # For demo purposes, accept any token and use a default user
        # In a real app, you would validate the token properly
        g.user = "demo_user"
        logger.info(f"User authenticated via API token: {g.user}")
        return f(*args, **kwargs)

    return decorated

# Generate API key for a user
@api.route('/auth/token', methods=['POST'])
def generate_token():
    """Generate an API token for mobile/API authentication."""
    data = request.json
    username = data.get('username')
    password = data.get('password')
    device_id = data.get('device_id')
    device_name = data.get('device_name', 'Unknown Device')
    device_type = data.get('device_type', 'unknown')

    if not username or not password or not device_id:
        return jsonify({"error": "Missing required fields"}), 400

    # Authenticate user (simplified for API)
    # In a real implementation, you would properly authenticate the user
    if username not in user_manager.users:
        return jsonify({"error": "Invalid credentials"}), 401

    # Generate and store token
    try:
        token = user_manager.generate_api_token(username, device_id)

        # Register device with sync manager
        sync_manager.register_device(username, device_id, device_type, device_name)

        return jsonify({
            "token": token,
            "user_id": username,
            "expires_in": 60 * 60 * 24 * 30  # 30 days
        })
    except Exception as e:
        logger.error(f"Error generating token: {e}")
        return jsonify({"error": "Server error"}), 500

# User profile endpoint
@api.route('/user/profile', methods=['GET'])
@require_auth
def get_user_profile():
    """Get user profile information."""
    user_id = g.user

    try:
        user_data = user_manager.get_user_profile(user_id)
        if not user_data:
            return jsonify({"error": "User not found"}), 404

        # Remove sensitive fields
        if 'password_hash' in user_data:
            del user_data['password_hash']
        if 'salt' in user_data:
            del user_data['salt']

        return jsonify(user_data)
    except Exception as e:
        logger.error(f"Error getting user profile: {e}")
        return jsonify({"error": "Server error"}), 500

# Update user profile endpoint
@api.route('/user/profile', methods=['PUT'])
@require_auth
def update_user_profile():
    """Update user profile information."""
    user_id = g.user
    data = request.json

    try:
        # Ensure we're not updating sensitive fields
        safe_fields = ['name', 'email', 'preferences', 'notifications', 'profile_image']
        update_data = {k: v for k, v in data.items() if k in safe_fields}

        success = user_manager.update_user_profile(user_id, update_data)
        if not success:
            return jsonify({"error": "Failed to update profile"}), 500

        return jsonify({"success": True})
    except Exception as e:
        logger.error(f"Error updating user profile: {e}")
        return jsonify({"error": "Server error"}), 500

# Statistics endpoints
@api.route('/stats/<user_id>', methods=['GET'])
@require_auth
def get_user_stats(user_id):
    """Get user statistics."""
    # Only allow users to access their own stats unless they're an admin
    current_user = g.user
    if current_user != user_id and not user_manager.is_user_admin(current_user):
        return jsonify({"error": "Access denied"}), 403

    detailed = request.args.get('detailed', '0') == '1'

    try:
        user_stats = user_manager.get_user_stats(user_id)

        # If detailed stats requested, add additional information
        if detailed:
            # Add weekly breakdown
            from datetime import datetime, timedelta
            today = datetime.now()

            # Initialize weekly distances with zeros
            weekly_distances = [0] * 7

            # Get user trips
            from models.trips import TripManager
            trip_manager = TripManager()
            trips = trip_manager.get_user_trips(user_id)

            # Calculate weekly distances
            for trip in trips:
                trip_date = datetime.fromtimestamp(trip.get('start_time', 0))
                days_diff = (today - trip_date).days

                if 0 <= days_diff < 7:
                    weekly_distances[6 - days_diff] += trip.get('distance', 0)

            user_stats['weekly_distances'] = weekly_distances
            user_stats['trips'] = trips[:10]  # Include last 10 trips

            # Add environmental impact data
            total_co2_saved = user_stats.get('total_co2_saved', 0)
            user_stats['environmental_impact'] = {
                'co2_saved': total_co2_saved,
                'trees_equivalent': total_co2_saved / 21,  # Rough estimate: 1 tree absorbs ~21kg CO2 per year
                'car_trips_avoided': user_stats.get('total_distance', 0) / 10  # Rough estimate: 10km per car trip
            }

        return jsonify(user_stats)
    except Exception as e:
        logger.error(f"Error getting user stats: {e}")
        return jsonify({"error": "Server error"}), 500

@api.route('/environmental-impact/<user_id>', methods=['GET'])
@require_auth
def get_environmental_impact(user_id):
    """Get detailed environmental impact data for a user."""
    # Only allow users to access their own data unless they're an admin
    current_user = g.user
    if current_user != user_id and not user_manager.is_user_admin(current_user):
        return jsonify({"error": "Access denied"}), 403

    try:
        user_stats = user_manager.get_user_stats(user_id)
        total_distance = user_stats.get('total_distance', 0)
        total_co2_saved = user_stats.get('total_co2_saved', 0)

        # Calculate environmental impact metrics
        trees_equivalent = total_co2_saved / 21  # Rough estimate: 1 tree absorbs ~21kg CO2 per year
        car_trips_avoided = total_distance / 10  # Rough estimate: 10km per car trip
        gasoline_saved = total_distance * 0.08  # Rough estimate: 0.08 liters per km

        # Calculate monthly breakdown
        from datetime import datetime, timedelta
        today = datetime.now()

        # Get user trips
        from models.trips import TripManager
        trip_manager = TripManager()
        trips = trip_manager.get_user_trips(user_id)

        # Initialize monthly data
        months = 6  # Last 6 months
        monthly_data = []

        for i in range(months):
            month_date = today - timedelta(days=30 * i)
            month_name = month_date.strftime('%b %Y')

            monthly_data.append({
                'month': month_name,
                'co2_saved': 0,
                'distance': 0
            })

        # Calculate monthly totals
        for trip in trips:
            trip_date = datetime.fromtimestamp(trip.get('start_time', 0))
            months_diff = (today.year - trip_date.year) * 12 + today.month - trip_date.month

            if 0 <= months_diff < months:
                monthly_data[months_diff]['co2_saved'] += trip.get('co2_saved', 0)
                monthly_data[months_diff]['distance'] += trip.get('distance', 0)

        # Reverse to get chronological order
        monthly_data.reverse()

        return jsonify({
            'total_co2_saved': total_co2_saved,
            'trees_equivalent': trees_equivalent,
            'car_trips_avoided': car_trips_avoided,
            'gasoline_saved': gasoline_saved,
            'monthly_breakdown': monthly_data
        })
    except Exception as e:
        logger.error(f"Error getting environmental impact: {e}")
        return jsonify({"error": "Server error"}), 500

# Routes endpoints
@api.route('/routes', methods=['GET'])
@require_auth
def get_user_routes():
    """Get user routes."""
    user_id = g.user

    try:
        from models.routes import RouteManager
        route_manager = RouteManager()
        routes = route_manager.get_user_routes(user_id)
        return jsonify(routes)
    except Exception as e:
        logger.error(f"Error getting user routes: {e}")
        return jsonify({"error": "Server error"}), 500

@api.route('/routes/<route_id>', methods=['GET'])
@require_auth
def get_route(route_id):
    """Get a specific route by ID."""
    user_id = g.user

    try:
        from models.routes import RouteManager
        route_manager = RouteManager()
        route = route_manager.get_route(user_id, route_id)

        if not route:
            return jsonify({"error": "Route not found"}), 404

        # Check if the route belongs to the user
        if route.get('user_id') and route.get('user_id') != user_id:
            return jsonify({"error": "Access denied"}), 403

        return jsonify(route)
    except Exception as e:
        logger.error(f"Error getting route: {e}")
        return jsonify({"error": "Server error"}), 500

@api.route('/routes', methods=['POST'])
@require_auth
def create_route():
    """Create a new route."""
    user_id = g.user
    data = request.json

    try:
        from models.routes import RouteManager
        route_manager = RouteManager()
        route_id = route_manager.add_route(user_id, data)

        if not route_id:
            return jsonify({"error": "Failed to create route"}), 500

        return jsonify({"success": True, "route_id": route_id})
    except Exception as e:
        logger.error(f"Error creating route: {e}")
        return jsonify({"error": "Server error"}), 500

@api.route('/routes/save', methods=['POST'])
@require_auth
def save_route():
    """Save a new route."""
    user_id = g.user
    route_data = request.json

    if not route_data:
        return jsonify({"error": "No route data provided"}), 400

    # Ensure required fields are present
    required_fields = ['name', 'start_point', 'end_point', 'distance', 'coordinates']
    missing_fields = [field for field in required_fields if field not in route_data]

    if missing_fields:
        return jsonify({"error": f"Missing required fields: {', '.join(missing_fields)}"}), 400

    # Add metadata
    from datetime import datetime
    route_data['created_at'] = datetime.now().isoformat()
    route_data['user_id'] = user_id

    try:
        from models.routes import RouteManager
        route_manager = RouteManager()
        route_id = route_manager.add_route(user_id, route_data)

        if not route_id:
            return jsonify({"error": "Failed to save route"}), 500

        return jsonify({"success": True, "route_id": route_id})
    except Exception as e:
        logger.error(f"Error saving route: {e}")
        return jsonify({"error": "Server error"}), 500

@api.route('/routes/analysis/<route_id>', methods=['GET'])
@require_auth
def get_route_analysis(route_id):
    """Get detailed analysis for a specific route."""
    user_id = g.user

    try:
        from models.routes import RouteManager
        route_manager = RouteManager()

        # Get the route
        route = route_manager.get_route(user_id, route_id)

        if not route:
            return jsonify({"error": "Route not found"}), 404

        # Check if the route belongs to the user
        if route.get('user_id') and route.get('user_id') != user_id:
            return jsonify({"error": "Access denied"}), 403

        # Calculate route analysis
        # In a real implementation, this would use more sophisticated algorithms
        distance = route.get('distance', 0)
        elevation_gain = route.get('elevation_gain', 0)

        # Calculate difficulty based on distance and elevation
        difficulty = 'easy'
        if distance > 20 or elevation_gain > 300:
            difficulty = 'hard'
        elif distance > 10 or elevation_gain > 150:
            difficulty = 'medium'

        # Calculate estimated time based on average speed
        avg_speed = 15  # km/h
        estimated_time_minutes = (distance / avg_speed) * 60

        # Calculate calories burned (rough estimate)
        calories_per_km = 50
        calories = distance * calories_per_km

        # Calculate CO2 savings
        co2_per_km = 0.12  # kg
        co2_saved = distance * co2_per_km

        # Weather conditions (would be fetched from a weather API in a real implementation)
        weather = {
            'temperature': 22,
            'conditions': 'Sunny',
            'wind_speed': 10,
            'precipitation_chance': 0
        }

        # Traffic conditions (would be fetched from a traffic API in a real implementation)
        traffic = {
            'congestion_level': 'low',
            'busy_segments': []
        }

        return jsonify({
            'route_id': route_id,
            'distance': distance,
            'elevation_gain': elevation_gain,
            'difficulty': difficulty,
            'estimated_time_minutes': estimated_time_minutes,
            'calories': calories,
            'co2_saved': co2_saved,
            'weather': weather,
            'traffic': traffic,
            'safety_score': 85,  # Would be calculated based on various factors
            'scenic_score': 75   # Would be calculated based on various factors
        })
    except Exception as e:
        logger.error(f"Error analyzing route: {e}")
        return jsonify({"error": "Server error"}), 500

@api.route('/routes/safety/<route_id>', methods=['GET'])
@require_auth
def get_route_safety(route_id):
    """Get safety assessment for a specific route."""
    user_id = g.user

    try:
        from models.routes import RouteManager
        route_manager = RouteManager()

        # Get the route
        route = route_manager.get_route(user_id, route_id)

        if not route:
            return jsonify({"error": "Route not found"}), 404

        # Check if the route belongs to the user
        if route.get('user_id') and route.get('user_id') != user_id:
            return jsonify({"error": "Access denied"}), 403

        # In a real implementation, this would use real safety data
        # For now, we'll generate some sample data

        # Overall safety score (0-100)
        safety_score = 85

        # Safety factors
        safety_factors = [
            {
                'name': 'Bike Lanes',
                'score': 90,
                'description': '90% of the route has dedicated bike lanes'
            },
            {
                'name': 'Traffic Volume',
                'score': 70,
                'description': 'Moderate traffic in some segments'
            },
            {
                'name': 'Road Conditions',
                'score': 85,
                'description': 'Good road surface with some minor issues'
            },
            {
                'name': 'Intersection Safety',
                'score': 80,
                'description': '8 intersections, most with bike-friendly signals'
            },
            {
                'name': 'Lighting',
                'score': 75,
                'description': 'Well-lit during daytime, some dark spots at night'
            }
        ]

        # Safety concerns
        safety_concerns = [
            {
                'location': {'lat': 37.7749, 'lng': -122.4194},
                'description': 'Busy intersection with limited visibility',
                'severity': 'medium'
            },
            {
                'location': {'lat': 37.7750, 'lng': -122.4180},
                'description': 'Road narrows with no bike lane',
                'severity': 'high'
            }
        ]

        # Safety recommendations
        recommendations = [
            'Use front and rear lights during evening rides',
            'Exercise caution at the Main St. intersection',
            'Consider alternative route during rush hour (4-6 PM)'
        ]

        return jsonify({
            'route_id': route_id,
            'safety_score': safety_score,
            'safety_factors': safety_factors,
            'safety_concerns': safety_concerns,
            'recommendations': recommendations
        })
    except Exception as e:
        logger.error(f"Error getting route safety: {e}")
        return jsonify({"error": "Server error"}), 500

@api.route('/routes/compare', methods=['POST'])
@require_auth
def compare_routes():
    """Compare multiple routes."""
    user_id = g.user
    data = request.json

    if not data or 'route_ids' not in data:
        return jsonify({"error": "Missing route IDs"}), 400

    route_ids = data.get('route_ids')

    if not isinstance(route_ids, list) or len(route_ids) < 2:
        return jsonify({"error": "At least two route IDs are required"}), 400

    try:
        from models.routes import RouteManager
        route_manager = RouteManager()

        routes = []
        for route_id in route_ids:
            route = route_manager.get_route(user_id, route_id)

            if not route:
                return jsonify({"error": f"Route {route_id} not found"}), 404

            # Check if the route belongs to the user
            if route.get('user_id') and route.get('user_id') != user_id:
                return jsonify({"error": f"Access denied for route {route_id}"}), 403

            routes.append(route)

        # Compare routes
        comparison = []
        for route in routes:
            # Calculate metrics for comparison
            distance = route.get('distance', 0)
            elevation_gain = route.get('elevation_gain', 0)

            # Calculate difficulty
            difficulty = 'easy'
            if distance > 20 or elevation_gain > 300:
                difficulty = 'hard'
            elif distance > 10 or elevation_gain > 150:
                difficulty = 'medium'

            # Calculate estimated time
            avg_speed = 15  # km/h
            estimated_time_minutes = (distance / avg_speed) * 60

            # Calculate calories
            calories_per_km = 50
            calories = distance * calories_per_km

            # Calculate CO2 savings
            co2_per_km = 0.12  # kg
            co2_saved = distance * co2_per_km

            comparison.append({
                'route_id': route.get('id'),
                'name': route.get('name', 'Unnamed Route'),
                'distance': distance,
                'elevation_gain': elevation_gain,
                'difficulty': difficulty,
                'estimated_time_minutes': estimated_time_minutes,
                'calories': calories,
                'co2_saved': co2_saved,
                'safety_score': 85,  # Would be calculated based on various factors
                'scenic_score': 75   # Would be calculated based on various factors
            })

        return jsonify({
            'routes': comparison,
            'recommendation': comparison[0]['route_id']  # In a real implementation, this would be more sophisticated
        })
    except Exception as e:
        logger.error(f"Error comparing routes: {e}")
        return jsonify({"error": "Server error"}), 500

@api.route('/routes/alternatives', methods=['POST'])
@require_auth
def get_alternative_routes():
    """Get alternative routes for a given start and end point."""
    user_id = g.user
    data = request.json

    # Log the request for debugging
    logger.info(f"Route alternatives request received from user: {user_id}")

    if not data:
        logger.warning(f"No data provided in request from user: {user_id}")
        return jsonify({"error": "No data provided"}), 400

    start = data.get('start')
    end = data.get('end')
    options = data.get('options', {})

    logger.info(f"Route request: Start={start}, End={end}, Options={options}")

    if not start or not end:
        logger.warning(f"Missing start or end points in request from user: {user_id}")
        return jsonify({"error": "Start and end points are required"}), 400

    try:
        # In a real implementation, this would call a routing service
        # For now, we'll generate some sample routes

        # Calculate distance between points
        import math
        def calculate_distance(p1, p2):
            # Simple Euclidean distance for demo purposes
            return math.sqrt((p2['lat'] - p1['lat'])**2 + (p2['lng'] - p1['lng'])**2) * 111  # Rough km conversion

        # Generate path between points
        def generate_path(p1, p2, num_points=10):
            path = []
            for i in range(num_points):
                t = i / (num_points - 1)
                path.append({
                    'lat': p1['lat'] + t * (p2['lat'] - p1['lat']),
                    'lng': p1['lng'] + t * (p2['lng'] - p1['lng'])
                })
            return path

        # Generate elevation data
        def generate_elevation_data(path):
            # Simplified elevation profile for demo
            import random
            return [random.uniform(0, 100) for _ in range(len(path))]

        # Base route
        base_distance = calculate_distance(start, end)
        base_time = base_distance * 4  # 4 minutes per km
        base_path = generate_path(start, end)

        # Generate elevation data
        elevation_data = generate_elevation_data(base_path)
        elevation_gain = sum(max(0, elevation_data[i] - elevation_data[i-1]) for i in range(1, len(elevation_data)))

        # Base route
        base_route = {
            'id': 'route_1',
            'name': 'Fastest Route',
            'distance': round(base_distance, 2),
            'elevation_gain': round(elevation_gain),
            'estimated_time_minutes': round(base_time),
            'path': base_path,
            'safety_score': 85,
            'surface_type': 'Mostly paved (90%)',
            'traffic_level': 'Medium'
        }

        # Alternative routes
        scenic_route = {
            'id': 'route_2',
            'name': 'Scenic Route',
            'distance': 12.8,
            'elevation_gain': 180,
            'estimated_time_minutes': 51,
            'path': [
                {'lat': start.get('lat'), 'lng': start.get('lng')},
                {'lat': start.get('lat') + 0.015, 'lng': start.get('lng') + 0.005},
                {'lat': start.get('lat') + 0.025, 'lng': start.get('lng') + 0.015},
                {'lat': end.get('lat'), 'lng': end.get('lng')}
            ]
        }

        safe_route = {
            'id': 'route_3',
            'name': 'Safest Route',
            'distance': 11.2,
            'elevation_gain': 90,
            'estimated_time_minutes': 45,
            'path': [
                {'lat': start.get('lat'), 'lng': start.get('lng')},
                {'lat': start.get('lat') + 0.005, 'lng': start.get('lng') + 0.015},
                {'lat': start.get('lat') + 0.015, 'lng': start.get('lng') + 0.025},
                {'lat': end.get('lat'), 'lng': end.get('lng')}
            ]
        }

        flat_route = {
            'id': 'route_4',
            'name': 'Flattest Route',
            'distance': 13.5,
            'elevation_gain': 50,
            'estimated_time_minutes': 54,
            'path': [
                {'lat': start.get('lat'), 'lng': start.get('lng')},
                {'lat': start.get('lat') + 0.02, 'lng': start.get('lng') + 0.005},
                {'lat': start.get('lat') + 0.03, 'lng': start.get('lng') + 0.01},
                {'lat': end.get('lat'), 'lng': end.get('lng')}
            ]
        }

        # If a specific route type is requested, prioritize that route
        route_type = options.get('type', '').lower()
        routes = [base_route, scenic_route, safe_route, flat_route]

        if route_type == 'scenic':
            routes = [scenic_route, base_route, safe_route, flat_route]
        elif route_type == 'safe':
            routes = [safe_route, base_route, flat_route, scenic_route]
        elif route_type == 'flat':
            routes = [flat_route, base_route, safe_route, scenic_route]

        return jsonify({
            'routes': routes
        })
    except Exception as e:
        logger.error(f"Error getting alternative routes: {e}")
        return jsonify({"error": "Server error"}), 500

# Real-time tracking endpoints
@api.route('/tracking/start', methods=['POST'])
@require_auth
def start_tracking():
    """Start real-time tracking for a user."""
    user_id = g.user
    data = request.json or {}

    device_id = data.get('device_id', request.headers.get('X-Device-ID', 'unknown'))

    try:
        # Generate a tracking ID
        tracking_id = str(uuid.uuid4())

        # Store initial tracking session
        tracking_data = {
            "user_id": user_id,
            "tracking_id": tracking_id,
            "start_time": int(time.time()),
            "device_id": device_id,
            "status": "active",
            "positions": []
        }

        # In a real implementation, we would store this in a database
        # For now, we'll use the sync manager
        sync_manager.upload_changes(user_id, device_id, [{
            "entity_type": "tracking_session",
            "entity_id": tracking_id,
            "data": tracking_data,
            "last_modified": int(time.time())
        }])

        return jsonify({
            "success": True,
            "tracking_id": tracking_id
        })
    except Exception as e:
        logger.error(f"Error starting tracking: {e}")
        return jsonify({"error": "Server error"}), 500

@api.route('/tracking/update', methods=['POST'])
@require_auth
def update_tracking():
    """Update position during real-time tracking."""
    user_id = g.user
    data = request.json

    if not data:
        return jsonify({"error": "No data provided"}), 400

    tracking_id = data.get('tracking_id')
    position = data.get('position')
    device_id = data.get('device_id', request.headers.get('X-Device-ID', 'unknown'))

    if not tracking_id or not position:
        return jsonify({"error": "Missing required fields"}), 400

    try:
        # In a real implementation, we would store this in a database
        # For now, we'll use the sync manager

        # First, get the current tracking session
        result = sync_manager.download_changes(user_id, device_id)

        current_session = None
        for change in result.get("changes", []):
            if change.get("entity_type") == "tracking_session" and change.get("entity_id") == tracking_id:
                current_session = change.get("data", {})
                break

        if not current_session:
            # If we can't find it, create a new one
            current_session = {
                "user_id": user_id,
                "tracking_id": tracking_id,
                "start_time": int(time.time()),
                "device_id": device_id,
                "status": "active",
                "positions": []
            }

        # Add the new position
        position["timestamp"] = position.get("timestamp", int(time.time()))
        current_session["positions"].append(position)
        current_session["last_updated"] = int(time.time())

        # Save the updated session
        sync_manager.upload_changes(user_id, device_id, [{
            "entity_type": "tracking_session",
            "entity_id": tracking_id,
            "data": current_session,
            "last_modified": int(time.time())
        }])

        return jsonify({"success": True})
    except Exception as e:
        logger.error(f"Error updating tracking: {e}")
        return jsonify({"error": "Server error"}), 500

@api.route('/tracking/end', methods=['POST'])
@require_auth
def end_tracking():
    """End real-time tracking session."""
    user_id = g.user
    data = request.json

    if not data:
        return jsonify({"error": "No data provided"}), 400

    tracking_id = data.get('tracking_id')
    device_id = data.get('device_id', request.headers.get('X-Device-ID', 'unknown'))

    if not tracking_id:
        return jsonify({"error": "Missing tracking ID"}), 400

    try:
        # In a real implementation, we would store this in a database
        # For now, we'll use the sync manager

        # First, get the current tracking session
        result = sync_manager.download_changes(user_id, device_id)

        current_session = None
        for change in result.get("changes", []):
            if change.get("entity_type") == "tracking_session" and change.get("entity_id") == tracking_id:
                current_session = change.get("data", {})
                break

        if not current_session:
            return jsonify({"error": "Tracking session not found"}), 404

        # Update the session status
        current_session["status"] = "completed"
        current_session["end_time"] = int(time.time())

        # Calculate trip stats
        positions = current_session.get("positions", [])

        if positions:
            # Simple distance calculation (in a real app, this would be more sophisticated)
            total_distance = calculate_distance(positions)

            # Calculate duration
            start_time = current_session.get("start_time", 0)
            end_time = current_session.get("end_time", int(time.time()))
            duration_seconds = end_time - start_time

            # Add stats to the session
            current_session["stats"] = {
                "total_distance": total_distance,
                "duration_seconds": duration_seconds,
                "avg_speed": total_distance / (duration_seconds / 3600) if duration_seconds > 0 else 0,
                "positions_count": len(positions)
            }

            # Calculate carbon savings
            # Average car emits ~120g CO2 per km
            co2_saved = total_distance * 0.120  # kg of CO2
            current_session["stats"]["co2_saved"] = co2_saved

            # Calculate calories burned (simple estimation)
            # ~50 calories per km for cycling
            calories = total_distance * 50
            current_session["stats"]["calories"] = calories

            # Update user stats (using the correct method signature)
            user_manager.update_user_stats(total_distance, co2_saved, int(calories), duration=0.0)

        # Save the updated session
        sync_manager.upload_changes(user_id, device_id, [{
            "entity_type": "tracking_session",
            "entity_id": tracking_id,
            "data": current_session,
            "last_modified": int(time.time())
        }])

        # Create a trip record from this tracking session
        trip_id = None
        if positions:
            from models.trips import TripManager
            trip_manager = TripManager()

            trip_data = {
                "name": data.get("name", f"Trip on {time.strftime('%Y-%m-%d %H:%M')}"),
                "distance": current_session.get("stats", {}).get("total_distance", 0),
                "duration": current_session.get("stats", {}).get("duration_seconds", 0),
                "start_time": current_session.get("start_time", 0),
                "end_time": current_session.get("end_time", 0),
                "co2_saved": current_session.get("stats", {}).get("co2_saved", 0),
                "calories": current_session.get("stats", {}).get("calories", 0),
                "start_location": positions[0] if positions else {},
                "end_location": positions[-1] if positions else {},
                "tracking_id": tracking_id
            }

            trip_id = trip_manager.create_trip(user_id, trip_data)

        return jsonify({
            "success": True,
            "stats": current_session.get("stats", {}),
            "trip_id": trip_id
        })
    except Exception as e:
        logger.error(f"Error ending tracking: {e}")
        return jsonify({"error": "Server error"}), 500

# Synchronization endpoints
@api.route('/sync/upload', methods=['POST'])
@require_auth
def sync_upload():
    """Upload changes for synchronization."""
    user_id = g.user
    data = request.json

    if not data:
        return jsonify({"error": "No data provided"}), 400

    device_id = data.get('device_id', request.headers.get('X-Device-ID', 'unknown'))
    changes = data.get('changes', [])

    if not changes:
        return jsonify({"success": True, "synced_entities": 0, "conflicts": []})

    try:
        result = sync_manager.upload_changes(user_id, device_id, changes)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error during sync upload: {e}")
        return jsonify({"error": "Server error"}), 500

@api.route('/sync/download', methods=['GET'])
@require_auth
def sync_download():
    """Download changes for synchronization."""
    user_id = g.user
    device_id = request.args.get('device_id', request.headers.get('X-Device-ID', 'unknown'))
    last_sync = int(request.args.get('last_sync', 0))

    try:
        result = sync_manager.download_changes(user_id, device_id, last_sync)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error during sync download: {e}")
        return jsonify({"error": "Server error"}), 500

@api.route('/sync/status', methods=['GET'])
@require_auth
def sync_status():
    """Get synchronization status for user devices."""
    user_id = g.user

    try:
        status = sync_manager.get_device_sync_status(user_id)
        conflicts = sync_manager.get_pending_conflicts(user_id)

        return jsonify({
            "devices": status,
            "conflicts": conflicts,
            "server_time": int(time.time())
        })
    except Exception as e:
        logger.error(f"Error getting sync status: {e}")
        return jsonify({"error": "Server error"}), 500

@api.route('/sync/conflicts/<int:conflict_id>/resolve', methods=['POST'])
@require_auth
def resolve_conflict(conflict_id):
    """Resolve a synchronization conflict."""
    user_id = g.user
    data = request.json

    if not data:
        return jsonify({"error": "No resolution data provided"}), 400

    resolution_data = data.get('resolution')
    device_id = data.get('device_id', request.headers.get('X-Device-ID', 'unknown'))

    if resolution_data is None:
        return jsonify({"error": "Missing resolution data"}), 400

    try:
        success = sync_manager.resolve_conflict(conflict_id, resolution_data, device_id)

        if not success:
            return jsonify({"error": "Failed to resolve conflict"}), 500

        return jsonify({"success": True})
    except Exception as e:
        logger.error(f"Error resolving conflict: {e}")
        return jsonify({"error": "Server error"}), 500

# Notification endpoints
@api.route('/notifications/register', methods=['POST'])
@require_auth
def register_push_notification():
    """Register a device for push notifications."""
    user_id = g.user
    data = request.json

    if not data:
        return jsonify({"error": "No data provided"}), 400

    token = data.get('token')
    platform = data.get('platform', 'unknown')
    device_id = data.get('device_id', request.headers.get('X-Device-ID', 'unknown'))

    if not token:
        return jsonify({"error": "Missing token"}), 400

    try:
        # In a real implementation, store the token in a database
        # For now, store it in the user's preferences

        # Get current notification tokens
        user_data = user_manager.users.get(user_id, {})
        preferences = user_data.get('preferences', {})

        if 'notification_tokens' not in preferences:
            preferences['notification_tokens'] = []

        # Check if token already exists
        token_exists = False
        for existing_token in preferences['notification_tokens']:
            if existing_token.get('token') == token:
                existing_token['platform'] = platform
                existing_token['device_id'] = device_id
                existing_token['updated_at'] = int(time.time())
                token_exists = True
                break

        if not token_exists:
            # Add new token
            preferences['notification_tokens'].append({
                'token': token,
                'platform': platform,
                'device_id': device_id,
                'created_at': int(time.time()),
                'updated_at': int(time.time())
            })

        # Update preferences
        user_manager.update_user_preferences(user_id, preferences)

        return jsonify({"success": True})
    except Exception as e:
        logger.error(f"Error registering push notification: {e}")
        return jsonify({"error": "Server error"}), 500

@api.route('/notifications/settings', methods=['GET', 'POST'])
@require_auth
def notification_settings():
    """Get or update notification settings."""
    user_id = g.user

    if request.method == 'POST':
        data = request.json

        if not data:
            return jsonify({"error": "No data provided"}), 400

        try:
            # Update notification preferences
            user_data = user_manager.users.get(user_id, {})
            preferences = user_data.get('preferences', {})

            if 'notifications' not in preferences:
                preferences['notifications'] = {}

            # Update notification preferences
            preferences['notifications'].update(data)

            # Update preferences
            user_manager.update_user_preferences(user_id, preferences)

            return jsonify({"success": True})
        except Exception as e:
            logger.error(f"Error updating notification settings: {e}")
            return jsonify({"error": "Server error"}), 500
    else:
        try:
            # Get notification preferences
            user_data = user_manager.users.get(user_id, {})
            preferences = user_data.get('preferences', {})
            notification_prefs = preferences.get('notifications', {})

            return jsonify(notification_prefs)
        except Exception as e:
            logger.error(f"Error getting notification settings: {e}")
            return jsonify({"error": "Server error"}), 500

# Media upload endpoints
@api.route('/media/upload', methods=['POST'])
@require_auth
def upload_media():
    """Upload media (photos/videos)."""
    user_id = g.user

    if 'file' not in request.files:
        return jsonify({"error": "No file part"}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400

    # Validate file type
    allowed_types = {
        'image': ['jpg', 'jpeg', 'png', 'gif'],
        'video': ['mp4', 'mov', 'avi']
    }

    filename = file.filename
    file_ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''

    media_type = None
    for type_name, extensions in allowed_types.items():
        if file_ext in extensions:
            media_type = type_name
            break

    if not media_type:
        return jsonify({"error": "File type not allowed"}), 400

    try:
        from werkzeug.utils import secure_filename

        # Create user media directory if it doesn't exist
        media_dir = os.path.join(config.DATA_DIR, 'media', user_id, media_type)
        os.makedirs(media_dir, exist_ok=True)

        # Generate unique filename
        unique_filename = f"{int(time.time())}_{secure_filename(file.filename)}"
        file_path = os.path.join(media_dir, unique_filename)

        # Save file
        file.save(file_path)

        # Create media record
        media_record = {
            "user_id": user_id,
            "type": media_type,
            "filename": unique_filename,
            "original_filename": file.filename,
            "path": file_path,
            "size": os.path.getsize(file_path),
            "mime_type": file.content_type,
            "uploaded_at": int(time.time())
        }

        # Store media record
        # In a real implementation, this would be stored in a database
        # For now, store it in the user's profile

        user_data = user_manager.users.get(user_id, {})

        if 'media' not in user_data:
            user_data['media'] = []

        user_data['media'].append(media_record)
        user_manager.users[user_id] = user_data
        user_manager.save_users()

        return jsonify({
            "success": True,
            "media_id": unique_filename,
            "url": f"/api/media/{user_id}/{media_type}/{unique_filename}"
        })
    except Exception as e:
        logger.error(f"Error uploading media: {e}")
        return jsonify({"error": "Server error"}), 500

@api.route('/media/<user_id>/<media_type>/<filename>', methods=['GET'])
def get_media(user_id, media_type, filename):
    """Get media (photos/videos)."""
    # Public endpoint, but restrict access to private media
    # In a real implementation, this would check permissions

    try:
        from flask import send_from_directory

        # Validate media type
        if media_type not in ['image', 'video']:
            return jsonify({"error": "Invalid media type"}), 400

        # Check if file exists
        media_dir = os.path.join(config.DATA_DIR, 'media', user_id, media_type)
        file_path = os.path.join(media_dir, filename)

        if not os.path.exists(file_path):
            return jsonify({"error": "File not found"}), 404

        return send_from_directory(media_dir, filename)
    except Exception as e:
        logger.error(f"Error getting media: {e}")
        return jsonify({"error": "Server error"}), 500

# Helper functions

def calculate_distance(positions):
    """Calculate distance from GPS positions."""
    import math

    total_distance = 0

    for i in range(1, len(positions)):
        prev_pos = positions[i-1]
        curr_pos = positions[i]

        # Extract lat/lng from positions
        prev_lat = prev_pos.get('lat', 0)
        prev_lng = prev_pos.get('lng', 0)
        curr_lat = curr_pos.get('lat', 0)
        curr_lng = curr_pos.get('lng', 0)

        # Calculate distance using Haversine formula
        R = 6371  # Earth radius in kilometers

        dLat = math.radians(curr_lat - prev_lat)
        dLng = math.radians(curr_lng - prev_lng)

        a = (math.sin(dLat/2) * math.sin(dLat/2) +
             math.cos(math.radians(prev_lat)) * math.cos(math.radians(curr_lat)) *
             math.sin(dLng/2) * math.sin(dLng/2))

        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        distance = R * c

        total_distance += distance

    return total_distance
