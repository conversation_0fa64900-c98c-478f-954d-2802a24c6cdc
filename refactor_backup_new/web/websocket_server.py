"""
EcoCycle - WebSocket Server
Handles real-time communication between web clients and Python backend
"""
import asyncio
import json
import logging
import ssl
import time
import uuid
from typing import Dict, List, Any, Optional, Set

import websockets
from websockets.server import WebSocketServerProtocol

# Add parent directory to path for imports
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import EcoCycle modules
from auth.user_management.user_manager import UserManager
from services.sync.sync_service import get_sync_service
from services.sheets.sheets_manager import SheetsManager

# Configure logger
logger = logging.getLogger(__name__)

class WebSocketServer:
    """WebSocket server for EcoCycle real-time communication"""
    
    def __init__(self, host: str = '0.0.0.0', port: int = 5052):
        self.host = host
        self.port = port
        self.clients: Dict[str, WebSocketServerProtocol] = {}
        self.user_connections: Dict[str, Set[str]] = {}  # user_id -> set of connection IDs
        self.device_connections: Dict[str, str] = {}  # device_id -> connection_id
        
        # Initialize components
        self.sheets_manager = SheetsManager()
        self.user_manager = UserManager(sheets_manager=self.sheets_manager)
        
        # Initialize sync service
        self.sync_service = get_sync_service(user_manager=self.user_manager, sheets_manager=self.sheets_manager)
        
        # Server state
        self.running = False
        self.server = None
        
        # Track ongoing sync operations
        self.sync_operations: Dict[str, Dict[str, Any]] = {}
        
        logger.info(f"WebSocket server initialized on {host}:{port}")
    
    async def start(self):
        """Start the WebSocket server"""
        logger.info("Starting WebSocket server...")
        
        try:
            self.server = await websockets.serve(
                self._handle_connection,
                self.host,
                self.port
            )
            
            self.running = True
            logger.info(f"WebSocket server running on ws://{self.host}:{self.port}")
            
            # Keep the server running indefinitely
            await asyncio.Future()
        except Exception as e:
            logger.error(f"Failed to start WebSocket server: {e}")
            self.running = False
    
    async def stop(self):
        """Stop the WebSocket server"""
        if self.server:
            self.server.close()
            await self.server.wait_closed()
            self.running = False
            logger.info("WebSocket server stopped")
    
    async def _handle_connection(self, websocket: WebSocketServerProtocol, path: str):
        """Handle a new WebSocket connection"""
        connection_id = str(uuid.uuid4())
        try:
            # Add to clients with temporary ID
            self.clients[connection_id] = websocket
            
            # First message should be authentication
            try:
                auth_message = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                auth_data = json.loads(auth_message)
                
                if auth_data.get('type') != 'auth':
                    await self._send_error(websocket, "First message must be authentication")
                    return
                
                # Authenticate the user
                user_id = auth_data.get('user_id')
                token = auth_data.get('token')
                device_id = auth_data.get('device_id')
                
                if not user_id or not device_id:
                    await self._send_error(websocket, "Missing user_id or device_id")
                    return
                
                # Basic validation for now - in production, verify the token
                # For simplicity here, we'll just check the user exists
                if user_id not in self.user_manager.users and user_id != 'guest':
                    await self._send_error(websocket, "Invalid authentication")
                    return
                
                # Associate connection with user and device
                if user_id not in self.user_connections:
                    self.user_connections[user_id] = set()
                self.user_connections[user_id].add(connection_id)
                self.device_connections[device_id] = connection_id
                
                # Send connected confirmation
                await websocket.send(json.dumps({
                    'type': 'connected',
                    'user_id': user_id,
                    'connection_id': connection_id,
                    'timestamp': int(time.time() * 1000),
                    'message': 'Successfully connected to EcoCycle WebSocket server'
                }))
                
                logger.info(f"User {user_id} connected with device {device_id} (connection: {connection_id})")
                
                # Process messages in a loop
                await self._process_messages(websocket, connection_id, user_id, device_id)
                
            except asyncio.TimeoutError:
                await self._send_error(websocket, "Authentication timeout")
                return
            except json.JSONDecodeError:
                await self._send_error(websocket, "Invalid JSON")
                return
            
        finally:
            # Clean up on disconnect
            await self._handle_disconnect(connection_id)
    
    async def _process_messages(self, websocket: WebSocketServerProtocol, connection_id: str, 
                                user_id: str, device_id: str):
        """Process messages from a client"""
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    message_type = data.get('type')
                    
                    if not message_type:
                        await self._send_error(websocket, "Missing message type")
                        continue
                    
                    # Process message based on type
                    if message_type == 'ping':
                        await self._handle_ping(websocket, data)
                    elif message_type == 'sync_request':
                        await self._handle_sync_request(websocket, data, user_id, device_id)
                    elif message_type == 'track_update':
                        await self._handle_track_update(websocket, data, user_id, device_id)
                    elif message_type == 'upload_changes':
                        await self._handle_upload_changes(websocket, data, user_id, device_id)
                    else:
                        logger.warning(f"Unknown message type: {message_type}")
                        await self._send_error(websocket, f"Unknown message type: {message_type}")
                
                except json.JSONDecodeError:
                    await self._send_error(websocket, "Invalid JSON")
                    continue
        
        except websockets.ConnectionClosed:
            logger.info(f"Connection closed for {connection_id}")
            # Connection already closed, handled in _handle_disconnect
            pass
        except Exception as e:
            logger.error(f"Error processing messages: {e}")
            try:
                await self._send_error(websocket, f"Server error: {str(e)}")
            except:
                pass  # Connection probably closed
    
    async def _handle_disconnect(self, connection_id: str):
        """Handle client disconnection"""
        if connection_id in self.clients:
            # Remove from clients
            del self.clients[connection_id]
            
            # Remove from user connections
            for user_id, connections in list(self.user_connections.items()):
                if connection_id in connections:
                    connections.remove(connection_id)
                    if not connections:
                        del self.user_connections[user_id]
                    logger.info(f"User {user_id} disconnected (connection: {connection_id})")
                    break
            
            # Remove from device connections
            for device_id, conn_id in list(self.device_connections.items()):
                if conn_id == connection_id:
                    del self.device_connections[device_id]
                    break
    
    async def _handle_ping(self, websocket: WebSocketServerProtocol, data: Dict[str, Any]):
        """Handle ping message"""
        await websocket.send(json.dumps({
            'type': 'pong',
            'timestamp': data.get('timestamp', int(time.time() * 1000))
        }))
    
    async def _handle_sync_request(self, websocket: WebSocketServerProtocol, data: Dict[str, Any],
                                  user_id: str, device_id: str):
        """Handle data synchronization request"""
        request_id = data.get('request_id', str(uuid.uuid4()))
        entity_types = data.get('entity_types', ['all'])
        last_sync = data.get('last_sync', 0)
        
        # Acknowledge sync request immediately
        await websocket.send(json.dumps({
            'type': 'sync_acknowledged',
            'request_id': request_id,
            'message': 'Sync request received and processing',
            'timestamp': int(time.time() * 1000)
        }))
        
        # Track sync operation
        self.sync_operations[request_id] = {
            'user_id': user_id,
            'device_id': device_id,
            'status': 'processing',
            'start_time': time.time(),
            'progress': 0
        }
        
        try:
            # Begin sync in background task to not block other messages
            asyncio.create_task(self._perform_sync(
                websocket, request_id, user_id, device_id, entity_types, last_sync
            ))
        
        except Exception as e:
            logger.error(f"Error starting sync task: {e}")
            await self._send_sync_error(websocket, request_id, str(e))
    
    async def _perform_sync(self, websocket: WebSocketServerProtocol, request_id: str, 
                           user_id: str, device_id: str, entity_types: List[str], last_sync: int):
        """Perform the actual data synchronization"""
        try:
            # Send progress updates
            for progress in [10, 25, 50, 75]:
                await asyncio.sleep(0.5)  # Simulate work being done
                
                # Update progress
                self.sync_operations[request_id]['progress'] = progress
                
                await websocket.send(json.dumps({
                    'type': 'sync_progress',
                    'request_id': request_id,
                    'progress': progress,
                    'items_processed': progress,
                    'total_items': 100,
                    'message': f'Syncing in progress: {progress}%'
                }))
            
            # Get user data to sync
            changes = []
            
            if 'all' in entity_types or 'preferences' in entity_types:
                # Get user preferences
                user_data = self.user_manager.users.get(user_id, {})
                if user_data and 'preferences' in user_data:
                    changes.append({
                        'entity_type': 'preferences',
                        'entity_id': f'{user_id}_preferences',
                        'data': user_data.get('preferences', {})
                    })
            
            if 'all' in entity_types or 'stats' in entity_types:
                # Get user statistics if available
                user_stats = self.user_manager.get_user_statistics(user_id)
                if user_stats:
                    changes.append({
                        'entity_type': 'stats',
                        'entity_id': f'{user_id}_stats',
                        'data': user_stats
                    })
            
            # Complete the sync process
            self.sync_operations[request_id]['progress'] = 100
            self.sync_operations[request_id]['status'] = 'completed'
            
            # Send final sync response
            await websocket.send(json.dumps({
                'type': 'sync_response',
                'request_id': request_id,
                'success': True,
                'changes': changes,
                'timestamp': int(time.time() * 1000),
                'message': 'Sync completed successfully'
            }))
            
            logger.info(f"Sync completed for user {user_id} on device {device_id}")
            
        except Exception as e:
            logger.error(f"Error during sync: {e}")
            self.sync_operations[request_id]['status'] = 'failed'
            await self._send_sync_error(websocket, request_id, str(e))
    
    async def _send_sync_error(self, websocket: WebSocketServerProtocol, request_id: str, error_message: str):
        """Send sync error to client"""
        await websocket.send(json.dumps({
            'type': 'sync_response',
            'request_id': request_id,
            'success': False,
            'error': error_message,
            'timestamp': int(time.time() * 1000),
            'message': f'Sync failed: {error_message}'
        }))
    
    async def _handle_track_update(self, websocket: WebSocketServerProtocol, data: Dict[str, Any],
                                  user_id: str, device_id: str):
        """Handle real-time tracking update"""
        tracking_id = data.get('tracking_id')
        position = data.get('position')
        
        if not tracking_id or not position:
            await self._send_error(websocket, "Missing tracking_id or position")
            return
        
        # Store the position update - in a real application, you would save this to your database
        # For demonstration, we'll just log it
        logger.info(f"Received position update for user {user_id}, tracking session {tracking_id}")
        
        # Acknowledge the update
        await websocket.send(json.dumps({
            'type': 'track_update_ack',
            'tracking_id': tracking_id,
            'timestamp': int(time.time() * 1000),
            'message': 'Position update received'
        }))
        
        # If there are other devices connected for this user, forward the update
        # This enables multi-device real-time tracking visualization
        if user_id in self.user_connections:
            for conn_id in self.user_connections[user_id]:
                if conn_id != self.device_connections.get(device_id):
                    try:
                        other_device = self.clients.get(conn_id)
                        if other_device:
                            await other_device.send(json.dumps({
                                'type': 'track_update',
                                'tracking_id': tracking_id,
                                'position': position,
                                'device_id': device_id,
                                'timestamp': int(time.time() * 1000)
                            }))
                    except Exception as e:
                        logger.error(f"Error forwarding track update: {e}")
    
    async def _handle_upload_changes(self, websocket: WebSocketServerProtocol, data: Dict[str, Any],
                                   user_id: str, device_id: str):
        """Handle changes uploaded from a client"""
        request_id = data.get('request_id', str(uuid.uuid4()))
        changes = data.get('changes', [])
        
        if not changes:
            await self._send_error(websocket, "No changes provided")
            return
        
        try:
            # Process each change
            processed_changes = []
            
            for change in changes:
                entity_type = change.get('entity_type')
                entity_id = change.get('entity_id')
                change_data = change.get('data')
                
                if not entity_type or not entity_id or not change_data:
                    continue
                
                # Based on entity type, process differently
                if entity_type == 'preferences':
                    # Update user preferences
                    self.user_manager.update_user_preferences(user_id, change_data)
                    processed_changes.append({
                        'entity_type': entity_type,
                        'entity_id': entity_id,
                        'status': 'success'
                    })
                
                elif entity_type == 'route':
                    # Store route data - in a real app this would use your route storage system
                    logger.info(f"Storing route data for {entity_id}")
                    processed_changes.append({
                        'entity_type': entity_type,
                        'entity_id': entity_id,
                        'status': 'success'
                    })
                
                else:
                    # Handle other entity types
                    logger.warning(f"Unhandled entity type: {entity_type}")
                    processed_changes.append({
                        'entity_type': entity_type,
                        'entity_id': entity_id,
                        'status': 'unknown_type'
                    })
            
            # Send response
            await websocket.send(json.dumps({
                'type': 'upload_response',
                'request_id': request_id,
                'success': True,
                'processed': processed_changes,
                'timestamp': int(time.time() * 1000)
            }))
            
            # Notify other devices to sync
            if user_id in self.user_connections:
                for conn_id in self.user_connections[user_id]:
                    if conn_id != self.device_connections.get(device_id):
                        try:
                            other_device = self.clients.get(conn_id)
                            if other_device:
                                await other_device.send(json.dumps({
                                    'type': 'notification',
                                    'data': {
                                        'type': 'sync_required',
                                        'message': 'Data has been updated, please sync'
                                    },
                                    'timestamp': int(time.time() * 1000)
                                }))
                        except Exception as e:
                            logger.error(f"Error notifying other device: {e}")
        
        except Exception as e:
            logger.error(f"Error processing upload: {e}")
            await websocket.send(json.dumps({
                'type': 'upload_response',
                'request_id': request_id,
                'success': False,
                'error': str(e),
                'timestamp': int(time.time() * 1000)
            }))
    
    async def _send_error(self, websocket: WebSocketServerProtocol, message: str, code: int = 400):
        """Send error to client"""
        try:
            await websocket.send(json.dumps({
                'type': 'error',
                'message': message,
                'code': code,
                'timestamp': int(time.time() * 1000)
            }))
        except:
            pass  # Connection may be closed

async def run_server(host: str = '0.0.0.0', port: int = 5052):
    """Run the WebSocket server"""
    server = WebSocketServer(host, port)
    await server.start()

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Start the WebSocket server
    asyncio.run(run_server())
