"""
EcoCycle - Supabase Client Module
Handles integration with Supabase for database operations
"""
import os
import logging
from supabase import create_client, Client
from dotenv import load_dotenv
import sys

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import EcoCycle modules
import config.config
import core.dependency.dependency_manager

# Configure logger
logger = logging.getLogger(__name__)

# Ensure required packages are installed
required_packages = ['supabase', 'python-dotenv']
dependency_manager.ensure_packages(required_packages)

# Load environment variables
load_dotenv()

class SupabaseClient:
    """Wrapper for Supabase client with authentication and error handling."""
    
    _instance = None
    
    def __new__(cls):
        """Implement singleton pattern for Supabase client."""
        if cls._instance is None:
            cls._instance = super(SupabaseClient, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialize the Supabase client if not already initialized."""
        if self._initialized:
            return
            
        self.supabase_url = os.environ.get('SUPABASE_URL')
        self.supabase_key = os.environ.get('SUPABASE_KEY')
        self.client = None
        
        if not self.supabase_url or not self.supabase_key:
            logger.warning("Supabase credentials not found in environment variables")
            # Set placeholder values for development
            self.supabase_url = "https://placeholder-project.supabase.co"
            self.supabase_key = "placeholder-key"
        
        try:
            self.client = create_client(self.supabase_url, self.supabase_key)
            logger.info("Supabase client initialized")
        except Exception as e:
            logger.error(f"Error initializing Supabase client: {e}")
            self.client = None
        
        self._initialized = True
    
    def get_client(self) -> Client:
        """
        Get the Supabase client instance.
        
        Returns:
            Client: Supabase client or None if initialization failed
        """
        return self.client
    
    def is_available(self) -> bool:
        """
        Check if Supabase is available and correctly configured.
        
        Returns:
            bool: True if Supabase is available, False otherwise
        """
        return self.client is not None
    
    def insert(self, table: str, data: dict) -> dict:
        """
        Insert data into a Supabase table.
        
        Args:
            table: The table name
            data: The data to insert
            
        Returns:
            dict: The inserted data with ID
        """
        if not self.is_available():
            logger.error("Supabase client not available")
            return None
            
        try:
            result = self.client.table(table).insert(data).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error inserting data into {table}: {e}")
            return None
    
    def select(self, table: str, columns: str = "*", filters: dict = None) -> list:
        """
        Select data from a Supabase table with optional filtering.
        
        Args:
            table: The table name
            columns: The columns to select (default: all)
            filters: Dictionary of filters to apply
            
        Returns:
            list: The selected data
        """
        if not self.is_available():
            logger.error("Supabase client not available")
            return []
            
        try:
            query = self.client.table(table).select(columns)
            
            # Apply filters if provided
            if filters:
                for key, value in filters.items():
                    query = query.eq(key, value)
            
            result = query.execute()
            return result.data
        except Exception as e:
            logger.error(f"Error selecting data from {table}: {e}")
            return []
    
    def update(self, table: str, data: dict, filters: dict) -> list:
        """
        Update data in a Supabase table.
        
        Args:
            table: The table name
            data: The data to update
            filters: Dictionary of filters to apply
            
        Returns:
            list: The updated data
        """
        if not self.is_available():
            logger.error("Supabase client not available")
            return []
            
        try:
            query = self.client.table(table).update(data)
            
            # Apply filters
            for key, value in filters.items():
                query = query.eq(key, value)
            
            result = query.execute()
            return result.data
        except Exception as e:
            logger.error(f"Error updating data in {table}: {e}")
            return []
    
    def delete(self, table: str, filters: dict) -> list:
        """
        Delete data from a Supabase table.
        
        Args:
            table: The table name
            filters: Dictionary of filters to apply
            
        Returns:
            list: The deleted data
        """
        if not self.is_available():
            logger.error("Supabase client not available")
            return []
            
        try:
            query = self.client.table(table).delete()
            
            # Apply filters
            for key, value in filters.items():
                query = query.eq(key, value)
            
            result = query.execute()
            return result.data
        except Exception as e:
            logger.error(f"Error deleting data from {table}: {e}")
            return []
    
    def search(self, table: str, column: str, query: str, columns: str = "*") -> list:
        """
        Search data in a Supabase table using pattern matching.
        
        Args:
            table: The table name
            column: The column to search in
            query: The search query
            columns: The columns to select (default: all)
            
        Returns:
            list: The search results
        """
        if not self.is_available():
            logger.error("Supabase client not available")
            return []
            
        try:
            result = self.client.table(table).select(columns).ilike(column, f"%{query}%").execute()
            return result.data
        except Exception as e:
            logger.error(f"Error searching data in {table}: {e}")
            return []
    
    def spatial_search(self, table: str, point: tuple, distance: float, columns: str = "*") -> list:
        """
        Perform a spatial search on a Supabase table with PostGIS extension.
        
        Args:
            table: The table name
            point: The reference point as (lat, lng)
            distance: The search radius in meters
            columns: The columns to select (default: all)
            
        Returns:
            list: The spatial search results
        """
        if not self.is_available():
            logger.error("Supabase client not available")
            return []
            
        try:
            # Using PostGIS ST_DWithin for spatial queries
            # This assumes the table has a geometry/geography column named 'location'
            lat, lng = point
            query = f"""
            SELECT {columns}
            FROM {table}
            WHERE ST_DWithin(
                location,
                ST_SetSRID(ST_MakePoint({lng}, {lat}), 4326)::geography,
                {distance}
            )
            """
            
            result = self.client.rpc('spatial_search', {
                "query": query
            }).execute()
            
            return result.data
        except Exception as e:
            logger.error(f"Error performing spatial search in {table}: {e}")
            return []
    
    def authenticate(self, email: str, password: str) -> dict:
        """
        Authenticate a user with Supabase.
        
        Args:
            email: User email
            password: User password
            
        Returns:
            dict: User data and session
        """
        if not self.is_available():
            logger.error("Supabase client not available")
            return None
            
        try:
            result = self.client.auth.sign_in_with_password({
                "email": email,
                "password": password
            })
            return result
        except Exception as e:
            logger.error(f"Error authenticating user: {e}")
            return None
    
    def create_user(self, email: str, password: str, user_data: dict = None) -> dict:
        """
        Create a new user in Supabase.
        
        Args:
            email: User email
            password: User password
            user_data: Additional user data
            
        Returns:
            dict: User data
        """
        if not self.is_available():
            logger.error("Supabase client not available")
            return None
            
        try:
            # Create user in Supabase Auth
            auth_result = self.client.auth.sign_up({
                "email": email, 
                "password": password
            })
            
            # If user_data is provided, store in profiles table
            if user_data and auth_result.user:
                user_id = auth_result.user.id
                profile_data = {
                    "id": user_id,
                    **user_data
                }
                
                # Insert profile data
                self.client.table("profiles").insert(profile_data).execute()
            
            return auth_result
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            return None
