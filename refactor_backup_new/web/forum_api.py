"""
EcoCycle - Forum API Module
Handles API endpoints for the community forum feature.
"""
import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from flask import Blueprint, request, jsonify, g, current_app
from functools import wraps

# Add parent directory to path
import sys
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import EcoCycle modules
from web.api_endpoints import require_auth
from auth.user_management.user_manager import UserManager

# Configure logger
logger = logging.getLogger(__name__)

# Create Blueprint
forum_api = Blueprint('forum_api', __name__, url_prefix='/api/forum')

# Initialize user manager
user_manager = UserManager()

# Database helper function
def get_db():
    """Get database connection from current app"""
    if 'db' not in g:
        from models.database import get_supabase_client
        g.db = get_supabase_client()
    return g.db

# ============= Forum Category Endpoints =============

@forum_api.route('/categories', methods=['GET'])
def get_categories():
    """Get all forum categories"""
    try:
        db = get_db()
        result = db.table('forum_categories').select('*').execute()
        
        if 'error' in result:
            logger.error(f"Error fetching categories: {result['error']}")
            return jsonify({"error": "Failed to fetch categories"}), 500
            
        return jsonify(result['data'])
    except Exception as e:
        logger.error(f"Error in get_categories: {str(e)}")
        return jsonify({"error": "Server error"}), 500

# ============= Forum Topic Endpoints =============

@forum_api.route('/topics/<category_id>', methods=['GET'])
def get_topics(category_id):
    """Get all topics in a category"""
    try:
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 20, type=int)
        
        db = get_db()
        # Get topics with user info and post counts
        query = """
        SELECT t.*, 
               u.username, 
               u.profile_image,
               COUNT(p.id) AS reply_count,
               MAX(p.created_at) AS last_activity
        FROM forum_topics t
        LEFT JOIN users u ON t.author_id = u.id
        LEFT JOIN forum_posts p ON t.id = p.topic_id
        WHERE t.category_id = ?
        GROUP BY t.id, u.username, u.profile_image
        ORDER BY t.pinned DESC, last_activity DESC
        LIMIT ? OFFSET ?
        """
        
        offset = (page - 1) * page_size
        result = db.raw(query, [category_id, page_size, offset]).execute()
        
        if 'error' in result:
            logger.error(f"Error fetching topics: {result['error']}")
            return jsonify({"error": "Failed to fetch topics"}), 500
            
        # Get total count for pagination
        count_result = db.table('forum_topics').select('count', count='exact').eq('category_id', category_id).execute()
        total_count = count_result.get('count', 0)
        
        return jsonify({
            "topics": result['data'],
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_count": total_count,
                "total_pages": (total_count + page_size - 1) // page_size
            }
        })
    except Exception as e:
        logger.error(f"Error in get_topics: {str(e)}")
        return jsonify({"error": "Server error"}), 500

@forum_api.route('/topic/<topic_id>', methods=['GET'])
def get_topic_with_posts(topic_id):
    """Get a topic with all its posts"""
    try:
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 20, type=int)
        
        db = get_db()
        # Get topic details
        topic_result = db.table('forum_topics').select('*').eq('id', topic_id).single().execute()
        
        if 'error' in topic_result or not topic_result.get('data'):
            return jsonify({"error": "Topic not found"}), 404
            
        topic = topic_result['data']
        
        # Get author details
        author_result = db.table('users').select('username,profile_image').eq('id', topic['author_id']).single().execute()
        if not 'error' in author_result and author_result.get('data'):
            topic['author'] = author_result['data']
        
        # Get posts with pagination
        offset = (page - 1) * page_size
        posts_query = """
        SELECT p.*,
               u.username,
               u.profile_image,
               COUNT(r.id) AS like_count 
        FROM forum_posts p
        LEFT JOIN users u ON p.author_id = u.id
        LEFT JOIN forum_reactions r ON p.id = r.post_id AND r.reaction_type = 'like'
        WHERE p.topic_id = ?
        GROUP BY p.id, u.username, u.profile_image
        ORDER BY p.created_at ASC
        LIMIT ? OFFSET ?
        """
        
        posts_result = db.raw(posts_query, [topic_id, page_size, offset]).execute()
        
        # Get total post count
        count_result = db.table('forum_posts').select('count', count='exact').eq('topic_id', topic_id).execute()
        total_count = count_result.get('count', 0)
        
        # Update view count
        db.table('forum_topics').update({'views': topic['views'] + 1}).eq('id', topic_id).execute()
        
        return jsonify({
            "topic": topic,
            "posts": posts_result.get('data', []),
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_count": total_count,
                "total_pages": (total_count + page_size - 1) // page_size
            }
        })
    except Exception as e:
        logger.error(f"Error in get_topic_with_posts: {str(e)}")
        return jsonify({"error": "Server error"}), 500

@forum_api.route('/topic', methods=['POST'])
@require_auth
def create_topic():
    """Create a new topic"""
    try:
        user_id = g.user
        data = request.json
        
        required_fields = ['title', 'content', 'category_id']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400
                
        # Sanitize content if needed (implement sanitization)
        
        # Create topic
        topic_data = {
            'title': data['title'],
            'content': data['content'],
            'category_id': data['category_id'],
            'author_id': user_id,
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat(),
            'views': 0,
            'pinned': False
        }
        
        db = get_db()
        result = db.table('forum_topics').insert(topic_data).execute()
        
        if 'error' in result:
            logger.error(f"Error creating topic: {result['error']}")
            return jsonify({"error": "Failed to create topic"}), 500
            
        return jsonify({"success": True, "topic_id": result['data'][0]['id']}), 201
    except Exception as e:
        logger.error(f"Error in create_topic: {str(e)}")
        return jsonify({"error": "Server error"}), 500

# ============= Forum Post Endpoints =============

@forum_api.route('/post', methods=['POST'])
@require_auth
def create_post():
    """Add a reply to a topic"""
    try:
        user_id = g.user
        data = request.json
        
        required_fields = ['topic_id', 'content']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400
                
        # Sanitize content if needed
        
        # Create post
        post_data = {
            'topic_id': data['topic_id'],
            'content': data['content'],
            'author_id': user_id,
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }
        
        db = get_db()
        result = db.table('forum_posts').insert(post_data).execute()
        
        if 'error' in result:
            logger.error(f"Error creating post: {result['error']}")
            return jsonify({"error": "Failed to create post"}), 500
            
        # Update last activity on topic
        db.table('forum_topics').update({'updated_at': datetime.utcnow().isoformat()}).eq('id', data['topic_id']).execute()
        
        return jsonify({"success": True, "post_id": result['data'][0]['id']}), 201
    except Exception as e:
        logger.error(f"Error in create_post: {str(e)}")
        return jsonify({"error": "Server error"}), 500

@forum_api.route('/post/<post_id>', methods=['PUT'])
@require_auth
def edit_post(post_id):
    """Edit a post"""
    try:
        user_id = g.user
        data = request.json
        
        if 'content' not in data:
            return jsonify({"error": "Missing content field"}), 400
            
        db = get_db()
        
        # Check if user is the author or admin
        post_result = db.table('forum_posts').select('author_id').eq('id', post_id).single().execute()
        
        if 'error' in post_result or not post_result.get('data'):
            return jsonify({"error": "Post not found"}), 404
            
        post = post_result['data']
        
        # Only allow author or admin to edit
        if post['author_id'] != user_id and not user_manager.is_admin(user_id):
            return jsonify({"error": "Not authorized to edit this post"}), 403
            
        # Update post
        update_data = {
            'content': data['content'],
            'updated_at': datetime.utcnow().isoformat(),
            'edited': True
        }
        
        result = db.table('forum_posts').update(update_data).eq('id', post_id).execute()
        
        if 'error' in result:
            logger.error(f"Error updating post: {result['error']}")
            return jsonify({"error": "Failed to update post"}), 500
            
        return jsonify({"success": True})
    except Exception as e:
        logger.error(f"Error in edit_post: {str(e)}")
        return jsonify({"error": "Server error"}), 500

@forum_api.route('/post/<post_id>', methods=['DELETE'])
@require_auth
def delete_post(post_id):
    """Delete a post"""
    try:
        user_id = g.user
        db = get_db()
        
        # Check if user is the author or admin
        post_result = db.table('forum_posts').select('author_id,topic_id').eq('id', post_id).single().execute()
        
        if 'error' in post_result or not post_result.get('data'):
            return jsonify({"error": "Post not found"}), 404
            
        post = post_result['data']
        
        # Only allow author or admin to delete
        if post['author_id'] != user_id and not user_manager.is_admin(user_id):
            return jsonify({"error": "Not authorized to delete this post"}), 403
            
        # Delete post
        result = db.table('forum_posts').delete().eq('id', post_id).execute()
        
        if 'error' in result:
            logger.error(f"Error deleting post: {result['error']}")
            return jsonify({"error": "Failed to delete post"}), 500
            
        return jsonify({"success": True})
    except Exception as e:
        logger.error(f"Error in delete_post: {str(e)}")
        return jsonify({"error": "Server error"}), 500

# ============= Forum Reaction Endpoints =============

@forum_api.route('/reaction', methods=['POST'])
@require_auth
def add_reaction():
    """React to a post (like, upvote, etc.)"""
    try:
        user_id = g.user
        data = request.json
        
        required_fields = ['post_id', 'reaction_type']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400
                
        # Validate reaction type
        valid_reactions = ['like', 'upvote', 'downvote', 'heart', 'smile']
        if data['reaction_type'] not in valid_reactions:
            return jsonify({"error": "Invalid reaction type"}), 400
            
        db = get_db()
        
        # Check if reaction already exists
        existing = db.table('forum_reactions').select('id').eq('post_id', data['post_id']).eq('user_id', user_id).eq('reaction_type', data['reaction_type']).execute()
        
        if 'data' in existing and existing['data']:
            # Remove existing reaction (toggle)
            db.table('forum_reactions').delete().eq('id', existing['data'][0]['id']).execute()
            return jsonify({"success": True, "action": "removed"})
        else:
            # Add new reaction
            reaction_data = {
                'post_id': data['post_id'],
                'user_id': user_id,
                'reaction_type': data['reaction_type'],
                'created_at': datetime.utcnow().isoformat()
            }
            
            result = db.table('forum_reactions').insert(reaction_data).execute()
            
            if 'error' in result:
                logger.error(f"Error adding reaction: {result['error']}")
                return jsonify({"error": "Failed to add reaction"}), 500
                
            return jsonify({"success": True, "action": "added"})
    except Exception as e:
        logger.error(f"Error in add_reaction: {str(e)}")
        return jsonify({"error": "Server error"}), 500

# ============= Forum Search Endpoint =============

@forum_api.route('/search', methods=['GET'])
def search_forum():
    """Search topics and posts"""
    try:
        query = request.args.get('q', '')
        if not query or len(query) < 3:
            return jsonify({"error": "Search query too short"}), 400
            
        db = get_db()
        
        # Search in topics
        topics_query = """
        SELECT t.*, c.name as category_name, u.username 
        FROM forum_topics t
        JOIN forum_categories c ON t.category_id = c.id
        JOIN users u ON t.author_id = u.id
        WHERE (t.title ILIKE ? OR t.content ILIKE ?)
        ORDER BY t.created_at DESC
        LIMIT 20
        """
        
        search_term = f'%{query}%'
        topics_result = db.raw(topics_query, [search_term, search_term]).execute()
        
        # Search in posts
        posts_query = """
        SELECT p.*, t.title as topic_title, u.username 
        FROM forum_posts p
        JOIN forum_topics t ON p.topic_id = t.id
        JOIN users u ON p.author_id = u.id
        WHERE p.content ILIKE ?
        ORDER BY p.created_at DESC
        LIMIT 20
        """
        
        posts_result = db.raw(posts_query, [search_term]).execute()
        
        return jsonify({
            "topics": topics_result.get('data', []),
            "posts": posts_result.get('data', [])
        })
    except Exception as e:
        logger.error(f"Error in search_forum: {str(e)}")
        return jsonify({"error": "Server error"}), 500

# ============= User Posts Endpoint =============

@forum_api.route('/user/<user_id>/posts', methods=['GET'])
def get_user_posts(user_id):
    """Get all posts by a user"""
    try:
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 20, type=int)
        
        db = get_db()
        offset = (page - 1) * page_size
        
        query = """
        SELECT p.*, t.title as topic_title
        FROM forum_posts p
        JOIN forum_topics t ON p.topic_id = t.id
        WHERE p.author_id = ?
        ORDER BY p.created_at DESC
        LIMIT ? OFFSET ?
        """
        
        result = db.raw(query, [user_id, page_size, offset]).execute()
        
        # Get total count
        count_result = db.table('forum_posts').select('count', count='exact').eq('author_id', user_id).execute()
        total_count = count_result.get('count', 0)
        
        return jsonify({
            "posts": result.get('data', []),
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_count": total_count,
                "total_pages": (total_count + page_size - 1) // page_size
            }
        })
    except Exception as e:
        logger.error(f"Error in get_user_posts: {str(e)}")
        return jsonify({"error": "Server error"}), 500
