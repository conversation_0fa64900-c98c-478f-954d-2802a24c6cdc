[bandit]
exclude_dirs = [
    "Tests",
    "venv", 
    "eco_venv",
    ".venv",
    "build",
    "dist",
    "backups",
    "temp",
    "cache",
    "data",
    "__pycache__",
    ".git",
    ".pytest_cache"
]

skips = [
    "B101",  # assert_used
    "B601",  # paramiko_calls
    "B602",  # subprocess_popen_with_shell_equals_true
    "B603",  # subprocess_without_shell_equals_true
    "B604",  # any_other_function_with_shell_equals_true
    "B605",  # start_process_with_a_shell
    "B606",  # start_process_with_no_shell
    "B607",  # start_process_with_partial_path
    "B108"   # hardcoded_tmp_directory
]

# Only scan Python files
include = ["*.py"]

# Confidence levels: LOW, MEDIUM, HIGH
confidence = "MEDIUM"

# Severity levels: LOW, MEDIUM, HIGH
severity = "MEDIUM"
