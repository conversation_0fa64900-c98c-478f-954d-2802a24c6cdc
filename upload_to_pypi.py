#!/usr/bin/env python3
"""
EcoCycle PyPI Upload Script
This script helps upload the EcoCycle package to PyPI.
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path

def check_requirements():
    """Check if required tools are installed."""
    required_tools = ['twine']
    missing = []
    
    for tool in required_tools:
        try:
            subprocess.run([tool, '--version'], capture_output=True, check=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            missing.append(tool)
    
    if missing:
        print(f"Missing required tools: {', '.join(missing)}")
        print("Install with: pip install twine")
        return False
    return True

def check_dist_files():
    """Check if distribution files exist."""
    dist_dir = Path('dist')
    if not dist_dir.exists():
        print("Error: dist/ directory not found. Run 'python setup.py sdist bdist_wheel' first.")
        return False
    
    files = list(dist_dir.glob('ecocycle-*.tar.gz')) + list(dist_dir.glob('ecocycle-*.whl'))
    if not files:
        print("Error: No distribution files found in dist/")
        return False
    
    print(f"Found distribution files:")
    for file in files:
        print(f"  - {file}")
    return True

def upload_to_test_pypi():
    """Upload to Test PyPI."""
    print("Uploading to Test PyPI...")
    cmd = [
        'twine', 'upload', 
        '--repository', 'testpypi',
        'dist/*'
    ]
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ Successfully uploaded to Test PyPI!")
        print("Test installation with:")
        print("pip install --index-url https://test.pypi.org/simple/ ecocycle")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to upload to Test PyPI: {e}")
        return False

def upload_to_pypi():
    """Upload to PyPI."""
    print("Uploading to PyPI...")
    cmd = ['twine', 'upload', 'dist/*']
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ Successfully uploaded to PyPI!")
        print("Install with: pip install ecocycle")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to upload to PyPI: {e}")
        return False

def check_package():
    """Check package with twine."""
    print("Checking package...")
    cmd = ['twine', 'check', 'dist/*']
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ Package check passed!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Package check failed: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Upload EcoCycle to PyPI')
    parser.add_argument('--test', action='store_true', 
                       help='Upload to Test PyPI instead of PyPI')
    parser.add_argument('--check-only', action='store_true',
                       help='Only check the package, do not upload')
    
    args = parser.parse_args()
    
    print("EcoCycle PyPI Upload Script")
    print("=" * 30)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Check distribution files
    if not check_dist_files():
        sys.exit(1)
    
    # Check package
    if not check_package():
        sys.exit(1)
    
    if args.check_only:
        print("Package check completed successfully!")
        return
    
    # Upload
    if args.test:
        success = upload_to_test_pypi()
    else:
        # Confirm before uploading to production PyPI
        confirm = input("Are you sure you want to upload to PyPI? (y/N): ")
        if confirm.lower() != 'y':
            print("Upload cancelled.")
            return
        success = upload_to_pypi()
    
    if success:
        print("\n🎉 Upload completed successfully!")
    else:
        print("\n❌ Upload failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
