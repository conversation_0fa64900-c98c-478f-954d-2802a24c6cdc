# EcoCycle Environment Configuration

# Debug mode (true/false)
DEBUG=false

# Admin password for accessing admin panel
ADMIN_PASSWORD=your_secure_admin_password

# Google Sheets API credentials (service account JSON as a string)
# This should be a JSON string containing the service account credentials
GOOGLE_SHEETS_CREDENTIALS_JSON={"type":"service_account","project_id":"your-project","private_key_id":"key-id","private_key":"-----B<PERSON>IN PRIVATE KEY-----\nYour Private Key\n-----END PRIVATE KEY-----\n","client_email":"*******","client_id":"client-id","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/service-account%40project.iam.gserviceaccount.com"}

# Google Sheets document ID
# This is the ID from the spreadsheet URL: https://docs.google.com/spreadsheets/d/YOUR_DOCUMENT_ID/edit
GOOGLE_SHEETS_DOC_ID=your_spreadsheet_id

# Email configuration (optional, for notifications)
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=*******
EMAIL_PASSWORD=your_app_password
EMAIL_FROM=*******
