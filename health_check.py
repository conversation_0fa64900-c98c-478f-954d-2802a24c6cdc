#!/usr/bin/env python3
"""
EcoCycle - Health Check Module
Provides a simple health check endpoint for the application.
"""
import os
import json
import time
import logging
import sqlite3
from http.server import HTTPServer, BaseHTTPRequestHandler
from typing import Dict, Any, Tuple

# Import config module for paths
try:
    import config.config as config
    # Use config module for database file
    DATABASE_FILE = config.DATABASE_FILE
    LOG_DIR = config.LOG_DIR
    VERSION = config.VERSION
except ImportError:
    # Fallback if config module is not available
    DATABASE_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ecocycle.db')
    LOG_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Logs')
    VERSION = "0.1.0"

# Configure logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
handler = logging.FileHandler(os.path.join(LOG_DIR, 'health_check.log'))
handler.setLevel(logging.DEBUG)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)

# Constants
HOST = '0.0.0.0'
PORT = 5050
HEALTH_CHECK_PATH = '/health'


class HealthCheckHandler(BaseHTTPRequestHandler):
    """HTTP request handler for health check endpoint."""

    def do_GET(self):
        """Handle GET requests."""
        if self.path == HEALTH_CHECK_PATH:
            # Perform health check
            status, data = self.check_health()

            # Set response headers
            self.send_response(status)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()

            # Send response
            self.wfile.write(json.dumps(data).encode('utf-8'))
        else:
            # Return 404 for other paths
            self.send_response(404)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'error': 'Not found'}).encode('utf-8'))

    def check_health(self) -> Tuple[int, Dict[str, Any]]:
        """
        Check the health of the application.

        Returns:
            Tuple[int, Dict[str, Any]]: HTTP status code and health check data
        """
        health_data = {
            'status': 'ok',
            'timestamp': time.time(),
            'version': VERSION,
            'checks': {}
        }

        # Check database connection
        db_status, db_message = self.check_database()
        health_data['checks']['database'] = {
            'status': 'ok' if db_status else 'error',
            'message': db_message
        }

        # Check disk space
        disk_status, disk_message, disk_data = self.check_disk_space()
        health_data['checks']['disk'] = {
            'status': 'ok' if disk_status else 'warning',
            'message': disk_message,
            'data': disk_data
        }

        # Check memory usage
        mem_status, mem_message, mem_data = self.check_memory()
        health_data['checks']['memory'] = {
            'status': 'ok' if mem_status else 'warning',
            'message': mem_message,
            'data': mem_data
        }

        # Determine overall status
        if not db_status:
            health_data['status'] = 'error'
            return 500, health_data
        elif not disk_status or not mem_status:
            health_data['status'] = 'warning'
            return 200, health_data
        else:
            return 200, health_data

    def check_database(self) -> Tuple[bool, str]:
        """
        Check database connection.

        Returns:
            Tuple[bool, str]: Status and message
        """
        try:
            # Check if database file exists
            if not os.path.exists(DATABASE_FILE):
                return False, f"Database file not found: {DATABASE_FILE}"

            # Try to connect to the database
            conn = sqlite3.connect(DATABASE_FILE)
            cursor = conn.cursor()

            # Execute a simple query
            cursor.execute("SELECT sqlite_version()")
            version = cursor.fetchone()[0]

            # Close connection
            conn.close()

            return True, f"Connected to SQLite version {version}"
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False, f"Database connection failed: {str(e)}"

    def check_disk_space(self) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Check available disk space.

        Returns:
            Tuple[bool, str, Dict[str, Any]]: Status, message, and data
        """
        try:
            # Get disk usage statistics
            disk_usage = os.statvfs('/')
            free_bytes = disk_usage.f_frsize * disk_usage.f_bavail
            total_bytes = disk_usage.f_frsize * disk_usage.f_blocks
            used_bytes = total_bytes - free_bytes

            # Convert to MB for readability
            free_mb = free_bytes / (1024 * 1024)
            total_mb = total_bytes / (1024 * 1024)
            used_mb = used_bytes / (1024 * 1024)

            # Calculate percentage used
            percent_used = (used_bytes / total_bytes) * 100

            # Check if disk space is low (less than 10% free)
            is_low = percent_used > 90

            # Prepare data
            data = {
                'free_mb': round(free_mb, 2),
                'total_mb': round(total_mb, 2),
                'used_mb': round(used_mb, 2),
                'percent_used': round(percent_used, 2)
            }

            if is_low:
                return False, f"Low disk space: {round(100 - percent_used, 2)}% free", data
            else:
                return True, f"Disk space OK: {round(100 - percent_used, 2)}% free", data
        except Exception as e:
            logger.error(f"Disk space check failed: {e}")
            return False, f"Disk space check failed: {str(e)}", {}

    def check_memory(self) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Check memory usage.

        Returns:
            Tuple[bool, str, Dict[str, Any]]: Status, message, and data
        """
        try:
            # Try to import psutil - it should be installed as a dependency
            try:
                import psutil
            except ImportError:
                logger.warning("psutil module not found. Memory check will be skipped.")
                return True, "Memory check skipped (psutil not available)", {}

            # Get memory info
            memory = psutil.virtual_memory()

            # Convert to MB for readability
            total_mb = memory.total / (1024 * 1024)
            available_mb = memory.available / (1024 * 1024)
            used_mb = memory.used / (1024 * 1024)

            # Calculate percentage used
            percent_used = memory.percent

            # Check if memory is low (less than 10% free)
            is_low = percent_used > 90

            # Prepare data
            data = {
                'total_mb': round(total_mb, 2),
                'available_mb': round(available_mb, 2),
                'used_mb': round(used_mb, 2),
                'percent_used': round(percent_used, 2)
            }

            if is_low:
                return False, f"Low memory: {round(100 - percent_used, 2)}% free", data
            else:
                return True, f"Memory OK: {round(100 - percent_used, 2)}% free", data
        except Exception as e:
            logger.error(f"Memory check failed: {e}")
            return False, f"Memory check failed: {str(e)}", {}


def run_server(host: str = HOST, port: int = PORT, max_retries: int = 5):
    """
    Run the health check server.

    Args:
        host: Host to bind to
        port: Port to listen on
        max_retries: Maximum number of port retries if the port is in use
    """
    retries = 0
    current_port = port

    while retries < max_retries:
        try:
            server_address = (host, current_port)
            httpd = HTTPServer(server_address, HealthCheckHandler)
            logger.info(f"Starting health check server on {host}:{current_port}")
            print(f"Starting health check server on {host}:{current_port}")
            try:
                httpd.serve_forever()
            except KeyboardInterrupt:
                logger.info("Stopping health check server")
                print("Stopping health check server")
            httpd.server_close()
            break
        except OSError as e:
            if e.errno == 98:  # Address already in use
                logger.warning(f"Port {current_port} is already in use, trying {current_port + 1}")
                current_port += 1
                retries += 1
            else:
                logger.error(f"Error starting health check server: {e}")
                raise

    if retries >= max_retries:
        logger.error(f"Failed to start health check server after {max_retries} retries")
        print(f"Failed to start health check server after {max_retries} retries")
        raise RuntimeError(f"Failed to start health check server after {max_retries} retries")


if __name__ == '__main__':
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description='EcoCycle Health Check Server')
    parser.add_argument('--host', default=HOST, help=f'Host to bind to (default: {HOST})')
    parser.add_argument('--port', type=int, default=PORT, help=f'Port to listen on (default: {PORT})')
    args = parser.parse_args()

    # Run server
    run_server(args.host, args.port)
